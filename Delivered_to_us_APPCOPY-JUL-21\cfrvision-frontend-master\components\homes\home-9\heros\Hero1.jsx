"use client";
import Image from "next/image";
export default function Hero1() {
  return (
    <>
      {/* End Background Shape */}
      <div className="container position-relative min-height-100vh d-flex align-items-center pt-100 pb-100 pt-md-120 pb-md-120">
        {/* Home Section Content */}
        <div className="home-content text-start">
          <div className="row">
            {/* Home Section Text */}
            <div className="col-md-10 offset-md-1 col-lg-6 offset-lg-0 d-flex align-items-center mb-md-60 mb-sm-30">
              <div className="w-100 text-center text-lg-start">
                <h3 className="mb-40 mb-sm-20 wow fadeInUp">
                  Revolutionizing Public Sector Financial Reporting with AI
                </h3>
                <h1 className="hs-title-10 mb-40 mb-sm-20 wow fadeInUp">
                  Efficiency, Accuracy, and Transparency at Your Fingertips
                  <span className=""> with CFRVision</span>
                </h1>
                <div className="row">
                  <div className="col-lg-10">
                    <p
                      className="section-descr mb-50 mb-sm-40 wow fadeInUp text-secondary"
                      data-wow-delay="0.15s"
                    >
                      Public sector financial reporting can be complex and
                      resource-intensive. CFRVision simplifies the process,
                      saving you time, reducing costs, and ensuring compliance
                      with the latest standards. Discover the power of AI
                      tailored for Annual Comprehensive Financial Reports
                      (ACFRs).
                    </p>
                  </div>
                </div>
                <div
                  className="local-scroll wow fadeInUp wch-unset"
                  data-wow-delay="0.3s"
                  data-wow-offset={0}
                >
                  <a
                    href="#contact"
                    className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10"
                  >
                    <span>Get Started</span>
                  </a>
                  <a
                    href="#about"
                    className="link-hover-anim ms-2 ms-sm-5 me-2"
                    data-link-animate="y"
                  >
                    {/* <span className="link-strong link-strong-unhovered">
                      <i
                        className="icon-play size-14 me-2"
                        aria-hidden="true"
                      ></i>
                      See Demo
                    </span> */}
                    <span
                      className="link-strong link-strong-hovered"
                      aria-hidden="true"
                    >
                      <i
                        className="icon-play size-14 me-2"
                        aria-hidden="true"
                      ></i>
                      How it works?
                    </span>
                  </a>
                </div>
              </div>
            </div>
            {/* End Home Section Text */}
            {/* Images */}
            <div className="col-lg-6 d-flex align-items-center">
              <div
                className="w-100 ps-xl-3 wow fadeInLeft"
                data-wow-delay="0.15s"
              >
                <div className="composition-4">
                  {/* <div className="composition-4-decoration opacity-065">
                    <Image
                      src="/assets/images/demo-slick/decoration-1.svg"
                      alt=""
                      width={228}
                      height={228}
                    />
                  </div> */}
                  <div className="composition-4-image-1">
                    <div className="composition-4-image-1-inner">
                      <Image
                        src="/assets/b.png"
                        alt="Image Description"
                        width={850}
                        height={1000}
                      />
                    </div>
                  </div>
                  {/* <div className="composition-4-image-2">
                    <div className="composition-4-image-2-inner">
                      <Image
                        src="/assets/j.png"
                        alt="Image Description"
                        width={570}
                        height={598}
                      />
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
            {/* End Images */}
          </div>
        </div>
        {/* End Home Section Content */}
        {/* Scroll Down */}
        {/* <div
          className="local-scroll scroll-down-wrap-type-1 wow fadeInUp"
          data-wow-offset={0}
        >
          <div className="container text-center text-lg-start">
            <a href="#about" className="scroll-down-1">
              <div className="scroll-down-1-icon">
                <i className="mi-arrow-down" />
              </div>
              <div className="scroll-down-1-text">Scroll Down</div>
            </a>
          </div>
        </div> */}
        {/* End Scroll Down */}
      </div>
    </>
  );
}
