import asyncio
import os
import time
from contextlib import asynccontextmanager
from typing import Optional

from dotenv import load_dotenv
from fastapi import Depends, FastAPI, HTTPException, status, Request
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy.ext.asyncio import AsyncSession


from app import logger
from app.api.v1.ai_checks import ai_check_router
from app.api.v1.auth import auth_router
from app.api.v1.category import category_router
from app.api.v1.cms import cms_router
from app.api.v1.file import file_router
from app.api.v1.llm_check import llm_check_request_router
from app.api.v1.profile import profile_router
from app.api.v1.contact_us import contact_us_router
from app.database import get_db
from app.utils.constants import response_format
from app.utils.middlewares import EncryptionMiddleware


@asynccontextmanager
async def get_db_context():
    """
    Creates an asynchronous context manager for database sessions.

    This function wraps the asynchronous generator `get_db()` to provide a single database session
    for use within an async context. It iterates over the sessions provided by `get_db()` and yields
    the first available session. The try-finally block ensures that any necessary cleanup can be
    performed after the session is used, although the iteration is explicitly terminated after the first session.

    **Args**:
        None

    **Yields**:
        db (AsyncSession): An active database session for executing database operations.

    **Returns**:
        None: This function yields a database session and does not return a value.
    """
    async for db in get_db():
        try:
            yield db
        finally:
            break

def delete_llm_check_result_temp_files():
    try:
        folder_path = 'llm_check_result_files'
        if not os.path.isdir(folder_path):
            logger.info(f"Invalid folder path: {folder_path}")
            return
        current_time = time.time()
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)

            # Only process regular files (skip directories)
            if os.path.isfile(file_path):
                file_creation_time = os.path.getctime(file_path)
                if current_time - file_creation_time > 3600:
                    try:
                        os.remove(file_path)
                        logger.info(f"Deleted: {file_path}")
                    except Exception as e:
                        logger.info(f"Error deleting {file_path}: {e}")
                else:
                    logger.info(f"Not old enough: {file_path}")
    except Exception as e:
        logger.error(f"Error in scheduled task execution: {str(e)}")


async def schedule_task():
    """
    Continuously executes scheduled database retry operations in an infinite loop.

    This asynchronous function runs an infinite loop that periodically attempts to reprocess any previously failed
    database operations by obtaining a database session from the asynchronous context manager `get_db_context` and
    invoking the `retry_failed_operations` function with the acquired session. It logs a success message upon
    successful execution and an error message if an exception occurs. After each iteration, the function waits
    for 10 seconds before repeating the process.

    **Args**:
        None

    **Returns**:
        None: This function does not return any value; it runs indefinitely as a background task.
    """
    while True:
        try:
            from app.utils.decorators import retry_failed_operations
            delete_llm_check_result_temp_files()
            # async with get_db_context() as db:
            #     await retry_failed_operations(db)
            logger.info("Scheduled task executed successfully.")
        except Exception as e:
            logger.error(f"Error in scheduled task execution: {str(e)}")

        await asyncio.sleep(60)



@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manages the lifespan of the FastAPI application by running a background task.

    This asynchronous context manager starts a background task that continuously executes scheduled retry operations
    by invoking `schedule_task()`. The task is initiated when the FastAPI application starts and is gracefully cancelled
    upon shutdown. If the background task is cancelled during the shutdown process, a log message is recorded indicating
    that the task was successfully cancelled.

    **Args**:
        app (FastAPI): The FastAPI application instance.

    **Yields**:
        None: This context manager yields control back to the application while the background task runs.

    **Returns**:
        None: This function does not return any value; it simply manages the application lifecycle.
    """
    task = asyncio.create_task(schedule_task())
    yield
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        logger.info("Background task cancelled on shutdown.")


app = FastAPI(lifespan=lifespan, title="CFRVision",
              description="Revolutionizing Public Sector Financial Reporting with AI"
                          "Fast API, Postgresql as database.",
              debug=eval(os.getenv("DEBUG")))

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)
# app.mount("/temp_files", StaticFiles(directory="temp_files"), name="pdfs")

load_dotenv()
exception_queue = []
content_bkp = {}


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    error = exc.errors()
    param_list = [str(param) for param in error[0].get('loc', (''))[1:]]
    param = ', '.join(param_list).replace('_', ' ').title()

    error_message = error[0].get('msg') if error else ''
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"data": "",
                                  "error": error,
                                  "message": (param + ' ' + error_message.lower()) or
                                             "There is missing data or data is not sent in expected format!",
                                  "status": False}),
    )


@app.get("/")
async def project_running():
    return {"message": "Project backend running successfully!"}



app.include_router(ai_check_router, prefix="/v1/cms/ai_checks", tags=["cms"], responses=response_format)
app.include_router(category_router, prefix="/v1/cms/category", tags=["cms"], responses=response_format)
app.include_router(llm_check_request_router, prefix="/v1/llm_check_request", tags=["llm_check_request"],
                   responses=response_format)
app.include_router(file_router, prefix="/v1/file", tags=["file"], responses=response_format)
app.include_router(cms_router, prefix="/v1/cms", tags=["cms"], responses=response_format)
app.include_router(profile_router, prefix="/v1/profile", tags=["profile"], responses=response_format)
app.include_router(auth_router, prefix="/v1/auth", tags=["auth"], responses=response_format)
app.include_router(contact_us_router, prefix="/v1/contact_us", tags=["contact_us"], responses=response_format)


@app.post("/chargebee-webhook")
async def chargebee_webhook(
        payload: Optional[dict] = None,
        db: AsyncSession = Depends(get_db),
):
    """
    Handles incoming Chargebee webhook events.

    This asynchronous endpoint processes webhook payloads from Chargebee. It first validates that a payload is provided, then attempts to reprocess any previously failed operations using a retry mechanism. Next, it extracts the event type and content from the payload and delegates the processing to the Chargebee webhook handler, which updates the database accordingly. In case of any errors during processing, the function logs the error and raises an HTTPException with a 500 status code.

    **Args**:
        payload (Optional[dict]): The webhook payload containing event details including the event type and content.
        db (AsyncSession): The database session used to query and store data.

    **Returns**:
        dict: A JSON response indicating that the webhook was processed successfully (e.g., {"message": "Webhook proceed"}).
    """

    if not payload:
        raise HTTPException(status_code=400, detail="Missing payload")
    print(f"Received Chargebee webhook payload:{payload}")
    print(payload.get("event_type"))
    try:
        from app.utils.decorators import retry_failed_operations
        await retry_failed_operations()

        from app.operations.chargebee_operations.chargebee_operations import HandleWebhook
        print(payload.get("content", {}))
        await HandleWebhook().parse_chargebee_request(event_type=payload.get("event_type"), content=payload.get("content", {}), db=db)

    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

    return {"message": "Webhook proceed"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host='127.0.0.1', port=3001)
