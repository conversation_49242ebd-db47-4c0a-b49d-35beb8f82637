## Docker file configuration for running this project

### Use the official slim Python 3.12 image as a base

FROM python:3.12-slim

### Set the working directory

WORKDIR /app

### Copy the current directory contents into the container at /app

COPY . .

### Install Python dependencies

RUN pip install --no-cache-dir -r requirements.txt

### Expose port 8000 to the outside world

EXPOSE 8000

### Run the command to start the FastAPI application using uvicorn

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

# cfrvision__API
