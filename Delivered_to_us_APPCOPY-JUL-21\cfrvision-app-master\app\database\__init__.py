import sys
from os.path import dirname, abspath

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.ext.declarative import declarative_base

from app import logger

sys.path.insert(0, abspath(dirname(dirname(__file__))))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker
from app.database.models import Base  # Make sure you import your Base model here
import os
from dotenv import load_dotenv

load_dotenv()

# Postgres database setup used within the whole project
database_user = os.getenv("DATABASE_USER")
database_password = os.getenv("DATABASE_PASSWORD")
database_host = os.getenv("DATABASE_HOST")
database_name = os.getenv("DATABASE_NAME")
DATABASE_URL = f"postgresql+asyncpg://{database_user}:{database_password}@{database_host}/{database_name}"
# print(f"DB URL::::{DATABASE_URL}")
logger.exception(f"DB URL: {DATABASE_URL}", exc_info=True)
# Create an asynchronous engine and session
engine = create_async_engine(DATABASE_URL, echo=False)
async_session = sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)


# Dependency to get the database session
async def get_db():
    async with async_session() as session:
        yield session


# Define SQLAlchemy ORM model
Base = declarative_base()
