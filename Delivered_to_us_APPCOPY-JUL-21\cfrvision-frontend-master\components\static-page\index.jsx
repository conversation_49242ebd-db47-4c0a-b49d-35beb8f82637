"use client";
import AnimatedText from "@/components/common/AnimatedText";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { useParams, useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const StaticPage = () => {
  const params = useParams();
  const [data, setData] = useState();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const response = await axiosInstance.post("/v1/cms/static-pages", {
        title: params.slug || "",
      });
      setData(response?.data?.data?.content);
    } catch (error) {
      console.log("error", error);
    }
  };

  return (
    <>
      <main id="main">
        <ParallaxContainer className="page-section bg-gray-light-1 parallax-5">
          <section className="container">
            <div dangerouslySetInnerHTML={{ __html: data }} />
          </section>
        </ParallaxContainer>
      </main>
    </>
  );
};

export default StaticPage;
