"""
Enhanced classifier that combines rule-based classification with Google LLM intelligence.
"""
import logging
from typing import List, Dict, Set
from datetime import date, timedelta
from pathlib import Path

from models import FileInfo, Task, TaskType, Priority, Config
from classifier import FileClassifier, RulesEngine
from google_llm_classifier import GoogleGeminiClassifier, GoogleVisionOCR

logger = logging.getLogger(__name__)


class GoogleEnhancedClassifier:
    """Combines rule-based classification with Google Gemini intelligence."""
    
    def __init__(self, config: Config, gemini_api_key: str = None, 
                 google_credentials_path: str = None, enable_vision: bool = True):
        self.config = config
        
        # Initialize rule-based classifier
        self.rule_classifier = FileClassifier(config)
        
        # Initialize Google services
        self.gemini_classifier = GoogleGeminiClassifier(api_key=gemini_api_key)
        
        if enable_vision:
            self.vision_ocr = GoogleVisionOCR(credentials_path=google_credentials_path)
        else:
            self.vision_ocr = None
        
        self.enable_vision = enable_vision
    
    def classify_file(self, file_info: FileInfo, root_path: str = "") -> FileInfo:
        """Enhanced classification using both rules and Google Gemini."""
        
        # Start with rule-based classification
        file_info = self.rule_classifier.classify_file(file_info)
        
        # Enhance with Google Vision for images/PDFs if available
        if self.enable_vision and self.vision_ocr and file_info.extension in ['.jpg', '.jpeg', '.png', '.pdf']:
            file_info = self._enhance_with_vision(file_info, root_path)
        
        # Enhance with Gemini LLM if we have content
        if self.gemini_classifier.model and file_info.extracted_text.strip():
            file_info = self._enhance_with_gemini(file_info)
        
        return file_info
    
    def _enhance_with_vision(self, file_info: FileInfo, root_path: str) -> FileInfo:
        """Enhance file info using Google Vision OCR and analysis."""
        try:
            file_path = Path(root_path) / file_info.path
            
            if file_info.extension == '.pdf':
                # Use Vision OCR for PDF
                text, confidence = self.vision_ocr.extract_text_from_pdf(file_path)
            else:
                # Use Vision OCR for images
                text, confidence = self.vision_ocr.extract_text_from_image(file_path)
            
            if confidence > 0.6 and text.strip():
                # Update extracted text with higher quality OCR
                file_info.extracted_text = text[:2000]  # Limit to 2000 chars
                file_info.confidence = max(file_info.confidence, confidence)
                
                # Use Gemini's vision capabilities for deeper analysis
                if self.gemini_classifier.model:
                    vision_analysis = self.gemini_classifier.analyze_document_with_vision(file_info, file_path)
                    
                    if vision_analysis.get("confidence", 0) > 0.7:
                        # Merge topics from vision analysis
                        vision_topics = set(vision_analysis.get("topics", []))
                        existing_topics = set(file_info.topics)
                        file_info.topics = list(existing_topics.union(vision_topics))
                        
                        # Update description with insights
                        if vision_analysis.get("insights"):
                            file_info.description = vision_analysis["insights"][:120]
                        
                        # Update confidence
                        file_info.confidence = max(file_info.confidence, vision_analysis["confidence"])
                
                logger.debug(f"Enhanced {file_info.path} with Google Vision (confidence: {confidence:.2f})")
        
        except Exception as e:
            logger.warning(f"Google Vision enhancement failed for {file_info.path}: {e}")
        
        return file_info
    
    def _enhance_with_gemini(self, file_info: FileInfo) -> FileInfo:
        """Enhance file info using Google Gemini classification."""
        try:
            gemini_result = self.gemini_classifier.classify_document(file_info)
            
            if gemini_result.get("confidence", 0) > 0.6:
                # Merge topics
                gemini_topics = set(gemini_result.get("topics", []))
                existing_topics = set(file_info.topics)
                file_info.topics = list(existing_topics.union(gemini_topics))
                
                # Update description with Gemini insights
                if gemini_result.get("insights"):
                    file_info.description = gemini_result["insights"][:120]
                
                # Update confidence (take the higher value)
                file_info.confidence = max(file_info.confidence, gemini_result["confidence"])
                
                logger.debug(f"Enhanced {file_info.path} with Gemini (confidence: {gemini_result['confidence']:.2f})")
        
        except Exception as e:
            logger.warning(f"Gemini enhancement failed for {file_info.path}: {e}")
        
        return file_info
    
    def extract_enhanced_tasks(self, files: List[FileInfo]) -> List[Task]:
        """Extract tasks using Google Gemini intelligence."""
        if not self.gemini_classifier.model:
            return []
        
        tasks = []
        
        for file_info in files:
            if not file_info.extracted_text.strip():
                continue
            
            try:
                gemini_tasks = self.gemini_classifier.extract_tasks(file_info)
                
                for task_data in gemini_tasks:
                    confidence = task_data.get("confidence", 0.7)
                    
                    if confidence > 0.6:  # Only high-confidence tasks
                        # Map priority
                        priority_map = {"high": Priority.HIGH, "medium": Priority.MEDIUM, "low": Priority.LOW}
                        priority = priority_map.get(task_data.get("priority", "medium"), Priority.MEDIUM)
                        
                        # Map task type
                        type_map = {"process": TaskType.PROCESS, "opportunity": TaskType.OPPORTUNITY, "oneoff": TaskType.ONEOFF}
                        task_type = type_map.get(task_data.get("type", "oneoff"), TaskType.ONEOFF)
                        
                        # Calculate due date
                        due_date = None
                        if task_data.get("due_in_days"):
                            due_date = date.today() + timedelta(days=task_data["due_in_days"])
                        
                        # Create task
                        task = Task(
                            id=Task.generate_id(task_data["title"], [file_info.path]),
                            title=task_data["title"],
                            type=task_type,
                            priority=priority,
                            due=due_date,
                            source=[file_info.path],
                            notes=task_data.get("description", ""),
                            confidence=confidence,
                            tags=task_data.get("tags", [])
                        )
                        
                        tasks.append(task)
                        logger.debug(f"Extracted task from {file_info.path}: {task.title}")
            
            except Exception as e:
                logger.warning(f"Task extraction failed for {file_info.path}: {e}")
        
        return tasks


class GoogleEnhancedRulesEngine(RulesEngine):
    """Enhanced rules engine that uses Google LLM for smarter rule application."""
    
    def __init__(self, config: Config, gemini_classifier: GoogleGeminiClassifier = None):
        super().__init__(config)
        self.gemini_classifier = gemini_classifier
    
    def generate_smart_process_candidates(self, files: List[FileInfo]) -> List:
        """Generate process candidates with Google LLM enhancement."""
        # Start with rule-based candidates
        candidates = self.generate_process_candidates(files)
        
        # Enhance with Gemini insights if available
        if self.gemini_classifier and self.gemini_classifier.model:
            candidates = self._enhance_candidates_with_gemini(candidates, files)
        
        return candidates
    
    def _enhance_candidates_with_gemini(self, candidates, files):
        """Enhance process candidates using Gemini analysis."""
        enhanced_candidates = []
        
        for candidate in candidates:
            try:
                # Get files related to this candidate
                related_files = [f for f in files if f.path in candidate.evidence_paths]
                
                if related_files:
                    # Analyze the business process with Gemini
                    analysis_prompt = f"""
                    Analyze this business process and provide recommendations:
                    
                    Process: {candidate.name}
                    Frequency: {candidate.cadence}
                    Related files: {[f.path for f in related_files[:3]]}
                    
                    Based on the files and process type, suggest:
                    1. Optimal frequency for this process
                    2. Priority level (high/medium/low)
                    3. Any additional steps or considerations
                    4. Business impact if this process is delayed
                    
                    Respond in JSON format:
                    {{
                      "recommended_frequency": "DAILY|WEEKLY|BIWEEKLY|MONTHLY",
                      "priority": "high|medium|low",
                      "confidence": 0.85,
                      "additional_steps": ["step1", "step2"],
                      "business_impact": "description of impact"
                    }}
                    """
                    
                    response = self.gemini_classifier.model.generate_content(analysis_prompt)
                    # Parse and apply enhancements
                    # (Implementation would parse JSON and update candidate)
                
                enhanced_candidates.append(candidate)
                
            except Exception as e:
                logger.warning(f"Failed to enhance candidate {candidate.name}: {e}")
                enhanced_candidates.append(candidate)  # Keep original
        
        return enhanced_candidates
