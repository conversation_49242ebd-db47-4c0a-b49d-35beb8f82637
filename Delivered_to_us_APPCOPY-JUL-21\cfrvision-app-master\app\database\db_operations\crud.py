import time

from sqlalchemy.ext.asyncio import AsyncSession

from app import logger
from app.utils.exception_handling import ExceptionHandling


class Dbcrud:

    def __init__(self):
        pass

    async def update(self, existing_data, update_data: dict, db: AsyncSession):
        """ Updates an existing record with new data in the database.

        This asynchronous method iterates over the provided update data and updates the corresponding attributes of the existing record, ensuring that only non-null values that are not dictionaries or lists are modified. It also updates the `updated_at` timestamp to reflect the modification time before committing the changes to the database. If an exception occurs, it logs detailed information about the input data and the error, handles the exception using a dedicated exception handler, and then re-raises the exception.

        **Args**:
            existing_data: The existing database record object that needs to be updated.
            update_data (dict): A dictionary containing the updated values for the record. Only non-null scalar values are considered for updating.
            db (AsyncSession): The asynchronous database session used to perform the commit operation.

        **Returns**:
            bool: Returns `True` upon successful update.

        **Raises**:
            Exception: If an error occurs during the update process, the exception is logged, handled, and re-raised.
        """

        try:
            for key, value in update_data.items():
                if hasattr(existing_data, key) and value is not None and not isinstance(value,dict) and not isinstance(value,list):
                    setattr(existing_data, key, value)

            setattr(existing_data, 'updated_at', int(time.time()))

            await db.commit()
            return True
        except Exception as e:
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="create_customer").exception_handling()
            raise
