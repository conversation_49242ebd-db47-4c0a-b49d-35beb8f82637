import React from "react";

import Image from "next/image";
import Contact from "./Contact";
import Link from "next/link";
import { features4 } from "@/data/features";

export default function Home9({  dark = false }) {
  return (
    <>
      <hr className={`mt-0 mb-0 ${dark ? "white" : ""} `} />
      <section
        className={`page-section  scrollSpysection  ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="contact"
      >
        <Contact />
      </section>
    </>
  );
}
