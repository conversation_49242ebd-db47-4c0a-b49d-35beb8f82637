import React, { useEffect } from "react";
import { <PERSON><PERSON>, Col, Modal, Row } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import { valibotResolver } from "@hookform/resolvers/valibot";

const schema = v.object({
  category_name: v.pipe(v.string(), v.nonEmpty("Category name is required")),
  category_description: v.pipe(
    v.string(),
    v.minLength(1, "Category description cannot be empty"),
    v.nonEmpty("Category description are required")
  ),
  category_order: v.pipe(
    v.string(), // Treat as string because that's what the input field gives
    v.nonEmpty("Category order is required"),
    v.transform((value) => Number(value)), // Convert to number
    v.number("Category order must be a valid number"), // Ensure it's a number
    v.minValue(1, "Category order must be greater than 0") // Ensure it's greater than 0
  ),
  is_active: v.boolean(), // Adds validation for is_active (checkbox)
});

const AddEditCategoryModal = ({
  show,
  setShow,
  categoryData,
  onSave,
  isView,
}) => {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      category_name: "",
      category_description: "",
      category_order: "",
      is_active: true, // Default value for is_active
    },
  });
  // Effect to set the form data when in edit mode
  useEffect(() => {
    if (categoryData && show) {
      setValue("category_name", categoryData.category_name);
      setValue("category_description", categoryData.category_description);
      setValue(
        "category_order",
        (categoryData.category_order || "").toString()
      );
      setValue("is_active", categoryData.is_active); // Set the actual value of is_active
    }
  }, [categoryData, show, setValue]);

  const onSubmit = async (data) => {
    try {
      if (categoryData) {
        await onSave({ ...data, category_id: categoryData.category_id }); // Pass the ID for update
      } else {
        await onSave(data);
      }
      setShow(false); // Close the modal after saving
    } catch (error) {
      console.log("Error saving category:", error);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  return (
    <Modal show={show} onHide={() => setShow(false)} centered size="lg">
      <form
        noValidate
        autoComplete="off"
        onSubmit={handleSubmit(onSubmit)}
        className="form contact-form"
      >
        <Modal.Header closeButton>
          <Modal.Title className="text-center">
            {categoryData
              ? isView
                ? "AI Check Category Detail"
                : "Edit AI Check Category"
              : "Add New AI Check Category"}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Row>
            <Col md={12}>
              <div className="form-group">
                <label htmlFor="category_name">Category Name</label>
                <Controller
                  name="category_name"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      name="category_name"
                      id="category_name"
                      className="input-md round form-control"
                      placeholder="Enter category name"
                      required
                      disabled={isView}
                    />
                  )}
                />
                {errors?.category_name && (
                  <span className="text-red">
                    {errors?.category_name.message}
                  </span>
                )}
              </div>
            </Col>
            <Col md={12}>
              <div className="form-group">
                <label htmlFor="category_description">
                  Category Description
                </label>
                <Controller
                  name="category_description"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      name="category_description"
                      id="category_description"
                      className="input-md round form-control"
                      placeholder="Enter category description"
                      required
                      disabled={isView}
                    />
                  )}
                />
                {errors?.category_description && (
                  <span className="text-red">
                    {errors?.category_description.message}
                  </span>
                )}
              </div>
            </Col>

            <Col md={12}>
              <div className="form-group">
                <label htmlFor="category_order">Category Order</label>
                <Controller
                  name="category_order"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      name="category_order"
                      id="category_order"
                      className="input-md round form-control"
                      placeholder="Enter category order"
                      required
                      disabled={isView}
                    />
                  )}
                />
                {errors?.category_order && (
                  <span className="text-red">
                    {errors?.category_order.message}
                  </span>
                )}
              </div>
            </Col>

            {/* Active checkbox that allows user to select active or inactive */}
            <Col md={12}>
              <div className="form-group">
                <Controller
                  name="is_active"
                  control={control}
                  render={({ field }) => (
                    <label>
                      <input
                        type="checkbox"
                        {...field}
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        disabled={isView}
                      />
                      <span style={{ marginLeft: "5px" }}>Active</span>
                    </label>
                  )}
                />
                {errors.is_active && (
                  <div className="text-red mt-2">
                    {errors.is_active.message}
                  </div>
                )}
              </div>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShow(false)}>
            Cancel
          </Button>
          {!isView && (
            <Button type="submit" variant="primary" disabled={isView}>
              {categoryData ? "Update" : "Save"}
            </Button>
          )}
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default AddEditCategoryModal;
