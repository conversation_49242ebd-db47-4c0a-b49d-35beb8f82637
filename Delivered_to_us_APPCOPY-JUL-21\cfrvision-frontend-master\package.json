{"name": "cfrvision", "version": "1.4.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@hookform/resolvers": "^3.10.0", "@iconify/react": "^5.2.0", "@popperjs/core": "2.11.8", "ag-grid-community": "^33.0.4", "ag-grid-enterprise": "^33.0.4", "ag-grid-react": "^33.0.4", "antd": "^5.23.4", "axios": "^1.7.9", "bootstrap": "^5.1.3", "imagesloaded": "^5.0.0", "isotope-layout": "^3.0.6", "jarallax": "^2.2.1", "moment": "^2.30.1", "next": "14.2.14", "photoswipe": "^5.4.4", "react": "^18", "react-bootstrap": "^2.10.9", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-modal-video": "^2.0.2", "react-photoswipe-gallery": "^3.0.1", "react-quill": "^2.0.0", "rellax": "^1.12.1", "swiper": "^11.1.4", "tippy.js": "^6.3.7", "typewriter-effect": "^2.21.0", "valibot": "^1.0.0-beta.14", "wowjs": "^1.1.3"}, "devDependencies": {"@types/node": "22.10.10", "@types/react": "19.0.8", "typescript": "5.7.3"}}