timezone: "America/New_York"

cadences:
  expenses:
    match: ["receipt", "receipts", "invoice", "chargebee", "stripe", "quickbooks"]
    recurrence: "DAILY"
    rule: "Enter receipts in QuickBooks"
  grants:
    match_folders: ["Possible_Grants_For_Us", "Possible_Grants_For _Us"]
    recurrence: "BIWEEKLY"
    rule: "Grant follow-up"
    followup_after_days: 14
  deliverables:
    match_folders: ["Delivered_to_us_*"]
    recurrence: null
    due_after_days: 5
    rule: "Test deliverable and send feedback"
  pricing:
    match_folders: ["Pricing_tiers_to_Customers"]
    recurrence: "MONTHLY"
    rule: "Review/update pricing deck"
  compliance:
    match_folders: ["Required_Compliance and Security"]
    recurrence: "MONTHLY"
    rule: "Check compliance actions"
  aws:
    match_folders: ["AWS"]
    match: ["aws", "access", "key", "user"]
    recurrence: "MONTHLY"
    rule: "Review AWS access and security"
  openxcell:
    match_folders: ["Openxcell"]
    recurrence: "WEEKLY"
    rule: "Review development progress and invoices"

opportunity_hints:
  - "branding"
  - "linkedin"
  - "Openxcell"
  - "AWS"
  - "github"
  - "architecture"
  - "proposal"
  - "design"
  - "consultation"

auto_complete_patterns:
  - "submitted"
  - "paid"
  - "approved"
  - "sent"
  - "accomplished"
  - "completed"
  - "done"

ignore_patterns:
  - "node_modules"
  - ".git"
  - "__pycache__"
  - "*.pyc"
  - ".DS_Store"
  - "Thumbs.db"
