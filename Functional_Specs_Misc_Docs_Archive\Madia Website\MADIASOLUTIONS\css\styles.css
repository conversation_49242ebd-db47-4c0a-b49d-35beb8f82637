body {
    font-family: '<PERSON><PERSON>', sans-serif; /* Updated font family */
    background-color: #ffffff; /* Set a white background color for clarity */
header {
    background: linear-gradient(120deg, #00509e, #f0f0f0); /* More pronounced gradient background for header */
    color: white;
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
}
    background-size: cover; /* Ensures the image covers the entire background */
    background-position: center; /* Centers the background image */
}

header {
    background-color: #003366; /* Dark blue */
    color: white;
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
}

nav {
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav ul {
    display: flex;
    gap: 30px; /* Increased gap for better spacing */
    margin: 0;
    padding: 0;
    background-color: #003366; /* Dark blue background for nav */
    list-style: none;
    text-align: center;
}

nav ul li {
    margin: 0;
}

nav ul li a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s; /* Smooth transition for hover effect */
}

nav ul li:hover {
    background-color: rgba(255, 255, 255, 0.2); /* Light hover effect */
}

main {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Responsive grid layout */
    gap: 20px;
    padding: 20px;
}

.hero {
    background-color: #ffffff; /* Set a solid background color for hero section */
    padding: 40px; /* Increased padding for hero section */
    text-align: center;
}

h1, h2 {
    color: #003366; /* Dark blue */
}

footer {
    text-align: center;
    padding: 20px 0; /* Increased padding for footer */
    background-color: #003366; /* Dark blue */
    color: #003366; /* Dark blue for better contrast */
    position: relative;
    bottom: 0;
    width: 100%;
}

.slider {
    position: relative;
    overflow: hidden;
    height: 400px; /* Set a height for the slider */
}

.slide {
    display: none; /* Hide all slides by default */
    height: 100%; /* Ensure slides take full height */
    background-size: cover; /* Cover the entire area */
    background-position: center; /* Center the background image */
}

.slider-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
}

.slider-controls button {
    background-color: #00509e; /* Brighter blue for buttons */
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.slider-controls button:hover {
    background-color: rgba(255, 255, 255, 0.5); /* Lighter hover effect for better visibility */
}
@media (max-width: 768px) {
    nav ul li {
        display: block;
        margin: 10px 0;
    }

    form {
        width: 90%;
        margin: auto;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.5em;
    }

    h2 {
        font-size: 1.2em;
    }
}
