import TableWithPagination from "@/components/common/TableWithPagination/page";
import axiosInstance from "@/utlis/axios";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";

const Cards = ({}) => {
  const [loading, setLoading] = useState(false);
  const [cardList, setCardList] = useState([]);
  const fetchCardData = async () => {
    setLoading(true);
    try {
      const payload = {
        filters: [
          {
            filter_column: "",
            filter_text: "",
          },
          {
            filter_column: "",
            filter_text: "",
          },
        ],
        id: 1,
        page_no: 1,
        page_size: 10,
        is_pagination: true,
        search_text: "",
      };
      const response = await axiosInstance.post(
        "/v1/profile/cards/get",
        payload
      );
      setCardList(response?.data?.data);
      setLoading(false);
    } catch (error) {
      console.log("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCardData();
  }, []);

  const handleSearchChange = (value) => {
    fetchCardData();
  };

  return (
    <>
      <div className="card p-4 box-shadow">
        <div className="d-flex align-items-center justify-content-between ">
          <h5 className="text-center mb-0">Cards</h5>
          <button
            className="btn btn-mod btn-color btn-small btn-circle btn-hover-anim mb-xs-10"
            id="upload-button"
            onClick={() => {}}
          >
            <span>Add Card</span>
          </button>
        </div>
        {cardList?.map((item) => (
          <div className="card my-4 p-4">
            <div className="d-flex justify-content-between">
              <div>
                <p className="mb-0">{item?.brand}</p>
                <p className="mb-0">{item?.masked_number}</p>
                <p className="mb-0">Type : {item?.funding_type}</p>
              </div>
              <div>
                <div
                  style={{
                    display: "flex",
                    gap: "8px",
                    marginTop: "15px",
                    marginBottom: "10px",
                    justifyContent: "end",
                  }}
                >
                  <Icon
                    icon="basil:edit-outline"
                    width="24"
                    height="24"
                    cursor={"pointer"}
                    onClick={() => {}}
                  />
                  <Icon
                    icon="mdi:trash-outline"
                    width="24"
                    height="24"
                    color="red"
                    cursor={"pointer"}
                    onClick={() => {}}
                  />
                </div>
                <p className="mb-0">
                  Card Exiprt at: {item?.expiry_month}/{item?.expiry_year}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default Cards;
