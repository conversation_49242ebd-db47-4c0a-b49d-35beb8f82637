"""
Google LLM integration for enhanced document understanding and OCR.
Supports Google Gemini API and Google Cloud Vision API.
"""
import json
import logging
import base64
from typing import List, Dict, Optional, Set, Tuple
from datetime import date, timedelta
import re
from pathlib import Path

from models import FileInfo, Task, TaskType, Priority

logger = logging.getLogger(__name__)

# Optional Google imports - gracefully handle missing dependencies
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

try:
    from google.cloud import vision
    VISION_AVAILABLE = True
except ImportError:
    VISION_AVAILABLE = False

try:
    from google.oauth2 import service_account
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False


class GoogleVisionOCR:
    """Google Cloud Vision API for advanced OCR."""
    
    def __init__(self, credentials_path: Optional[str] = None, api_key: Optional[str] = None):
        self.client = None
        
        if not VISION_AVAILABLE:
            logger.warning("Google Cloud Vision not available. Install: pip install google-cloud-vision")
            return
        
        try:
            if credentials_path:
                credentials = service_account.Credentials.from_service_account_file(credentials_path)
                self.client = vision.ImageAnnotatorClient(credentials=credentials)
            else:
                # Uses default credentials or GOOGLE_APPLICATION_CREDENTIALS env var
                self.client = vision.ImageAnnotatorClient()
            logger.info("Google Cloud Vision OCR initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Google Cloud Vision: {e}")
    
    def extract_text_from_image(self, image_path: Path) -> Tuple[str, float]:
        """Extract text from image using Google Cloud Vision OCR."""
        if not self.client:
            return "", 0.0
        
        try:
            with open(image_path, 'rb') as image_file:
                content = image_file.read()
            
            image = vision.Image(content=content)
            response = self.client.text_detection(image=image)
            
            if response.error.message:
                logger.error(f"Vision API error: {response.error.message}")
                return "", 0.0
            
            texts = response.text_annotations
            if not texts:
                return "", 0.0
            
            # First annotation contains the full text
            full_text = texts[0].description
            
            # Calculate confidence from individual word confidences
            confidence = self._calculate_confidence(response)
            
            return full_text, confidence
            
        except Exception as e:
            logger.warning(f"Google Vision OCR failed for {image_path}: {e}")
            return "", 0.0
    
    def extract_text_from_pdf(self, pdf_path: Path) -> Tuple[str, float]:
        """Extract text from PDF using Google Cloud Vision OCR."""
        if not self.client:
            return "", 0.0
        
        try:
            with open(pdf_path, 'rb') as pdf_file:
                content = pdf_file.read()
            
            # For PDFs, we use document_text_detection
            image = vision.Image(content=content)
            response = self.client.document_text_detection(image=image)
            
            if response.error.message:
                logger.error(f"Vision API error: {response.error.message}")
                return "", 0.0
            
            if response.full_text_annotation:
                text = response.full_text_annotation.text
                confidence = self._calculate_document_confidence(response)
                return text, confidence
            
            return "", 0.0
            
        except Exception as e:
            logger.warning(f"Google Vision PDF OCR failed for {pdf_path}: {e}")
            return "", 0.0
    
    def _calculate_confidence(self, response) -> float:
        """Calculate average confidence from OCR response."""
        if not response.text_annotations:
            return 0.0
        
        # Skip the first annotation (full text) and calculate from words
        word_annotations = response.text_annotations[1:]
        if not word_annotations:
            return 0.8  # Default confidence if no word-level data
        
        confidences = []
        for annotation in word_annotations:
            if hasattr(annotation, 'confidence'):
                confidences.append(annotation.confidence)
        
        return sum(confidences) / len(confidences) if confidences else 0.8
    
    def _calculate_document_confidence(self, response) -> float:
        """Calculate confidence for document text detection."""
        if not response.full_text_annotation:
            return 0.0
        
        # Use page-level confidence if available
        pages = response.full_text_annotation.pages
        if pages:
            confidences = [page.confidence for page in pages if hasattr(page, 'confidence')]
            return sum(confidences) / len(confidences) if confidences else 0.8
        
        return 0.8


class GoogleGeminiClassifier:
    """Google Gemini API for intelligent document classification and task extraction."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gemini-1.5-flash"):
        self.model_name = model
        self.model = None
        
        if not GEMINI_AVAILABLE:
            logger.warning("Google Gemini not available. Install: pip install google-generativeai")
            return
        
        try:
            if api_key:
                genai.configure(api_key=api_key)
            # Otherwise uses GOOGLE_API_KEY env var
            
            self.model = genai.GenerativeModel(model)
            logger.info(f"Google Gemini initialized with model: {model}")
        except Exception as e:
            logger.warning(f"Failed to initialize Google Gemini: {e}")
    
    def classify_document(self, file_info: FileInfo) -> Dict:
        """Classify document using Gemini."""
        if not self.model:
            return {"topics": [], "confidence": 0.0, "insights": "Gemini not available"}
        
        try:
            prompt = self._build_classification_prompt(file_info)
            response = self.model.generate_content(prompt)
            return self._parse_classification_response(response.text)
        except Exception as e:
            logger.warning(f"Gemini classification failed for {file_info.path}: {e}")
            return {"topics": [], "confidence": 0.0, "insights": f"Error: {str(e)[:100]}"}
    
    def extract_tasks(self, file_info: FileInfo) -> List[Dict]:
        """Extract tasks using Gemini."""
        if not self.model:
            return []
        
        try:
            prompt = self._build_task_extraction_prompt(file_info)
            response = self.model.generate_content(prompt)
            return self._parse_task_response(response.text)
        except Exception as e:
            logger.warning(f"Gemini task extraction failed for {file_info.path}: {e}")
            return []
    
    def analyze_document_with_vision(self, file_info: FileInfo, image_path: Path) -> Dict:
        """Analyze document using Gemini's vision capabilities."""
        if not self.model:
            return {"topics": [], "confidence": 0.0, "insights": "Gemini not available"}
        
        try:
            # Read image
            with open(image_path, 'rb') as img_file:
                image_data = img_file.read()
            
            # Create image part for Gemini
            image_part = {
                "mime_type": "image/jpeg" if image_path.suffix.lower() in ['.jpg', '.jpeg'] else "image/png",
                "data": base64.b64encode(image_data).decode()
            }
            
            prompt = f"""
            Analyze this business document image and provide insights.
            
            File: {file_info.path}
            Folder: {file_info.folder}
            
            Please:
            1. Extract and understand the text content
            2. Classify the document type and business category
            3. Identify any actionable items or follow-ups needed
            4. Assess the business importance and urgency
            
            Respond in JSON format:
            {{
              "extracted_text": "Full text content from the image",
              "topics": ["category1", "category2"],
              "confidence": 0.85,
              "insights": "What this document is about and why it matters",
              "priority": "high|medium|low",
              "actionable_items": ["task1", "task2"],
              "document_type": "receipt|invoice|contract|report|etc"
            }}
            """
            
            response = self.model.generate_content([prompt, image_part])
            return self._parse_vision_response(response.text)
            
        except Exception as e:
            logger.warning(f"Gemini vision analysis failed for {file_info.path}: {e}")
            return {"topics": [], "confidence": 0.0, "insights": f"Vision error: {str(e)[:100]}"}
    
    def _build_classification_prompt(self, file_info: FileInfo) -> str:
        """Build classification prompt for Gemini."""
        return f"""
        You are an expert business analyst. Analyze this startup business document and provide detailed insights.
        
        File: {file_info.path}
        Folder: {file_info.folder}
        File Type: {file_info.extension}
        Content: {file_info.extracted_text[:2000]}
        
        Based on the file path, folder structure, and content, classify this document into relevant business categories:
        
        Categories to consider:
        - expenses (receipts, invoices, bills, payments, financial transactions)
        - grants (funding opportunities, applications, proposals, government funding)
        - deliverables (completed work, milestones, submissions, project outputs)
        - branding (logos, marketing materials, design assets, brand guidelines)
        - compliance (legal documents, security requirements, regulations, audits)
        - pricing (cost structures, subscription plans, pricing tiers, revenue models)
        - aws (cloud infrastructure, amazon services, hosting, technical infrastructure)
        - development (code, software, programming, applications, technical specs)
        - business (strategy, planning, business models, revenue, operations)
        - legal (contracts, agreements, incorporation, terms, legal documents)
        - finance (accounting, banking, taxes, financial planning, budgets)
        - opportunities (potential improvements, follow-ups, business development)
        - partnerships (vendor relationships, collaborations, third-party services)
        
        Provide a comprehensive analysis in JSON format:
        {{
          "topics": ["most relevant categories"],
          "confidence": 0.85,
          "insights": "Detailed explanation of what this document contains and its business significance",
          "priority": "high|medium|low",
          "actionable": true/false,
          "document_type": "specific type like receipt, contract, report, etc",
          "business_impact": "how this affects the business operations",
          "next_steps": "suggested actions or follow-ups"
        }}
        """
    
    def _build_task_extraction_prompt(self, file_info: FileInfo) -> str:
        """Build task extraction prompt for Gemini."""
        return f"""
        You are a business operations expert. Analyze this document and extract specific, actionable tasks.
        
        File: {file_info.path}
        Folder: {file_info.folder}
        Content: {file_info.extracted_text[:2000]}
        
        Look for:
        1. Explicit tasks (TODO items, action items, deadlines, requirements)
        2. Implicit tasks (things that need follow-up, review, or action)
        3. Recurring processes (regular business activities)
        4. Compliance requirements (things that must be done for legal/regulatory reasons)
        5. Business opportunities (improvements, optimizations, growth opportunities)
        6. Financial tasks (payments due, invoices to process, expenses to categorize)
        
        For each task identified, consider:
        - Is this actionable and specific?
        - What is the business impact if not completed?
        - When should this be done?
        - Who would typically handle this?
        
        Return a JSON array of tasks:
        [
          {{
            "title": "Specific, actionable task title",
            "description": "Why this task matters and what needs to be done",
            "priority": "high|medium|low",
            "type": "process|opportunity|oneoff",
            "due_in_days": 7,
            "confidence": 0.8,
            "tags": ["relevant", "business", "tags"],
            "category": "expenses|grants|compliance|etc",
            "estimated_effort": "quick|medium|substantial",
            "business_impact": "what happens if this isn't done"
          }}
        ]
        
        Only include tasks with confidence > 0.6. If no clear tasks are found, return an empty array [].
        """
    
    def _parse_classification_response(self, response: str) -> Dict:
        """Parse Gemini classification response."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                return {
                    "topics": data.get("topics", []),
                    "confidence": data.get("confidence", 0.5),
                    "insights": data.get("insights", ""),
                    "priority": data.get("priority", "medium"),
                    "actionable": data.get("actionable", False),
                    "document_type": data.get("document_type", "unknown"),
                    "business_impact": data.get("business_impact", ""),
                    "next_steps": data.get("next_steps", "")
                }
        except Exception as e:
            logger.warning(f"Failed to parse Gemini classification response: {e}")
        
        # Fallback parsing
        return {
            "topics": [],
            "confidence": 0.3,
            "insights": response[:200] if response else "No response",
            "priority": "medium",
            "actionable": False
        }
    
    def _parse_task_response(self, response: str) -> List[Dict]:
        """Parse Gemini task extraction response."""
        try:
            # Extract JSON array from response
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                tasks = json.loads(json_match.group())
                return tasks if isinstance(tasks, list) else []
        except Exception as e:
            logger.warning(f"Failed to parse Gemini task response: {e}")
        
        return []
    
    def _parse_vision_response(self, response: str) -> Dict:
        """Parse Gemini vision analysis response."""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except Exception as e:
            logger.warning(f"Failed to parse Gemini vision response: {e}")
        
        return {"topics": [], "confidence": 0.3, "insights": "Failed to parse response"}
