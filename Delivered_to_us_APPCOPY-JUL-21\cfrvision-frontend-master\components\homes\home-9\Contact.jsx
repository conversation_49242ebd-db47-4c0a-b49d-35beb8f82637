"use client";
import { contactItems } from "@/data/contact";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { Button } from "react-bootstrap";
import Toaster from "@/components/common/Toaster";
import axiosInstance from "@/utlis/axios";

const schema = v.object({
  name: v.pipe(v.string(), v.nonEmpty("Name is required")),
  email: v.pipe(
    v.string(),
    v.nonEmpty("Email is required"),
    v.email("Email is invalid")
  ),
  organization: v.pipe(v.string(), v.nonEmpty("Organization is required")),
  message: v.pipe(v.string(), v.nonEmpty("Message is required")),
});

export default function Contact() {
  const [isLoading, setIsLoading] = useState(false);
  const { contextHolder, showToast } = Toaster();

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      name: "",
      email: "",
      organization: "",
      message: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.post("/v1/contact_us/add", data);
      console.log("response: ", response);
      showToast({
        type: "success",
        message: response?.data?.message,
      });
      reset();
      setIsLoading(false);
    } catch (error) {
      console.log("Error saving category:", error);
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <div className="container position-relative">
        <div className="row">
          {/* Left Column */}
          <div className="col-lg-4 mb-md-50 mb-sm-30 position-relative z-index-1">
            <h2 className="section-caption-slick mb-30 mb-sm-20">Contact Us</h2>
            <h3 className="section-title mb-50 mb-sm-30">
              Happy to connect with great people.
            </h3>
            {/* Contact Information */}
            <div className="row">
              <div className="col-md-11">
                {/* Address */}
                {contactItems.map((item, index) => (
                  <React.Fragment key={index}>
                    <div
                      className={`contact-item ${
                        index !== 3 ? "mb-40 mb-sm-20" : ""
                      }`}
                    >
                      <div className="ci-icon">
                        <i className={item.iconClass} />
                      </div>
                      <h4 className="ci-title  visually-hidden">
                        {item.title}
                      </h4>
                      <div className="ci-text">{item.text}</div>
                      <div>
                        <a
                          href={item.link.url}
                          target={item.link.target}
                          rel={item.link.rel}
                          className="link-hover-anim"
                          data-link-animate="y"
                        >
                          <span className="link-strong link-strong-unhovered">
                            {item.link.text}{" "}
                            <i
                              className="mi-arrow-right size-18"
                              aria-hidden="true"
                            ></i>
                          </span>
                          <span
                            className="link-strong link-strong-hovered"
                            aria-hidden="true"
                          >
                            {item.link.text}{" "}
                            <i
                              className="mi-arrow-right size-18"
                              aria-hidden="true"
                            ></i>
                          </span>
                        </a>
                      </div>
                    </div>
                  </React.Fragment>
                ))}
                {/* End Phone */}
              </div>
            </div>
            {/* End Contact Information */}
          </div>
          {/* End Left Column */}
          {/* Right Column */}
          <div className="col-lg-8 col-xl-7 offset-xl-1 wow fadeInUp">
            <div className="row g-0">
              {/* Google Map Column */}
              {/* <div className="col-md-5 d-flex align-items-stretch pt-40 pt-sm-0 pb-40 pb-sm-0 mb-sm-30">
              <div className="map-boxed-1 d-flex align-items-stretch">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d6143.08567813473!2d-75.602457!3d39.660002!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c703f3cdadbfdb%3A0x80d20252268fc006!2zMjQ1IFF1aWdsZXkgQmx2ZCBzdGUgaywgTmV3IENhc3RsZSwgREUgMTk3MjAsINCh0L_QvtC70YPRh9C10L3RliDQqNGC0LDRgtC4INCQ0LzQtdGA0LjQutC4!5e0!3m2!1suk!2sua!4v1677158678087!5m2!1suk!2sua"
                  width={600}
                  height={450}
                  style={{ border: 0 }}
                  allowFullScreen=""
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                />
              </div>
            </div> */}
              {/* End Google Map Column */}
              {/* Contact Form Column */}
              <div className="col-md-12">
                <div className="box-shadow bg-white round p-4 p-sm-5 position-relative z-index-1">
                  <h4 className="h3 mb-40 mb-sm-30">Get in Touch</h4>
                  {/* Contact Form */}
                  <form
                    noValidate
                    autoComplete="off"
                    onSubmit={handleSubmit(onSubmit)}
                    className="form contact-form"
                  >
                    {/* Name */}
                    <div className="form-group">
                      <Controller
                        name="name"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            name="name"
                            id="name"
                            className="input-md input-circle form-control"
                            placeholder="Name"
                            pattern=".{3,100}"
                            required
                          />
                        )}
                      />
                      {errors?.name && (
                        <span className="text-red">{errors?.name.message}</span>
                      )}
                    </div>
                    {/* End Name */}
                    {/* Email */}
                    <div className="form-group">
                      <Controller
                        name="email"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            name="email"
                            id="email"
                            className="input-md input-circle form-control"
                            placeholder="Email"
                            pattern=".{5,100}"
                            required
                          />
                        )}
                      />
                      {errors?.email && (
                        <span className="text-red">
                          {errors?.email.message}
                        </span>
                      )}
                    </div>
                    {/* End Email */}
                    {/* organization */}
                    <div className="form-group">
                      <Controller
                        name="organization"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            name="organization"
                            id="organization"
                            className="input-md input-circle form-control"
                            placeholder="Organization"
                            pattern=".{3,100}"
                            required
                          />
                        )}
                      />
                      {errors?.organization && (
                        <span className="text-red">
                          {errors?.organization.message}
                        </span>
                      )}
                    </div>
                    {/* End Email */}
                    {/* Message */}
                    <div className="form-group">
                      <Controller
                        name="message"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <textarea
                            {...field}
                            type="text"
                            name="message"
                            id="message"
                            className="input-md input-circle form-control"
                            style={{ height: 130 }}
                            placeholder="Message"
                            required
                          />
                        )}
                      />
                      {errors?.message && (
                        <span className="text-red">
                          {errors?.message?.message}
                        </span>
                      )}
                    </div>

                    {/* Send Button */}
                    <Button
                      type="submit"
                      className="submit_btn btn btn-mod btn-color btn-large btn-full btn-circle btn-hover-anim"
                      disabled={isLoading}
                    >
                      <span>Send Message</span>
                    </Button>
                    {/* End Send Button */}
                    {/* Inform Tip */}
                    <div className="form-tip w-100 pt-30 mt-sm-20">
                      <i className="icon-info size-16" /> All the fields are
                      required.
                    </div>
                    {/* End Inform Tip */}

                    {/* Inform Tip */}
                    {/* <div className="form-tip w-100 pt-30 mt-sm-20">
                    <i className="icon-info size-16" />
                    All the fields are required. By sending the form you agree
                    to the <a href="#">Terms &amp; Conditions</a> and{" "}
                    <a href="#">Privacy Policy</a>.
                  </div> */}
                    {/* End Inform Tip */}

                    <div
                      id="result"
                      role="region"
                      aria-live="polite"
                      aria-atomic="true"
                    />
                  </form>
                  {/* End Contact Form */}
                </div>
              </div>
              {/* End Contact Form Column */}
            </div>
          </div>
          {/* End Right Column */}
        </div>
      </div>
    </>
  );
}
