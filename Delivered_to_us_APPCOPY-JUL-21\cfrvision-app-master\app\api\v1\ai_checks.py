from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import ai_check_router
from app.database import get_db
from app.operations.ai_checks_operations.ai_checks_operations import AIChecks
from app.schemas.ai_checks import AICheckSchemaCreate, AICheckSchemaUpdate, CheckID
from app.schemas.common import FetchSchemaSearchFilter
from app.utils.jwt_authentication.jwt_handler import JW<PERSON><PERSON>earer


@ai_check_router.post("/ai-checks", dependencies=[Depends(JWTBearer())])
async def ai_checks_api(data: FetchSchemaSearchFilter, db: AsyncSession = Depends(get_db)):
    """
    Fetch AI checks based on the given filter criteria.

    This endpoint retrieves AI checks based on the provided search filter data.

    **Args**:
        data (FetchSchemaSearchFilter): The search filter criteria for fetching AI checks.
        db (AsyncSession): The database session used to query and retrieve AI check data.

    **Returns**:
        JSON: A list of AI checks that match the filter criteria.
    """
    return await AIChecks(db).ai_checks(data)


@ai_check_router.post("/create-ai-check", dependencies=[Depends(JWTBearer())])
async def create_ai_check_api(data: AICheckSchemaCreate, db: AsyncSession = Depends(get_db)):
    """
    Create a new AI check.

    This endpoint creates a new AI check entry in the database based on the provided input data.

    **Args**:
        data (AICheckSchemaCreate): The schema containing the necessary information to create an AI check.
        db (AsyncSession): The database session used to create the AI check in the database.

    **Returns**:
        JSON: The created AI check data.
    """
    return await AIChecks(db).create_ai_check(data)


@ai_check_router.post("/update-ai-check", dependencies=[Depends(JWTBearer())])
async def update_ai_check_api(data: AICheckSchemaUpdate, db: AsyncSession = Depends(get_db)):
    """
    Update an existing AI check.

    This endpoint updates an existing AI check entry in the database using the provided data.

    **Args**:
        data (AICheckSchemaUpdate): The schema containing the data used to update the AI check.
        db (AsyncSession): The database session used to update the AI check in the database.

    **Returns**:
        JSON: The updated AI check data.
    """
    return await AIChecks(db).update_ai_check(data)


@ai_check_router.post("/delete-ai-check", dependencies=[Depends(JWTBearer())])
async def delete_ai_check_api(data: CheckID, db: AsyncSession = Depends(get_db)):
    """
    Delete an AI check based on the provided ID.

    This endpoint deletes an AI check from the database using the provided check ID.

    **Args**:
        data (CheckID): The ID of the AI check to be deleted.
        db (AsyncSession): The database session used to perform the deletion operation.

    **Returns**:
        JSON: Confirmation of the deletion.
    """
    return await AIChecks(db).delete_ai_check(data)
