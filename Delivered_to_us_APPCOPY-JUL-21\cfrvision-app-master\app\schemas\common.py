from typing import Optional, List

from pydantic import BaseModel, Field
from pydantic.types import conint


class SearchText:
    search_text: Optional[str] = Field(
        default="",
        description="The text to search for.",
        examples=["example text", "search query"]
    )


class Pagination:
    page_no: int = Field(
        default=1,
        ge=1,
        description="The page number to retrieve. Starts from 1.",
        examples=[1, 2, 3]
    )
    page_size: int = Field(
        default=10,
        ge=1,
        description="Number of results per page.",
        examples=[10, 20, 50]
    )
    is_pagination: bool = Field(
        default=True,
        description="True if pagination is required else False.",
        examples=[True, False]
    )


class Filter(BaseModel):
    filter_column: Optional[str] = Field(
        default="",
        description="The column name on which the filter should be applied.",
        examples=["status", "category"]
    )
    filter_text: Optional[str] = Field(
        default="",
        description="The text to match in the filter column.",
        examples=["active", "inactive", "all"]
    )


class FilterList:
    filters: List[Filter] = Field(
        default=[],
        description="A list of filters to apply.",
        examples=[
            [{"filter_column": "status", "filter_text": "active"},
             {"filter_column": "category", "filter_text": "electronics"}]
        ]
    )


class UserID:
    user_id: str = Field(
        ...,
        description="Id of user, must be greater than or equal to 1.",
        examples=["1", "100"]
    )



class ObjectID:
    id: conint(ge=1) = Field(
        ...,
        description="Unique identifier of the column for any table used for, must be greater than or equal to 1.",
        examples=[1, 100]
    )


class ObjectIDOptional:
    id: conint(ge=1) = Field(
        None,
        description="Unique identifier of the column for any table used for, must be greater than or equal to 1.",
        examples=[1, 100]
    )


# FetchSchemaSearch
class FetchSchemaSearch(SearchText, Pagination, ObjectIDOptional, BaseModel):
    pass


# FetchSchemaSearchFilter
class FetchSchemaSearchFilter(SearchText, Pagination, ObjectIDOptional, FilterList, BaseModel):
    pass
