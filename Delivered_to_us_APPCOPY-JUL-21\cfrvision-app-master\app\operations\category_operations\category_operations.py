import json
import time

from fastapi import Depends
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import noload

from app.database import get_db
from app.database.models import Ai<PERSON>heck, AiCheckCategory
from app.schemas.category import CheckCategories
from app.utils.constants import categories_fetched_success, categories_fetched_failed, category_name_exists, \
    add_category_success, add_category_failed, category_not_found, update_category_success, dependent_llm_check_exists, \
    dependent_ai_check_exists, category_delete_success, category_delete_rejected, category_delete_failed, \
    update_category_failed


class AICheckCategory:
    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def get_ai_check_categories(self, data):
        """
        Retrieve AI check categories based on search text.

        This method fetches AI check categories from the database, optionally filtering them by the provided
        search text.

        **Args**:
            data (FetchSchemaSearchFilter): The filter criteria used to retrieve AI check categories, including
                                            optional search text.

        **Returns**:
            JSONResponse: A response containing the list of AI check categories that match the search criteria.
        """
        try:
            categories_query = select(AiCheckCategory).options(noload(AiCheckCategory.ai_checks)).filter(AiCheckCategory.is_deleted == False)
            # Add search filter if search_text is provided
            if data.search_text:
                categories_query = categories_query.filter(AiCheckCategory.category_name.ilike(f"%{data.search_text}%"))

            start_time = time.perf_counter()
            categories = (await self.db.execute(categories_query)).scalars().all()
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            print(f"Query execution for ai check took {execution_time:.4f} seconds")

            categories_data = [json.loads(CheckCategories.model_validate(category).model_dump_json()) for category in
                               categories]
            api_response = {
                "data": categories_data,
                "error": "",
                "message": categories_fetched_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            api_response = {
                "data": {},
                "error": str(e),
                "message": categories_fetched_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def create_ai_check_categories(self, categories):
        """
        Create a new AI check category.

        This method creates a new AI check category in the database, ensuring the category name does not already
        exist in the active categories.

        **Args**:
            categories (CheckCategories): The data used to create a new AI check category.

        **Returns**:
            JSONResponse: A response indicating whether the category creation was successful or not.
        """
        try:
            category = (await self.db.execute(select(AiCheckCategory).filter(
                AiCheckCategory.category_name == categories.category_name,
                AiCheckCategory.is_deleted == False))).scalars().first()
            if category:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": category_name_exists,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
            new_category = AiCheckCategory(
                category_name=categories.category_name,
                category_description=categories.category_description,
                category_order=categories.category_order,
                is_active=categories.is_active
            )
            self.db.add(new_category)
            await self.db.commit()
            api_response = {
                "data": {},
                "error": "",
                "message": add_category_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            api_response = {
                "data": {},
                "error": str(e),
                "message": add_category_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def update_ai_check_categories(self, categories):
        """
        Update an existing AI check category.

        This method updates an existing AI check category in the database. It checks if the category exists and
        whether another category with the same name already exists. It also handles partial updates for the category.

        **Args**:
            categories (CheckCategories): The data used to update an existing AI check category.

        **Returns**:
            JSONResponse: A response indicating whether the category update was successful or not.
        """
        try:
            category = (await self.db.execute(select(AiCheckCategory).filter(
                AiCheckCategory.category_id == categories.category_id,
                AiCheckCategory.is_deleted == False))).scalars().first()
            if not category:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": category_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            category_exists = (await self.db.execute(select(AiCheckCategory).filter(
                AiCheckCategory.category_id != categories.category_id,
                AiCheckCategory.category_name == categories.category_name,
                AiCheckCategory.is_deleted == False))).scalars().first()
            if not category_exists:
                update_data = categories.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    setattr(category, key, value)
                await self.db.commit()
                api_response = {
                    "data": {},
                    "error": "",
                    "message": update_category_success,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": category_name_exists,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
        except Exception as e:
            api_response = {
                "data": {},
                "error": str(e),
                "message": update_category_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def has_ai_check(self, check_category_id: int):
        """
        Check if any AI checks exist for a given category.

        This method checks if there are any AI checks associated with a given category. It also checks whether
        these AI checks are dependent on any LLM check requests.

        **Args**:
            check_category_id (int): The ID of the category to check for associated AI checks.

        **Returns**:
            str or None: A message indicating the existence of dependent LLM checks or AI checks, or None if no
                         dependent checks exist.
        """
        categories = (await self.db.execute(select(AiCheck).filter(
            AiCheck.category_id == check_category_id,
            AiCheck.is_deleted == False))).scalars().all()
        if len(categories) <= 0:
            return None
        for check in categories:
            if len(check.llm_check_requests) > 0:
                return dependent_llm_check_exists
            return dependent_ai_check_exists

    async def delete_ai_check_categories(self, categories):
        """
        Delete an AI check category.

        This method deletes an AI check category if it exists and is not dependent on any AI or LLM check requests.
        If dependent checks exist, the deletion is rejected.

        **Args**:
            categories (CheckCategoryID): The ID of the category to be deleted.

        **Returns**:
            JSONResponse: A response confirming whether the category deletion was successful or rejected due to dependencies.
        """
        try:
            category = (await self.db.execute(select(AiCheckCategory).filter(
                AiCheckCategory.category_id == categories.id,
                AiCheckCategory.is_deleted == False))).scalars().first()
            if category:
                ai_check_exists = await self.has_ai_check(categories.id)
                if ai_check_exists is None:
                    category.is_deleted = True
                    await self.db.commit()
                    api_response = {
                        "data": {},
                        "error": "",
                        "message": category_delete_success,
                        "status": True,
                    }
                    return JSONResponse(content=api_response, status_code=200)
                else:
                    api_response = {
                        "data": {},
                        "error": "",
                        "message": f"{category_delete_rejected} {ai_check_exists}",
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=400)
            else:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": category_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)

        except Exception as e:
            api_response = {
                "data": {},
                "error": str(e),
                "message": category_delete_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)
