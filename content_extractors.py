"""
Content extraction utilities for different file types.
"""
import logging
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)

# Optional imports - gracefully handle missing dependencies
try:
    from pdfminer.high_level import extract_text as pdf_extract_text
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logger.warning("pdfminer.six not available - PDF extraction disabled")

try:
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logger.warning("pytesseract/PIL not available - OCR disabled")

try:
    import zipfile
    import xml.etree.ElementTree as ET
    OFFICE_AVAILABLE = True
except ImportError:
    OFFICE_AVAILABLE = False


def extract_text_content(file_path: Path) -> str:
    """Extract content from plain text files."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.read()
    except Exception as e:
        logger.warning(f"Failed to read text file {file_path}: {e}")
        return ""


def extract_pdf_content(file_path: Path, use_google_vision: bool = False, google_ocr=None) -> str:
    """Extract text content from PDF files."""

    # Try Google Vision OCR for PDFs first if available
    if use_google_vision and google_ocr:
        try:
            text, confidence = google_ocr.extract_text_from_pdf(file_path)
            if confidence > 0.5 and text.strip():
                return text
        except Exception as e:
            logger.warning(f"Google Vision PDF OCR failed for {file_path}: {e}")

    # Fallback to pdfminer
    if not PDF_AVAILABLE:
        return "PDF extraction not available"

    try:
        text = pdf_extract_text(str(file_path))
        return text or ""
    except Exception as e:
        logger.warning(f"Failed to extract PDF content from {file_path}: {e}")
        return f"PDF extraction failed: {str(e)[:50]}"


def extract_image_content(file_path: Path, confidence_threshold: float = 0.5,
                         use_google_vision: bool = False, google_ocr=None) -> str:
    """Extract text from images using OCR (Tesseract or Google Vision)."""

    # Try Google Vision first if available and enabled
    if use_google_vision and google_ocr:
        try:
            text, confidence = google_ocr.extract_text_from_image(file_path)
            if confidence > confidence_threshold and text.strip():
                return text
        except Exception as e:
            logger.warning(f"Google Vision OCR failed for {file_path}: {e}")

    # Fallback to Tesseract
    if not OCR_AVAILABLE:
        return "OCR not available"

    try:
        image = Image.open(file_path)

        # Get OCR data with confidence scores
        ocr_data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)

        # Filter by confidence
        confident_text = []
        for i, conf in enumerate(ocr_data['conf']):
            if int(conf) > confidence_threshold * 100:  # pytesseract uses 0-100 scale
                text = ocr_data['text'][i].strip()
                if text:
                    confident_text.append(text)

        result = ' '.join(confident_text)
        return result if result else "No confident OCR text found"

    except Exception as e:
        logger.warning(f"OCR failed for {file_path}: {e}")
        return f"OCR failed: {str(e)[:50]}"


def extract_office_content(file_path: Path) -> str:
    """Extract content from Office documents (docx, xlsx, pptx)."""
    if not OFFICE_AVAILABLE:
        return "Office document extraction not available"
    
    try:
        extension = file_path.suffix.lower()
        
        if extension == '.docx':
            return _extract_docx_content(file_path)
        elif extension == '.xlsx':
            return _extract_xlsx_content(file_path)
        elif extension == '.pptx':
            return _extract_pptx_content(file_path)
        else:
            return "Unsupported Office format"
            
    except Exception as e:
        logger.warning(f"Office document extraction failed for {file_path}: {e}")
        return f"Office extraction failed: {str(e)[:50]}"


def _extract_docx_content(file_path: Path) -> str:
    """Extract text from DOCX files."""
    try:
        with zipfile.ZipFile(file_path, 'r') as docx:
            xml_content = docx.read('word/document.xml')
            root = ET.fromstring(xml_content)
            
            # Extract text from all text nodes
            text_elements = []
            for elem in root.iter():
                if elem.text:
                    text_elements.append(elem.text)
            
            return ' '.join(text_elements)
    except Exception as e:
        logger.warning(f"DOCX extraction failed: {e}")
        return ""


def _extract_xlsx_content(file_path: Path) -> str:
    """Extract text from XLSX files (sheet names and some cell content)."""
    try:
        with zipfile.ZipFile(file_path, 'r') as xlsx:
            # Try to get workbook info
            try:
                workbook_xml = xlsx.read('xl/workbook.xml')
                root = ET.fromstring(workbook_xml)
                
                # Extract sheet names
                sheet_names = []
                for sheet in root.iter():
                    if 'name' in sheet.attrib:
                        sheet_names.append(sheet.attrib['name'])
                
                return f"Excel file with sheets: {', '.join(sheet_names)}"
            except:
                return "Excel file (content not extractable)"
    except Exception as e:
        logger.warning(f"XLSX extraction failed: {e}")
        return ""


def _extract_pptx_content(file_path: Path) -> str:
    """Extract text from PPTX files."""
    try:
        with zipfile.ZipFile(file_path, 'r') as pptx:
            text_elements = []
            
            # Look for slide content
            for file_name in pptx.namelist():
                if file_name.startswith('ppt/slides/slide') and file_name.endswith('.xml'):
                    try:
                        slide_xml = pptx.read(file_name)
                        root = ET.fromstring(slide_xml)
                        
                        for elem in root.iter():
                            if elem.text and elem.text.strip():
                                text_elements.append(elem.text.strip())
                    except:
                        continue
            
            return ' '.join(text_elements) if text_elements else "PowerPoint file (no extractable text)"
    except Exception as e:
        logger.warning(f"PPTX extraction failed: {e}")
        return ""
