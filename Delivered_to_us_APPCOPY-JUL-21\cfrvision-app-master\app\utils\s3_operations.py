import io
import os

import boto3
from botocore.exceptions import NoCredentialsError

from app import logger
from app.utils.aes_encrypt_decrypt import AESEncryptDecrypt


class AWSFileOperations:
    def __init__(self):
        self.AWS_ACCESS_KEY_ID = AESEncryptDecrypt().decrypt(os.getenv("AWS_ACCESS_KEY_ID"))
        self.AWS_SECRET_ACCESS_KEY = AESEncryptDecrypt().decrypt(os.getenv("AWS_SECRET_ACCESS_KEY"))
        self.AWS_REGION = AESEncryptDecrypt().decrypt(os.getenv("AWS_REGION"))
        self.S3_BUCKET_NAME = AESEncryptDecrypt().decrypt(os.getenv("S3_BUCKET_NAME"))
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=self.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=self.AWS_SECRET_ACCESS_KEY,
            region_name=self.AWS_REGION,
        )

    async def s3_file_upload(self, file, user_id, file_name):
        try:
            file_key = f"files/{user_id}/{file_name}"
            self.s3_client.upload_fileobj(file.file, self.S3_BUCKET_NAME, file_key)
            file_url = self.get_presigned_url(user_id=user_id,file_name=file_name )

            return {"filename": file_name, "url": file_url}

        except NoCredentialsError as e:
            logger.exception(f"Error uploading file: {str(e)}", exc_info=True)
            raise

        except Exception as e:
            logger.exception(f"Error uploading file: {str(e)}", exc_info=True)
            raise

    def get_presigned_url(self, user_id, file_name):
        try:
            file_key = f"files/{user_id}/{file_name}"
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.S3_BUCKET_NAME, 'Key': file_key},
                ExpiresIn=3600  # Link valid for 1 hour
            )
            return url if url else ""
        except Exception as e:
            logger.exception(f"Error getting file: {str(e)}", exc_info=True)
            raise

    async def read_pdf_from_s3(self,user_id, file_name):
        try:
            file_key = f"files/{user_id}/{file_name}"
            # Download file into memory
            pdf_stream = io.BytesIO()
            self.s3_client.download_fileobj(self.S3_BUCKET_NAME, file_key, pdf_stream)
            pdf_stream.seek(0)  # Rewind the file pointer
            return pdf_stream
        except Exception as e:
            logger.exception(f"Error reading file: {str(e)}", exc_info=True)
            raise
