import { message } from "antd";

const Toaster = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const showToast = ({ message, type }) => {
    messageApi.open({
      type,
      content: message,
      className: "custom-toaster",
      duration: type === "error" ? 6 : 4,
      style: {
        // marginTop: "15vh",
        fontSize: "16px",
        fontWeight: "bold",
      },
    });
  };

  return { contextHolder, showToast };
};

export default Toaster;
