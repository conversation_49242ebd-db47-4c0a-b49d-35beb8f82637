from typing import Optional, Text

from pydantic import BaseModel, Field, model_validator


# APIKeysSchema
class APIKeysSchema(BaseModel):
    id: int = Field(
        ...,
        description="Unique identifier of the API key.",
        examples=[1, 2]
    )
    api_key: str = Field(
        ...,
        description="The API key string.",
        examples=["abcd1234xyz", "key-5678"]
    )
    is_active: bool = Field(
        ...,
        description="Indicates whether the API key is active.",
        examples=[True, False]
    )
    available_models: str = Field(
        ...,
        description="Comma Separated list of models",
        example="gpt-4o-mini, gpt=3.4-turbo"
    )

class APIKeysResponse(BaseModel):
    id: Optional[int] = None
    provider: str
    is_active: bool
    available_models: str

    class Config:
        from_attributes = True
        orm_mode = True

# APIKeysResponseSchema
class APIKeysResponseSchema(BaseModel):
    id: Optional[int] = None
    provider: str
    is_active: bool
    api_key: str
    available_models: str

    class Config:
        from_attributes = True
        orm_mode = True

class ViewEditStaticPagesSchema(BaseModel):
    title: str
    name: str
    content: Text
    created_at: int

    class Config:
        from_attributes = True
        orm_mode = True

class GetStaticPagesInputSchema(BaseModel):
    title: Optional[str] = None

class UpdateStaticPagesInputSchema(GetStaticPagesInputSchema):
    content: str

    @model_validator(mode='before')
    @classmethod
    def update_html_content(cls, values):
        content = values.get('content')
        if content:
            # Perform string replacements
            content = content.replace(
                '<p>', '<p style="margin: 0 !important;">'
            ).replace(
                '<h2>', '<h2 style="margin: 1rem 0 !important; color: #365f91 !important;">'
            ).replace(
                '<h1>', '<h1 style="margin: 1rem 0 !important; color: #17365d !important;">'
            )
            # Handle case variations (optional, see Edge Cases)
            content = content.replace(
                '<P>', '<p style="margin: 0 !important;">'
            ).replace(
                '<H2>', '<h2 style="margin: 1rem 0 !important; color: #365f91 !important;">'
            ).replace(
                '<H1>', '<h1 style="margin: 1rem 0 !important; color: #17365d !important;">'
            )
            values['content'] = content
        return values
