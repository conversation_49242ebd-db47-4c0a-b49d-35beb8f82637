from typing import Optional

from fastapi import Depends, Body
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import cms_router
from app.database import get_db
from app.operations.cms.cms import CMSOperations, StaticPagesOperations
from app.operations.contact_us_operations.contact_us_operations import ContactUsOperations
from app.schemas.cms import APIKeysSchema, GetStaticPagesInputSchema, UpdateStaticPagesInputSchema
from app.schemas.common import FetchSchemaSearchFilter
from app.schemas.contact_us import UpdateContactUsStatus
from app.utils.jwt_authentication.jwt_handler import JWTBearer


@cms_router.post("/get-api_keys", dependencies=[Depends(JWTBearer())])
async def api_keys(db: AsyncSession = Depends(get_db)):
    """
    Retrieve API keys from the system.

    This endpoint fetches the API keys currently stored in the system for use in authentication or integration.

    **Args**:
        db (AsyncSession): The database session used to query and retrieve API key data.

    **Returns**:
        JSON: A list of API keys available in the system.
    """
    return await CMSOperations(db).api_keys()


@cms_router.post("/edit-api_keys", dependencies=[Depends(JWTBearer())])
async def edit_keys(data: APIKeysSchema, db: AsyncSession = Depends(get_db)):
    """
    Edit API keys in the system.

    This endpoint allows modification of existing API keys in the system based on the provided data.

    **Args**:
        data (APIKeysSchema): The schema containing the data to update the API keys.
        db (AsyncSession): The database session used to perform the update operation.

    **Returns**:
        JSON: The updated API keys data after modification.
    """
    return await CMSOperations(db).edit_keys(data)

@cms_router.post("/contact-us", dependencies=[Depends(JWTBearer())])
async def contact_us_api(data: FetchSchemaSearchFilter, db: AsyncSession = Depends(get_db)):
    return await ContactUsOperations(db).contact_us(data)


@cms_router.post("/contact-us/update-status", dependencies=[Depends(JWTBearer())])
async def update_contact_us_api(data: UpdateContactUsStatus, db: AsyncSession = Depends(get_db)):
    return await ContactUsOperations(db).update_contact_us(data)

@cms_router.post("/static-pages/update-content", dependencies=[Depends(JWTBearer())])
async def update_static_pages_api(data: UpdateStaticPagesInputSchema, db: AsyncSession = Depends(get_db)):
    return await StaticPagesOperations(db).edit_static_page(data)

@cms_router.post("/static-pages")
async def get_static_pages_api(data: Optional[GetStaticPagesInputSchema] = Body(default=None), db: AsyncSession = Depends(get_db)):
    return await StaticPagesOperations(db).get_static_page(data)