"use client";
import TableWithPagination from "@/components/common/TableWithPagination/page";
import { Icon } from "@iconify/react";
import React, { useEffect, useState } from "react";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";
import ContactUsModal from "./ContactUsModal";
import moment from "moment";
import StaticPageModal from "./StaticPageModal";

const StaticPageList = () => {
  const [staticPageList, setStaticPageList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [show, setShow] = useState(false);
  const [staticPageData, setStaticPageData] = useState(null);

  const { contextHolder, showToast } = Toaster();

  const columns = [
    {
      headerName: "NAME",
      valueGetter: (p) => p.data.name,
      flex: 2,
    },
    {
      field: "CREATED DATE",
      valueFormatter: (p) => p.data.created_at,
      flex: 2,
      cellRenderer: (params) => {
        return (
          <p>
            {moment.unix(params.data.created_at).format("MM/DD/YYYY HH:MM")}
          </p>
        );
      },
    },
    {
      headerName: "ACTIONS",
      field: "actions",
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="basil:edit-outline"
            width="24"
            height="24"
            cursor={"pointer"}
            onClick={() => {
              setStaticPageData(params.data);
              setShow(true);
            }}
          />
        </div>
      ),
    },
  ];

  const fetchStaticPageList = async (
    page_no = 1,
    page_size = 10,
    search = searchText || ""
  ) => {
    setLoading(true);
    try {
      const requestBody = {
        filters: [],
        page_no: page_no,
        page_size: page_size,
        is_pagination: true,
        search_text: search,
      };

      const response = await axiosInstance.post(
        "/v1/cms/static-pages",
        requestBody
      );
      setStaticPageList(response.data.data);
    } catch (error) {
      console.error("Failed to fetch AI checks:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStaticPageList();
  }, []);

  const handlePaginationChange = (page_no, page_size) => {
    fetchStaticPageList(page_no, page_size, searchText);
  };

  const handleSearchChange = (value) => {
    setSearchText(value);
    fetchStaticPageList(1, 10, value);
  };

  const handleUpdateStaticPage = async (data) => {
    try {
      const response = await axiosInstance.post(
        "/v1/cms/static-pages/update-content",
        { title: data?.title, content: data?.content }
      );
      showToast({
        type: "success",
        message: response.data.message,
      });
      fetchStaticPageList(); // Refetch categories after update
    } catch (error) {
      console.error("Failed to update contact us:", error);
    }
  };

  return (
    <>
      {contextHolder}
      <div className="d-flex align-items-center flex-wrap justify-content-between w-100">
        <h5 className="text-start mb-0 flex-grow-1">Static Pages</h5>
      </div>

      <TableWithPagination
        data={staticPageList}
        columns={columns}
        onPaginationChange={handlePaginationChange}
        loading={loading}
        onSearchChange={handleSearchChange}
      />
      {show && (
        <StaticPageModal
          show={show}
          setShow={setShow}
          staticPageData={staticPageData}
          onSave={handleUpdateStaticPage}
        />
      )}
    </>
  );
};

export default StaticPageList;
