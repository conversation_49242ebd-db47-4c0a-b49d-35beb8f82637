import axios from "axios";
const baseURL = process.env.NEXT_PUBLIC_BACKEND_URL;

const axiosInstance = axios.create({
  baseURL: baseURL,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const authToken = localStorage.getItem("auth_token");

    // Attach the Authorization header if authToken exists
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Flag to track ongoing refresh requests
let isRefreshing = false;
let refreshSubscribers = [];

const onRefreshed = (newToken) => {
  refreshSubscribers.forEach((callback) => callback(newToken));
  refreshSubscribers = [];
};

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const location = window.location.pathname;
    const pathArray = ["/login", "/forgot-password"];

    if (!pathArray?.includes(location)) {
      const originalRequest = error.config;
      if (
        error.response &&
        error.response.status === 401 &&
        !originalRequest._retry
      ) {
        if (isRefreshing) {
          return new Promise((resolve) => {
            refreshSubscribers.push((token) => {
              originalRequest.headers["Authorization"] = `Bearer ${token}`;
              resolve(axiosInstance(originalRequest));
            });
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const refreshToken = localStorage.getItem("refresh_token"); // Get refresh token

          if (!refreshToken) {
            localStorage.clear();
            window.location.href = "/login";
            return Promise.reject(error);
          }

          // Call refresh token API
          const { data } = await axios.post(
            `${process.env.NEXT_PUBLIC_BACKEND_URL}v1/auth/token/refresh`,
            {},
            {
              headers: {
                Authorization: `Bearer ${refreshToken}`,
              },
            }
          );

          // Store new access token
          localStorage.setItem("auth_token", data?.data?.access_token);
          localStorage.setItem("refresh_token", data?.data?.refresh_token);

          // // Update all queued requests
          onRefreshed(data?.data.access_token);

          // // Retry original request with new token
          originalRequest.headers[
            "Authorization"
          ] = `Bearer ${data?.data?.access_token}`;
          return axiosInstance(originalRequest);
        } catch (refreshError) {
          console.log("refreshError: ", refreshError);
          localStorage.clear();
          window.location.href = "/login";
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else if (error.response && error.response.status === 403) {
        localStorage.clear();
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
