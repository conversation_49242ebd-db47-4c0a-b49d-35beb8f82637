# 🚀 Startup Ops Indexer & Daily Dashboard

A comprehensive system to automatically organize your startup documents, track recurring processes, identify opportunities, and provide a beautiful dashboard to manage everything.

## 🎯 What It Does

**Ingests everything** in your startup folders and produces:

- **📋 documents.txt** – Index of all documents by folder with descriptions
- **⚙️ processes.txt** – Recurring processes with frequency and next due dates  
- **💡 opportunities.txt** – Non-dated but valuable follow-ups
- **🌐 dashboard.html** – Visual dashboard with accomplishments, urgent tasks, calendar view
- **✅ Task Management** – Mark items complete/snooze and keep state across runs

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Indexer

```bash
python ops_indexer.py --root /path/to/your/startup/files
```

### 3. Open Dashboard

The system creates `.ops_out/dashboard.html` - open it in your browser to see your organized tasks!

### 4. Manage Tasks

```bash
# Mark a task as done
python ops_cli.py mark-done --id abc123 --note "Completed successfully"

# Snooze a task for 3 days
python ops_cli.py snooze --id abc123 --days 3

# Add a note to a task
python ops_cli.py note --id abc123 --text "Need to follow up with client"

# List all pending tasks
python ops_cli.py list --status pending

# Show summary
python ops_cli.py summary
```

## 📁 Example Folder Structure

The system works great with organized startup folders like yours:

```
STARTUP_ROOT/
├── AWS/                          # Cloud infrastructure docs
├── Branding/                     # Logo, design materials
├── Chargebee/                    # Billing system docs
├── Delivered_to_us_*/            # Deliverables from contractors
├── Functional_Specs_*/           # Technical specifications
├── Openxcell/                    # Development partner docs
├── Possible_Grants_For_Us/       # Grant opportunities
├── Pricing_tiers_to_Customers/   # Pricing documentation
├── Required_Compliance*/         # Legal/compliance docs
├── Week X Accomplished/          # Completed work by week
├── linkedin/                     # Social media materials
└── github/                       # Code repositories
```

## ⚙️ Configuration

Edit `config.yml` to customize rules for your business:

```yaml
timezone: "America/New_York"

cadences:
  expenses:
    match: ["receipt", "receipts", "invoice", "chargebee", "stripe"]
    recurrence: "DAILY"
    rule: "Enter receipts in QuickBooks"
  
  grants:
    match_folders: ["Possible_Grants_For_Us"]
    recurrence: "BIWEEKLY" 
    rule: "Grant follow-up"
    followup_after_days: 14

  deliverables:
    match_folders: ["Delivered_to_us_*"]
    recurrence: null
    due_after_days: 5
    rule: "Test deliverable and send feedback"

opportunity_hints:
  - "branding"
  - "linkedin" 
  - "aws"
  - "architecture"
```

## 🔧 Advanced Usage

### Enable OCR for Images/PDFs

```bash
python ops_indexer.py --root . --enable-ocr
```

### 🤖 Enable Google LLM Intelligence

```bash
# Set up Google Gemini API key
export GOOGLE_API_KEY="your-gemini-api-key"

# Use Google Gemini for smart document understanding
python ops_indexer.py --root . --enable-llm --llm-provider google

# Add Google Vision OCR for superior accuracy
python ops_indexer.py --root . --enable-llm --llm-provider google --enable-google-vision --google-credentials /path/to/credentials.json
```

**What Google Integration Adds:**
- 🧠 **Smart classification** - Understands document context, not just keywords
- 📋 **Intelligent task extraction** - Finds implicit tasks and business opportunities
- 🔍 **Superior OCR** - Better accuracy for receipts, invoices, handwritten notes
- 💡 **Business insights** - Explains why documents matter and suggests next steps

See [GOOGLE_SETUP.md](GOOGLE_SETUP.md) for detailed setup instructions.

### Custom Configuration

```bash
python ops_indexer.py --root . --config my_config.yml
```

### Custom Output Directory

```bash
python ops_indexer.py --root . --output-dir ~/my_dashboard
```

### Verbose Logging

```bash
python ops_indexer.py --root . --verbose
```

## 📊 Output Files

All outputs are created in `.ops_out/`:

- **📋 documents.txt** - Complete file index with descriptions
- **⚙️ processes.txt** - Recurring processes with due dates
- **💡 opportunities.txt** - Business opportunities to pursue
- **📝 tasks.json** - Canonical task state (used by dashboard)
- **🌐 dashboard.html** - Interactive web dashboard
- **📈 summary.txt** - Statistics and overview
- **📜 activity.log** - System activity log

## 🌐 Dashboard Features

The dashboard provides:

- **📅 Today** - Tasks due today
- **⚠️ Overdue** - Past due items
- **📆 This Week** - Upcoming tasks
- **💡 Opportunities** - Business opportunities
- **✅ Accomplished** - Recent completions

Interactive features:
- ✅ Mark tasks done
- 😴 Snooze tasks (1d/3d/1w)
- 📝 Add notes
- 🔄 Auto-refresh

## 🧪 Testing

Run the test suite:

```bash
python -m pytest tests/ -v
```

Or run individual test files:

```bash
python -m unittest tests.test_task_manager
python -m unittest tests.test_classifier
```

## 🔄 Automation

### Daily Cron Job

Add to your crontab for daily updates:

```bash
# Run indexer every day at 8 AM
0 8 * * * cd /path/to/startup/files && python ops_indexer.py --root .
```

### Windows Task Scheduler

Create a scheduled task to run:
```cmd
python C:\path\to\ops_indexer.py --root C:\path\to\startup\files
```

## 🛠️ Architecture

The system is modular and extensible:

- **📁 ingest.py** - File discovery and content extraction
- **🏷️ classifier.py** - File classification and rules engine  
- **📋 task_manager.py** - Task generation and state management
- **📤 output_generator.py** - Output file generation
- **🌐 dashboard_renderer.py** - HTML dashboard creation
- **⚙️ ops_cli.py** - Command-line interface
- **🔧 config_loader.py** - Configuration management
- **📊 models.py** - Data models and schemas

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Run the test suite
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Troubleshooting

### OCR Issues
If OCR fails, install Tesseract:
- **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt-get install tesseract-ocr`

### PDF Extraction Issues
If PDF extraction fails, try:
```bash
pip install --upgrade pdfminer.six
```

### Dashboard Not Loading
1. Check that `.ops_out/dashboard.html` exists
2. Ensure tasks.json was created successfully
3. Try regenerating: `python dashboard_renderer.py`

### Permission Errors
Ensure the script has read access to your files and write access to the output directory.

---

**🎉 Happy organizing! Your startup ops are now under control.**
