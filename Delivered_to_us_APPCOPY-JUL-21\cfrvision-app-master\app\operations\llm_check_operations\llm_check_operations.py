import asyncio
import json
import os
import re
import threading
import time

import httpx
import numpy as np
import tiktoken

from fastapi import Depends
from fastapi.responses import JSONResponse, FileResponse
from sqlalchemy import update, func, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from cryptography.fernet import <PERSON>rnet

from openai import OpenAI, AuthenticationError, APIConnectionError

from sentence_transformers import SentenceTransformer


from app import logger
from app.database import get_db
from app.database.models import LlmCheckRequest, AiCheck, PdfChunk, ChunkEmbedding, ApiKey, LlmCheckResponse, File, User
from app.schemas.cms import APIKeysResponseSchema, APIKeysResponse
from app.schemas.llm_check import LLMCheckRequestResponse
from app.utils.constants import (llm_checks_fetched_success, llm_checks_fetched_failed, add_llm_checks_success,
                                 add_check_failed, llm_check_not_found,
                                 delete_llm_checks_success, delete_llm_checks_failed, exception_occurred,
                                 api_keys_fetch_success, api_key_not_configure, api_keys_fetch_fail,
                                 fail_llm_check_response_html, add_llm_checks_fail_try_again,
                                 add_llm_checks_fail_file_issue)
from app.utils.create_pdf import html_to_pdf_with_fpdf

# Configuration constants for token management
DEFAULT_MAX_TOKENS = 80000
RESERVED_TOKENS = 3000  # For prompt + system message + response
MAX_RESPONSE_TOKENS = 1000
TOKEN_REDUCTION_BUFFER = 1000  # Buffer when reducing content
MIN_CHUNK_SIZE = 100  # Minimum characters to keep when truncating

ENCRYPTION_KEY = os.getenv("FERNET_ENCRYPTION_KEY")
cipher_suite = Fernet(ENCRYPTION_KEY)

root_model_path = os.getenv('MODEL_PATH')

async def process_request(check_prompt, request_id, check_id, db: AsyncSession = Depends(get_db)):
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.abspath(os.path.join(current_dir, '..', '..','..'))  # parent_dir = os.path.abspath("madia-app-backend")
        model_path = os.path.join(parent_dir, root_model_path)
        # Use the cached model instead of creating a new one
        if not os.path.exists(model_path):
            # If the model is not downloaded yet, download it (requires internet)
            model = SentenceTransformer('all-mpnet-base-v2')
            model.save(model_path)  # Save for offline use
        else:
            # Load the model offline from the local directory
            model = SentenceTransformer(model_path)
        # Match the same embedding pattern as used for document chunks
        # First create the embedding vector
        prompt_embedding = model.encode(check_prompt)
        # Then store as bytes when saving to DB, exactly like document chunks
        await db.execute(update(LlmCheckRequest).where(LlmCheckRequest.request_id == request_id)
                         .values(prompt_embedding=prompt_embedding.tobytes()))
        await db.commit()
        await find_top_chunks(request_id, max_tokens=95000, db=db)
        stmt_chunks = await db.execute(
            select(LlmCheckRequest.prompt, LlmCheckRequest.retrieved_chunks).where(
                LlmCheckRequest.request_id == request_id))
        result = stmt_chunks.first()

        if result:
            prompt, retrieved_chunks_json = result
        else:
            prompt, retrieved_chunks_json = None, None

        if retrieved_chunks_json is None:
            retrieved_chunks = []
            logger.error("Retrieved chunks is None for request ID: " + str(request_id))
        else:
            retrieved_chunks = json.loads(retrieved_chunks_json)

        if retrieved_chunks:
            placeholders = [_ for _ in retrieved_chunks]
            stmt_pdf_chunks = await db.execute(
                select(PdfChunk.chunk_text, PdfChunk.is_table, PdfChunk.page_number,
                       PdfChunk.chunk_index).where(
                    PdfChunk.id.in_(placeholders)))
            result = stmt_pdf_chunks.all()

            formatted_chunks = []
            current_page = None

            for row in result:
                chunk_text, is_table, page_number, chunk_index = row

                # Add page header when page changes
                if current_page != page_number:
                    formatted_chunks.append(f"\n\n==== PAGE {page_number} ====\n")
                    current_page = page_number

                # Add chunk type header
                chunk_type = "TABLE" if is_table else "TEXT"
                formatted_chunks.append(f"\n--- {chunk_type} (Chunk {chunk_index}) ---\n")

                # Add the chunk text
                formatted_chunks.append(chunk_text)

            relevant_text = "\n".join(formatted_chunks)
        else:
            # If no chunks were retrieved, use an empty string
            relevant_text = "No relevant text was found for this check."
            logger.warning(f"No chunks retrieved for request ID: {request_id}")
            
            # Update status to indicate no content found
            await _safe_update_request_status(request_id, 'Complete', 'No relevant chunks found for the request', db)
            
            # Return early with a meaningful response
            return
        stmt_ai_check = await db.execute(
            select(AiCheck.check_expected_result_format).where(
                AiCheck.check_id == check_id))
        api_field_value_result = stmt_ai_check.scalars().first()
        api_field_value = api_field_value_result[0] if api_field_value_result else ""
        new_prompt = (
            f"A.PROMPT: {check_prompt}. Consider the given description and check the values from the provided relevant text and provide the response considering PROMPT, RESPONSE FORMAT and RELEVANT TEXT\n"
            f"B.RESPONSE FORMAT: {api_field_value}\n"
            "Create a html code for the table using RESPONSE FORMAT.Make sure not to add any other headings or description with the html code"
        )
        await db.execute(update(LlmCheckRequest).where(LlmCheckRequest.request_id == request_id)
                         .values(status='Ready to Run'))
        await db.commit()

        run_check, run_check_response = await run_llm_check(request_id, new_prompt, relevant_text,
                                                            db)
        if run_check:
            print(f"LLM check run completed successfully for request_id:{request_id}")
            logger.info(f"LLM check run completed successfully for request_id:{request_id}")
        else:
            print(f"LLM check run failed for request_id:{request_id} with error. Probable Issue:'{run_check_response}'")
            logger.error(f"LLM check run failed for request_id:{request_id} with error. Probable Issue:'{run_check_response}'")
            
            # Check if it's a context window issue
            if "context window" in str(run_check_response).lower() or "token" in str(run_check_response).lower():
                await _safe_update_request_status(request_id, 'Complete', str(run_check_response), db)
            else:
                await _safe_update_request_status(request_id, 'Failed', str(run_check_response), db)
    except Exception as e:
        logger.error(
            f"Error in background processing for run llm check for request id:{request_id} with error. Probable Issue:'{str(e)}'")
        print(f"Error in background processing: {str(e)}")
        
        # Use safe update function to handle transaction errors
        await _safe_update_request_status(request_id, 'Failed', str(e), db)


async def _safe_update_request_status(request_id: int, status: str, comment: str = None, db: AsyncSession = None):
    """
    Safely update request status with proper error handling for aborted transactions.
    """
    if db is None:
        return
    
    try:
        # Try to rollback first to clear any aborted transaction
        try:
            await db.rollback()
        except Exception:
            pass
        
        # Update the status
        update_values = {"status": status}
        if comment:
            update_values["comment"] = comment
            
        await db.execute(
            update(LlmCheckRequest)
            .where(LlmCheckRequest.request_id == request_id)
            .values(**update_values)
        )
        await db.commit()
        logger.info(f"Successfully updated request {request_id} status to {status}")
        
    except Exception as e:
        logger.error(f"Error updating request {request_id} status to {status}: {str(e)}")
        try:
            await db.rollback()
        except Exception:
            pass


def cosine_similarity(a, b):
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))


async def find_top_chunks(request_id, max_tokens=DEFAULT_MAX_TOKENS, db: AsyncSession = Depends(get_db)):
    """Token-aware chunk selection that preserves document flow and balances content types."""
    try:
        stmt_llm_check = select(LlmCheckRequest.prompt_embedding, LlmCheckRequest.document_id, AiCheck.check_name,
                                LlmCheckRequest.prompt, LlmCheckRequest.model_used).join(AiCheck,
                                                             LlmCheckRequest.check_id == AiCheck.check_id).where(
            LlmCheckRequest.request_id == request_id, LlmCheckRequest.is_deleted == False)

        result_llm_check = await db.execute(stmt_llm_check)
        result = result_llm_check.first()
        if not result:
            logger.warning(f"No request found for ID: {request_id}")
            return

        prompt_embedding, document_id, check_name, prompt_text, model_used = result
        prompt_embedding = np.frombuffer(prompt_embedding, dtype=np.float32)

        # Get the actual model context limit
        model_context_limit = get_model_context_limit(model_used)
        
        # Reserve tokens for the prompt, system message, and response
        available_tokens = min(max_tokens, model_context_limit) - RESERVED_TOKENS

        stmt_chunks = (select(
            PdfChunk.id.label("pdf_chunk_id"),
            ChunkEmbedding.embedding,
            PdfChunk.is_table,
            PdfChunk.page_number,
            PdfChunk.chunk_text,
            PdfChunk.chunk_index,
            func.length(PdfChunk.chunk_text).label("char_length"))
                       .join(ChunkEmbedding, ChunkEmbedding.chunk_id == PdfChunk.id)
                       .where(PdfChunk.file_id == document_id)
                       .order_by(PdfChunk.page_number, PdfChunk.chunk_index))

        result_chunks = await db.execute(stmt_chunks)
        result_chunk = result_chunks.all()
        
        # Calculate similarities with equal weighting for tables and text
        chunk_data = []
        for pdf_chunk_id, embedding_blob, is_table, page_number, chunk_text, chunk_index, char_length in result_chunk:
            # Use actual token counting instead of character estimation
            estimated_tokens = count_tokens(chunk_text, model_used)

            chunk_embedding = np.frombuffer(embedding_blob, dtype=np.float32)
            similarity = cosine_similarity(prompt_embedding, chunk_embedding)

            # Apply minimal boosting based on content relevance, with equal weight for tables and text
            boost = 1.0

            # Boost chunks with terms that match the prompt
            prompt_terms = set(re.findall(r'\b\w+\b', prompt_text.lower()))
            chunk_terms = set(re.findall(r'\b\w+\b', chunk_text.lower()))
            matching_terms = prompt_terms.intersection(chunk_terms)

            # Boost based on term overlap
            if matching_terms:
                boost *= (1 + 0.1 * len(matching_terms))

            # Store all relevant info
            chunk_data.append({
                'id': pdf_chunk_id,
                'similarity': similarity * boost,
                'is_table': is_table,
                'page_number': page_number,
                'chunk_index': chunk_index,
                'tokens': estimated_tokens,
                'text': chunk_text
            })

        # Organize chunks by page to maintain document flow
        chunks_by_page = {}
        for chunk in chunk_data:
            page = chunk['page_number']
            if page not in chunks_by_page:
                chunks_by_page[page] = []
            chunks_by_page[page].append(chunk)

        # Sort chunks within each page by similarity
        for page in chunks_by_page:
            chunks_by_page[page].sort(key=lambda x: x['similarity'], reverse=True)

        # Select chunks while preserving document flow
        final_chunks = []
        total_tokens = 0

        # First pass: Take the most relevant chunk from each page
        # This ensures coverage across the entire document
        pages = sorted(chunks_by_page.keys())
        for page in pages:
            if chunks_by_page[page] and total_tokens < available_tokens:
                best_chunk = chunks_by_page[page][0]
                if total_tokens + best_chunk['tokens'] <= available_tokens:
                    final_chunks.append(best_chunk)
                    total_tokens += best_chunk['tokens']
                    chunks_by_page[page].pop(0)

        # Second pass: Take remaining chunks by page order and relevance
        # This maintains the logical flow of the document
        for page in pages:
            # Sort remaining chunks on this page by chunk_index to maintain order
            chunks_by_page[page].sort(key=lambda x: x['chunk_index'])

            for chunk in chunks_by_page[page]:
                if total_tokens + chunk['tokens'] <= available_tokens:
                    final_chunks.append(chunk)
                    total_tokens += chunk['tokens']
                else:
                    break

        # Before finalizing, identify table chunks and add their preceding chunks
        # This ensures table titles are captured
        enhanced_chunks = []
        chunk_ids_added = set()  # Track which chunks have been added

        # Process chunks in order
        for i, chunk in enumerate(final_chunks):
            # Add the current chunk if not already added
            if chunk['id'] not in chunk_ids_added:
                enhanced_chunks.append(chunk)
                chunk_ids_added.add(chunk['id'])

            # If this is a table, look for the preceding chunk
            if chunk['is_table']:
                # Find the chunk that comes before this one on the same page
                preceding_chunks = [
                    c for c in chunk_data
                    if c['page_number'] == chunk['page_number'] and
                       c['chunk_index'] < chunk['chunk_index'] and
                       c['id'] not in chunk_ids_added
                ]

                if preceding_chunks:
                    # Sort by chunk_index in descending order to get the closest preceding chunk
                    preceding_chunks.sort(key=lambda x: x['chunk_index'], reverse=True)
                    preceding_chunk = preceding_chunks[0]

                    # Check if adding this chunk would exceed the token limit
                    if total_tokens + preceding_chunk['tokens'] <= available_tokens:
                        enhanced_chunks.append(preceding_chunk)
                        chunk_ids_added.add(preceding_chunk['id'])
                        total_tokens += preceding_chunk['tokens']
                        logger.info(f"Added preceding chunk {preceding_chunk['id']} for table chunk {chunk['id']}")

        # If we still have too many tokens, implement fallback strategy
        if total_tokens > available_tokens:
            logger.warning(f"Token limit exceeded ({total_tokens} > {available_tokens}). Implementing fallback strategy.")
            enhanced_chunks = await _apply_fallback_strategy(enhanced_chunks, available_tokens, model_used)
            total_tokens = sum(chunk['tokens'] for chunk in enhanced_chunks)

        # Extract just the IDs for storage
        final_chunk_ids = [c['id'] for c in enhanced_chunks]

        # Update the request with selected chunks
        result = await db.execute(
            text("SELECT column_name FROM information_schema.columns WHERE table_name = 'llm_check_requests'")
        )
        columns = [row[0] for row in result.fetchall()]

        try:
            if 'chunk_selection_method' in columns:
                await db.execute(update(LlmCheckRequest).where(LlmCheckRequest.request_id == request_id)
                                 .values(retrieved_chunks=json.dumps(final_chunk_ids),
                                         chunk_selection_method="document_flow_preserving"))
                await db.commit()
            else:
                await db.execute(update(LlmCheckRequest).where(LlmCheckRequest.request_id == request_id)
                                 .values(retrieved_chunks=json.dumps(final_chunk_ids)))
                await db.commit()
            logger.info(
                f"Updated request ID {request_id} with {len(final_chunk_ids)} chunks ({total_tokens} tokens) in document flow order")
        except Exception as update_error:
            logger.error(f"Error updating chunks for request {request_id}: {str(update_error)}")
            try:
                await db.rollback()
            except Exception:
                pass

    except Exception as e:
        logger.error(f"Error finding top chunks: {str(e)}")
        try:
            await db.rollback()
        except Exception:
            pass


async def _apply_fallback_strategy(chunks, available_tokens, model_used):
    """
    Apply fallback strategy when chunks exceed token limit.
    Strategy: Keep most relevant chunks and reduce content progressively.
    """
    try:
        # Strategy 1: Keep only the most relevant chunks
        sorted_chunks = sorted(chunks, key=lambda x: x['similarity'], reverse=True)
        selected_chunks = []
        total_tokens = 0
        
        for chunk in sorted_chunks:
            if total_tokens + chunk['tokens'] <= available_tokens:
                selected_chunks.append(chunk)
                total_tokens += chunk['tokens']
            else:
                break
        
        # If still too many tokens, try strategy 2: Truncate long chunks
        if total_tokens > available_tokens:
            logger.warning("Strategy 1 failed, trying chunk truncation")
            selected_chunks = await _truncate_chunks(selected_chunks, available_tokens, model_used)
        
        # Sort back by page and chunk order for document flow
        selected_chunks.sort(key=lambda x: (x['page_number'], x['chunk_index']))
        
        return selected_chunks
        
    except Exception as e:
        logger.error(f"Error in fallback strategy: {str(e)}")
        # Return empty list as last resort
        return []


async def _truncate_chunks(chunks, available_tokens, model_used):
    """
    Truncate chunks to fit within token limit while preserving most important content.
    """
    try:
        truncated_chunks = []
        total_tokens = 0
        
        for chunk in chunks:
            if total_tokens >= available_tokens:
                break
                
            chunk_text = chunk['text']
            chunk_tokens = chunk['tokens']
            
            # If chunk fits, add it as is
            if total_tokens + chunk_tokens <= available_tokens:
                truncated_chunks.append(chunk)
                total_tokens += chunk_tokens
            else:
                # Calculate how many tokens we can add
                remaining_tokens = available_tokens - total_tokens
                
                # Estimate characters per token (rough estimate)
                chars_per_token = len(chunk_text) / chunk_tokens
                max_chars = int(remaining_tokens * chars_per_token * 0.8)  # 80% to be safe
                
                if max_chars > MIN_CHUNK_SIZE:  # Only truncate if we can keep meaningful content
                    # Truncate text and recalculate tokens
                    truncated_text = chunk_text[:max_chars]
                    truncated_tokens = count_tokens(truncated_text, model_used)
                    
                    if truncated_tokens <= remaining_tokens:
                        truncated_chunk = chunk.copy()
                        truncated_chunk['text'] = truncated_text
                        truncated_chunk['tokens'] = truncated_tokens
                        truncated_chunks.append(truncated_chunk)
                        total_tokens += truncated_tokens
                        logger.info(f"Truncated chunk {chunk['id']} from {chunk_tokens} to {truncated_tokens} tokens")
        
        return truncated_chunks
        
    except Exception as e:
        logger.error(f"Error truncating chunks: {str(e)}")
        return chunks[:1] if chunks else []  # Return first chunk as fallback


def decrypt_api_key(encrypted_key):
    if not encrypted_key:
        return None
    try:
        return cipher_suite.decrypt(encrypted_key.encode()).decode()
    except Exception:
        return None


async def get_api_key(provider, db: AsyncSession = Depends(get_db)):
    stmt_api_key = await db.execute(
        select(ApiKey.api_key).where(
            ApiKey.provider == provider))
    result = stmt_api_key.scalars().first()
    if result and result[0]:
        return result
        # final_result = AESEncryptDecrypt().decrypt(result)
        # return final_result
    return None


async def get_llm_model(request_id, provider, db: AsyncSession = Depends(get_db)):
    """Get model from api_keys table for specified provider"""
    try:
        stmt_llm_creds = await db.execute(
            select(LlmCheckRequest.model_used).where(
                LlmCheckRequest.request_id == request_id))
        result = stmt_llm_creds.scalars().first()
        if result and result[0]:
            return result
        else:
            return 'gpt-4o-mini'
    except Exception:
        return 'gpt-4o-mini'


def count_tokens(text, model_name):
    # Use the actual model name for encoding
    try:
        encoding = tiktoken.encoding_for_model(model_name)
    except Exception:
        # Fallback to cl100k_base encoding if model not found
        encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))


def get_model_context_limit(model_name):
    # Define context limits for different models
    context_limits = {
        "gpt-4o-mini": 128000,
        "gpt-4o": 128000,
        "gpt-4": 8192,
        "gpt-3.5-turbo": 16385,
        # Add other models as needed
    }
    # Return the limit for the specified model, or a default value
    return context_limits.get(model_name, 16385)  # Default to 16k if unknown


async def run_llm_check(request_id, prompt, relevant_text, db: AsyncSession = Depends(get_db)):
    try:        # Fetch Active API Key and Model
        api_key = await get_api_key('openai', db)
        if not api_key:
            return False, 'API key not found'

        # Get model from api_keys table
        model = await get_llm_model(request_id, 'openai', db)
        if not model:
            return False, "Model not found"
        # Create custom HTTP client
        http_client = httpx.Client()

        # Configure OpenAI client with custom HTTP client
        client = OpenAI(
            api_key=api_key,
            http_client=http_client
        )

        try:
            # Set max_tokens for the response
            max_tokens = MAX_RESPONSE_TOKENS
            # Get the model's context limit
            model_context_limit = get_model_context_limit(model)

            # Create the full prompt content
            system_message = "You are a helpful assistant."
            user_content = f"{prompt}\n\nRELEVANT TEXT{relevant_text}. " \
                          f"Make sure to provide just the raw html code for table with given RESPONSE FORMAT." \
                          f"Consider replacing the amount or values obtained from the relevant text in the table." \
                          f"Check for match or discrepancy and in that particular table data, provide inputs like match or discrepancy in that particular table data place" \
                          f"Give me only the HTML <table> element (no <html> or <body> tags). " \
                          f"Return it as a single-line string with no newlines or extra whitespace. " \
                          f"No Markdown formatting, no backticks — just the plain string."

            # Count tokens for the full prompt
            full_prompt_tokens = count_tokens(system_message + user_content, model)
            total_estimated_tokens = full_prompt_tokens + max_tokens

            if total_estimated_tokens > model_context_limit:
                # Try to reduce the relevant text
                logger.warning(f"Token limit exceeded ({total_estimated_tokens} > {model_context_limit}). Attempting to reduce content.")
                
                # Calculate how much we need to reduce
                excess_tokens = total_estimated_tokens - model_context_limit
                tokens_to_reduce = excess_tokens + TOKEN_REDUCTION_BUFFER
                
                # Estimate characters to remove (rough estimate)
                chars_per_token = len(relevant_text) / count_tokens(relevant_text, model)
                chars_to_remove = int(tokens_to_reduce * chars_per_token)
                
                if len(relevant_text) > chars_to_remove:
                    # Truncate the relevant text
                    reduced_text = relevant_text[:-chars_to_remove]
                    user_content = f"{prompt}\n\nRELEVANT TEXT{reduced_text}. " \
                                  f"Make sure to provide just the raw html code for table with given RESPONSE FORMAT." \
                                  f"Consider replacing the amount or values obtained from the relevant text in the table." \
                                  f"Check for match or discrepancy and in that particular table data, provide inputs like match or discrepancy in that particular table data place" \
                                  f"Give me only the HTML <table> element (no <html> or <body> tags). " \
                                  f"Return it as a single-line string with no newlines or extra whitespace. " \
                                  f"No Markdown formatting, no backticks — just the plain string."
                    
                    # Recheck token count
                    full_prompt_tokens = count_tokens(system_message + user_content, model)
                    total_estimated_tokens = full_prompt_tokens + max_tokens
                    
                    if total_estimated_tokens > model_context_limit:
                        return False, f'Input text still exceeds the model\'s context window after reduction. The model {model} has a limit of {model_context_limit} tokens, but your input has {total_estimated_tokens} tokens.'
                else:
                    return False, f'Input text exceeds the model\'s context window. The model {model} has a limit of {model_context_limit} tokens, but your input has {total_estimated_tokens} tokens.'

            # Make the API call
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_content}
                ],
                max_tokens=max_tokens
            )

            await db.execute(update(LlmCheckRequest).where(LlmCheckRequest.request_id == request_id)
                             .values(status='Complete',result=response.choices[0].message.content))
            await db.commit()

            # Insert the OpenAI API response into the llm_response table
            response_data = {
                'id': response.id,
                'object': response.object,
                'created': response.created,
                'model': response.model,
                'choices': [{
                    'message': {
                        'role': choice.message.role,
                        'content': choice.message.content
                    },
                    'finish_reason': choice.finish_reason
                } for choice in response.choices],
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            }
            llm_check_response = LlmCheckResponse(
                request_id=request_id,
                raw_response=json.dumps(response_data),
                parsed_response=response.choices[0].message.content,
                status_code=200,
                error_message=None,
                processing_time=None,
                prompt_tokens=response.usage.prompt_tokens,
                completion_tokens=response.usage.completion_tokens,
                total_tokens=response.usage.total_tokens
            )
            db.add(llm_check_response)
            await db.commit()
            response_data = {
                "request_id": request_id,
                "raw_response": json.dumps(response_data),
                "parsed_response": response.choices[0].message.content,
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }

            return True, response_data

        except AuthenticationError:
            try:
                await db.rollback()
            except Exception:
                pass
            
            try:
                llm_check_response = LlmCheckResponse(
                    request_id=request_id,
                    raw_response=json.dumps({}),
                    parsed_response=fail_llm_check_response_html,
                    status_code=400,
                    error_message=None,
                    processing_time=None,
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0
                )
                db.add(llm_check_response)
                await db.commit()
            except Exception as db_error:
                logger.error(f"Error saving authentication error response: {str(db_error)}")
            
            return False, 'Invalid API key'
        except APIConnectionError as e:
            try:
                await db.rollback()
            except Exception:
                pass
            
            try:
                llm_check_response = LlmCheckResponse(
                    request_id=request_id,
                    raw_response=json.dumps({}),
                    parsed_response=fail_llm_check_response_html,
                    status_code=400,
                    error_message=None,
                    processing_time=None,
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0
                )
                db.add(llm_check_response)
                await db.commit()
            except Exception as db_error:
                logger.error(f"Error saving connection error response: {str(db_error)}")
            
            return False, f'Connection error: {str(e)}'
        except Exception as e:
            try:
                await db.rollback()
            except Exception:
                pass
            
            try:
                llm_check_response = LlmCheckResponse(
                    request_id=request_id,
                    raw_response=json.dumps({}),
                    parsed_response=fail_llm_check_response_html,
                    status_code=400,
                    error_message=None,
                    processing_time=None,
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0
                )
                db.add(llm_check_response)
                await db.commit()
            except Exception as db_error:
                logger.error(f"Error saving general error response: {str(db_error)}")
            
            return False, f'Error running LLM check: {str(e)}'

    except Exception as e:
        return False, "Internal server error"


class LLMCheckRequest:

    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def get_llm_check_request(self, data, user_id):
        try:
            stmt_user = select(User).where(User.id == user_id)
            result_user = await self.db.execute(stmt_user)
            user_obj = result_user.scalars().first()
            stmt_llm_check = select(LlmCheckRequest).where(LlmCheckRequest.user_id==user_id,LlmCheckRequest.is_deleted == False)
            if data.id:
                stmt_llm_check = stmt_llm_check.where(LlmCheckRequest.request_id==data.id)
                result_llm_check = await self.db.execute(stmt_llm_check)
                llm_checks = result_llm_check.scalars().first()

                # Format data for response
                llm_checks_data = json.loads(LLMCheckRequestResponse.model_validate(llm_checks).model_dump_json())
                files_query = select(File).where(File.id == llm_checks_data.get('document_id'))
                if llm_checks_data.get('llm_check_response'):
                    llm_checks_data['llm_check_response'] = [
                        sorted(llm_checks_data.get('llm_check_response'), key=lambda x: x['created_at'], reverse=True)[0]]
                file = (await self.db.execute(files_query)).scalars().first()
                llm_checks_data['institution_name'] = user_obj.organization
                llm_checks_data['file_name'] = file.file_name
                llm_checks_data['version'] = file.version
                llm_checks_data['fy_end'] = file.fy_end
                llm_checks_data['draft_final'] = file.draft_final

            else:
                result_llm_check = await self.db.execute(stmt_llm_check)
                llm_checks = result_llm_check.scalars().all()

                # Format data for response
                llm_checks_data = [json.loads(LLMCheckRequestResponse.model_validate(check).model_dump_json()) for check in
                                   llm_checks]
                for llm_check in llm_checks_data:
                    if llm_check.get('llm_check_response'):
                        llm_check['llm_check_response'] = [sorted(llm_check.get('llm_check_response'), key=lambda x: x['created_at'], reverse=True)[0]]
                    files_query = select(File).where(File.id == llm_check.get('document_id'))
                    file = (await self.db.execute(files_query)).scalars().first()
                    llm_check['institution_name'] = user_obj.organization
                    llm_check['file_name'] = file.file_name
                    llm_check['version'] = file.version
                    llm_check['fy_end'] = file.fy_end
                    llm_check['draft_final'] = file.draft_final

            api_response = {
                "data": llm_checks_data,
                "error": "",
                "message": llm_checks_fetched_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error retrieving LLM Check Request: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": llm_checks_fetched_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def create_llm_check_request(self, data, user_id):
        try:
            # Create a new AI Check
            stmt_file = select(File).where(File.id==data.document_id)
            stmt_file_obj = await self.db.execute(stmt_file)
            file_obj = stmt_file_obj.scalars().first()
            if file_obj.status in ["Processing","Extracting Text"]:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": add_llm_checks_fail_try_again,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
            elif file_obj.status == "Failed":
                api_response = {
                    "data": {},
                    "error": "",
                    "message": add_llm_checks_fail_file_issue,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)

            check_details = await self.db.execute(select(AiCheck).where(AiCheck.check_id == data.check_id))
            check_details = check_details.scalars().first()
            check_id = check_details.check_id
            check_prompt = check_details.check_prompt + check_details.check_extended_description
            llm_check = LlmCheckRequest(
                document_id=data.document_id,
                check_id=data.check_id,
                status="Ready to Run",
                prompt=check_prompt,
                user_id=user_id,
                llm_provider='openai',
                model_used=data.model_used
            )
            self.db.add(llm_check)
            await self.db.commit()
            await self.db.refresh(llm_check)
            # request_id = llm_check.request_id

            # Get the event loop from the main thread
            # loop = asyncio.get_running_loop()
            # def run_async():
            #     future = asyncio.run_coroutine_threadsafe(
            #         process_request(check_prompt, request_id, check_id, db=self.db), loop
            #     )
            #     future.result()
            # # # Start background thread
            # thread = threading.Thread(target=run_async)
            # thread.start()
            # loop = asyncio.get_running_loop()

            api_response = {
                "data": {"id": llm_check.request_id},
                "error": "",
                "message": add_llm_checks_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error adding llm check request: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": add_check_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def re_run_llm_check_request(self, data, user_id):
        try:
            # Create a new AI Check
            llm_check_request = await self.db.execute(select(LlmCheckRequest).where(LlmCheckRequest.user_id==user_id,LlmCheckRequest.request_id == data.id))
            llm_check_request = llm_check_request.scalars().first()
            if not llm_check_request:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": "LLM Check request not found",
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            check_prompt = llm_check_request.prompt
            request_id = data.id
            check_id = llm_check_request.check_id
            llm_check_request.status = "processing"
            await self.db.commit()
            await self.db.refresh(llm_check_request)

            # Get the event loop from the main thread
            loop = asyncio.get_running_loop()
            # print(1)
            #
            def run_async():
                future = asyncio.run_coroutine_threadsafe(
                    process_request(check_prompt, request_id, check_id, db=self.db), loop
                )

                future.result()
            # # Start background thread
            thread = threading.Thread(target=run_async)
            # thread.daemon = True
            thread.start()

            api_response = {
                "data": {"id": llm_check_request.request_id},
                "error": "",
                "message": "Running llm check request",
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error running llm check request: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": add_check_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def delete_llm_check_request(self, data, user_id):
        try:  # Delete existing LLM check request
            stmt_llm_check = select(LlmCheckRequest).where(LlmCheckRequest.user_id==user_id,
                                                           LlmCheckRequest.request_id == data.id,
                                                           LlmCheckRequest.is_deleted == False)
            result = await self.db.execute(stmt_llm_check)
            check = result.scalar_one_or_none()

            if not check:
                api_response = {
                    "data": {},
                    "error": llm_check_not_found,
                    "message": llm_check_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            stmt = (
                update(LlmCheckResponse)
                .where(
                    LlmCheckResponse.request_id == data.id,
                    LlmCheckResponse.is_deleted == False
                )
                .values(is_deleted=True)
            )
            await self.db.execute(stmt)
            check.is_deleted = True
            await self.db.commit()

            api_response = {
                "data": {},
                "error": "",
                "message": delete_llm_checks_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error deleting LLM Check Request: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": delete_llm_checks_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)


    async def download_result_llm_check_request_api(self, data, user_id):
        try:
            user_check = select(User).where(User.id==user_id,User.is_deleted==False)
            result_user = await self.db.execute(user_check)
            user_obj = result_user.scalars().first()
            stmt_llm_check = select(LlmCheckRequest).where(LlmCheckRequest.user_id == user_id,
                                                           LlmCheckRequest.request_id == data.id,
                                                           LlmCheckRequest.is_deleted == False)
            result_llm_check = await self.db.execute(stmt_llm_check)
            llm_checks = result_llm_check.scalars().first()
            if llm_checks:
                # Format data for response
                llm_checks_data = json.loads(LLMCheckRequestResponse.model_validate(llm_checks).model_dump_json())
                if llm_checks_data.get('llm_check_response'):
                    llm_checks_data['llm_check_response'] = [
                        sorted(llm_checks_data.get('llm_check_response'), key=lambda x: x['created_at'], reverse=True)[0]]
                files_query = select(File).where(File.id == llm_checks.document_id)
                file = (await self.db.execute(files_query)).scalars().first()
                data = {
                    "institution_name": user_obj.organization,
                    "file_name": file.file_name,
                    "draft_final": file.draft_final,
                    "fy_end": file.fy_end,
                    "version": file.version,
                    "category_name": llm_checks_data.get('ai_check').get('category').get('category_name'),
                    "check_name": llm_checks_data.get('ai_check').get('check_name'),
                    "result": llm_checks_data.get('llm_check_response')
                }
                file_path = await html_to_pdf_with_fpdf(data, f"llm_check_result_files/{time.time()}.pdf")
                if not file_path:
                    api_response = {
                        "data": {},
                        "error": exception_occurred,
                        "message": exception_occurred,
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=500)


                return FileResponse(
                        path=f"{file_path}",
                        filename=file.file_name,
                        media_type='application/pdf'
                    )
            else:
                api_response = {
                    "data": "",
                    "error": "",
                    "message": llm_check_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)

        except Exception as e:
            print(str(e))
            logger.exception(f"Error exporting LLM Check Request Result to PDF: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def get_list_of_models(self):
        try:
            stmt = select(ApiKey).where(ApiKey.provider=="openai")
            result = await self.db.execute(stmt)
            api_keys = result.scalars().first()
            if api_keys:
                api_keys_data = json.loads(APIKeysResponse.model_validate(api_keys).model_dump_json())
                api_response = {
                    "data": api_keys_data,
                    "error": "",
                    "message": api_keys_fetch_success,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:

                api_response = {
                    "data": {},
                    "error": "",
                    "message": api_key_not_configure,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
        except Exception as e:
            logger.exception(f"Error deleting AI Check: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": api_keys_fetch_fail,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)
