"""
Unit tests for task management functionality.
"""
import unittest
import tempfile
import json
from datetime import date, timedelta
from pathlib import Path

from models import Task, TaskType, TaskStatus, Priority, Recurrence
from task_manager import TaskGenerator, TaskStateManager
from config_loader import get_default_config


class TestTaskGenerator(unittest.TestCase):
    """Test task generation logic."""
    
    def setUp(self):
        self.config = get_default_config()
        self.generator = TaskGenerator(self.config)
    
    def test_task_id_generation(self):
        """Test that task IDs are stable and unique."""
        title = "Test Task"
        sources = ["file1.txt", "file2.txt"]
        
        id1 = Task.generate_id(title, sources)
        id2 = Task.generate_id(title, sources)
        
        # Same inputs should generate same ID
        self.assertEqual(id1, id2)
        
        # Different inputs should generate different IDs
        id3 = Task.generate_id("Different Task", sources)
        self.assertNotEqual(id1, id3)
        
        # Order of sources shouldn't matter
        id4 = Task.generate_id(title, ["file2.txt", "file1.txt"])
        self.assertEqual(id1, id4)


class TestTaskStateManager(unittest.TestCase):
    """Test task state persistence and merging."""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.state_manager = TaskStateManager(self.temp_dir)
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_save_and_load_tasks(self):
        """Test saving and loading tasks."""
        # Create test tasks
        tasks = [
            Task(
                id="test1",
                title="Test Task 1",
                type=TaskType.PROCESS,
                status=TaskStatus.PENDING,
                due=date.today()
            ),
            Task(
                id="test2",
                title="Test Task 2",
                type=TaskType.OPPORTUNITY,
                status=TaskStatus.DONE,
                last_completed=date.today()
            )
        ]
        
        # Save tasks
        self.state_manager.save_tasks(tasks)
        
        # Load tasks
        loaded_tasks = self.state_manager.load_existing_tasks()
        
        # Verify
        self.assertEqual(len(loaded_tasks), 2)
        self.assertIn("test1", loaded_tasks)
        self.assertIn("test2", loaded_tasks)
        
        task1 = loaded_tasks["test1"]
        self.assertEqual(task1.title, "Test Task 1")
        self.assertEqual(task1.status, TaskStatus.PENDING)
        self.assertEqual(task1.due, date.today())
    
    def test_idempotent_merging(self):
        """Test that merging is idempotent."""
        # Create initial task
        initial_task = Task(
            id="test1",
            title="Initial Task",
            type=TaskType.PROCESS,
            status=TaskStatus.PENDING,
            notes="Initial note"
        )
        
        # Save initial state
        self.state_manager.save_tasks([initial_task])
        existing_tasks = self.state_manager.load_existing_tasks()
        
        # Create "new" task with same ID but different title
        new_task = Task(
            id="test1",
            title="Updated Task",
            type=TaskType.PROCESS,
            status=TaskStatus.PENDING,
            source=["same_file.txt"]  # Same source
        )
        
        # Merge
        merged = self.state_manager.merge_tasks([new_task], existing_tasks)
        
        # Should preserve existing state
        merged_task = merged[0]
        self.assertEqual(merged_task.notes, "Initial note")
        self.assertEqual(merged_task.title, "Initial Task")  # Should keep existing title
    
    def test_task_status_update(self):
        """Test updating task status."""
        # Create and save initial task
        task = Task(
            id="test1",
            title="Test Task",
            type=TaskType.PROCESS,
            status=TaskStatus.PENDING
        )
        self.state_manager.save_tasks([task])
        
        # Update status
        success = self.state_manager.update_task_status("test1", TaskStatus.DONE, "Completed successfully")
        self.assertTrue(success)
        
        # Verify update
        loaded_tasks = self.state_manager.load_existing_tasks()
        updated_task = loaded_tasks["test1"]
        self.assertEqual(updated_task.status, TaskStatus.DONE)
        self.assertEqual(updated_task.notes, "Completed successfully")
        self.assertEqual(updated_task.last_completed, date.today())
        
        # Test updating non-existent task
        success = self.state_manager.update_task_status("nonexistent", TaskStatus.DONE)
        self.assertFalse(success)


class TestRecurrenceCalculation(unittest.TestCase):
    """Test recurrence date calculations."""
    
    def test_daily_recurrence(self):
        """Test daily recurrence calculation."""
        from classifier import RulesEngine
        from models import CadenceRule
        
        config = get_default_config()
        engine = RulesEngine(config)
        
        rule = CadenceRule(
            recurrence=Recurrence.DAILY,
            rule="Daily task"
        )
        
        next_due = engine._calculate_next_due_date(rule)
        expected = date.today() + timedelta(days=1)
        self.assertEqual(next_due, expected)
    
    def test_weekly_recurrence(self):
        """Test weekly recurrence calculation."""
        from classifier import RulesEngine
        from models import CadenceRule
        
        config = get_default_config()
        engine = RulesEngine(config)
        
        rule = CadenceRule(
            recurrence=Recurrence.WEEKLY,
            rule="Weekly task"
        )
        
        next_due = engine._calculate_next_due_date(rule)
        expected = date.today() + timedelta(days=7)
        self.assertEqual(next_due, expected)
    
    def test_due_after_days(self):
        """Test due_after_days calculation."""
        from classifier import RulesEngine
        from models import CadenceRule
        
        config = get_default_config()
        engine = RulesEngine(config)
        
        rule = CadenceRule(
            due_after_days=5,
            rule="Task due in 5 days"
        )
        
        next_due = engine._calculate_next_due_date(rule)
        expected = date.today() + timedelta(days=5)
        self.assertEqual(next_due, expected)


class TestDeduplication(unittest.TestCase):
    """Test file deduplication logic."""
    
    def test_content_hash_deduplication(self):
        """Test that files with same content hash are treated as same item."""
        from ingest import FileDiscovery
        from models import FileInfo
        
        config = get_default_config()
        discovery = FileDiscovery(config)
        
        # Create two FileInfo objects with same content hash
        file1 = FileInfo(
            path="folder1/document.txt",
            size=1000,
            created=date.today(),
            modified=date.today(),
            extension=".txt",
            folder="folder1",
            content_hash="abc123"
        )
        
        file2 = FileInfo(
            path="folder2/document.txt",  # Different path
            size=1000,
            created=date.today(),
            modified=date.today(),
            extension=".txt",
            folder="folder2",
            content_hash="abc123"  # Same hash
        )
        
        # They should be considered the same file
        self.assertEqual(file1.content_hash, file2.content_hash)
    
    def test_task_id_stability_across_path_changes(self):
        """Test that task IDs remain stable when file paths change but content doesn't."""
        # Create task with original path
        original_sources = ["old_folder/document.txt"]
        task_id_1 = Task.generate_id("Process Document", original_sources)
        
        # Create task with new path but same content (would have same hash)
        # In real scenario, the task manager would detect this via content hash
        new_sources = ["new_folder/document.txt"]
        task_id_2 = Task.generate_id("Process Document", new_sources)
        
        # IDs will be different because paths are different
        # But in real implementation, content hash matching would preserve the task
        self.assertNotEqual(task_id_1, task_id_2)
        
        # However, if we use the same source list, ID should be stable
        task_id_3 = Task.generate_id("Process Document", original_sources)
        self.assertEqual(task_id_1, task_id_3)


if __name__ == "__main__":
    unittest.main()
