"use client";

import addScrollspy from "@/utlis/addScrollSpy";
import { init_classic_menu_resize } from "@/utlis/menuToggle";
import { closeMobileMenu } from "@/utlis/toggleMobileMenu";
import dynamic from "next/dynamic";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

const scrollToElement = dynamic(() => import("@/utlis/scrollToElement"), {
  ssr: false,
});

export default function OnePageNav({ links, animateY = false }) {
  const [role, setRole] = useState();
  const [navbarLink, setNavbarLink] = useState(links);
  useEffect(() => {
    setRole(localStorage?.getItem("role"));
  }, []);
  useEffect(() => {
    setTimeout(() => {
      scrollToElement();
    }, 1000);
    init_classic_menu_resize();
    window.addEventListener("scroll", addScrollspy);

    window.addEventListener("resize", init_classic_menu_resize);

    return () => {
      window.removeEventListener("scroll", addScrollspy);
      window.removeEventListener("resize", init_classic_menu_resize);
    };
  }, []);
  useEffect(() => {
    setNavbarLink(links);
  }, [links, role]);
  const pathname = usePathname();

  return (
    <>
      {navbarLink[0].href?.includes("/") &&
        navbarLink.map((link, index) => {
          return (
            (!link?.type || link?.type === role) && (
              <li
                key={index}
                onClick={() => {
                  closeMobileMenu();
                  if (link?.isPublic) {
                    window.location.href = `${link.href}`;
                  }
                }}
              >
                <Link
                  className={
                    pathname.split("/")[1] == link.href.split("/")[1]
                      ? "active"
                      : ""
                  }
                  href={link.href}
                >
                  {animateY ? (
                    <span className="btn-animate-y">
                      <span className="btn-animate-y-1">{link.text}</span>
                      <span className="btn-animate-y-2" aria-hidden="true">
                        {link.text}
                      </span>
                    </span>
                  ) : (
                    link.text
                  )}
                </Link>
              </li>
            )
          );
        })}
      {!links[0].href?.includes("/") &&
        links.map((link, index) => (
          <li className="scrollspy-link" key={index}>
            <a onClick={() => closeMobileMenu()} className="" href={link.href}>
              {animateY ? (
                <span className="btn-animate-y">
                  <span className="btn-animate-y-1">{link.text}</span>
                  <span className="btn-animate-y-2" aria-hidden="true">
                    {link.text}
                  </span>
                </span>
              ) : (
                link.text
              )}
            </a>
          </li>
        ))}
    </>
  );
}
