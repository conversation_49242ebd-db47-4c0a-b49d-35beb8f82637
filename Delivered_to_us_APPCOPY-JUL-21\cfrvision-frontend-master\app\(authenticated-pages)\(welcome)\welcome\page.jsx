import Loading from "@/components/common/Loading";
import dynamic from "next/dynamic";
import React from "react";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  {
    ssr: false, // This disables SSR for this component
  }
);

const CFRVision = dynamic(
  () => import("@/app/(guest-pages)/(cfrvision)/main-pages-cfrvision/page"),
  {
    ssr: false, // This disables SSR for this component
    loading: () => <Loading/>
  }
);

const Welcome = () => {
  
  return (
    <main id="main">
      <section className="page-section pt-0 pb-0">
        <ParallaxContainer className="page-section bg-gray-light-1 parallax-5">
          <CFRVision />
          {/* <h4 color="#000" className="text-center">
            Welcome to ACFR Evaluation System
          </h4>
          <p className="text-center">
            Your comprehensive solution for ACFR report analysis and validation
          </p> */}
        </ParallaxContainer>
      </section>
    </main>
  );
};

export default Welcome;
