"use client";

import { valibotResolver } from "@hookform/resolvers/valibot";
import { useState } from "react";
import { <PERSON><PERSON>, Col, Modal, Row } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";

const schema = v.object({
  files: v.file("Please select a PDF file."),
  fy_end: v.pipe(v.string(), v.nonEmpty("FY End is required")),
  draft_final: v.pipe(v.string(), v.nonEmpty("Please select an option")),
  version: v.pipe(
    v.string(),
    v.regex(
      /^\d+(\.\d+)*$/,
      "Version should be a valid format (e.g., 1.0, 1.0.0, 2.1)"
    )
  ),
  comments: v.pipe(v.string(), v.minLength(1, "Comments cannot be empty")),
});

const userId = "16BlC9Uc1XMaY10YQ";

const UploadNewFilesModal = ({
  show,
  setShow,
  fetchFiles,
  onFileUploaded,
  isView,
  data,
}) => {
  const { contextHolder, showToast } = Toaster();

  const [uploading, setUploading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      fy_end: data?.fy_end || "",
      draft_final: data?.draft_final || "Draft",
      version: data?.version || "",
      comments: data?.comment || "",
      files: null,
    },
  });

  const handleFileChange = (e, onChange) => {
    const file = e.target.files[0];
    if (file && file.type === "application/pdf") {
      onChange(file); // Set the file as the form value
    } else {
      showToast({
        type: "error",
        message: "Only PDF files are allowed.",
      });
    }
  };

  const onSubmit = async (data) => {
    setUploading(true);
    console.log("Data", data);
    try {
      const formData = new FormData();
      formData.append("file", data.files);
      formData.append("user_id", userId);
      formData.append("fy_end", data.fy_end);
      formData.append("draft_final", data.draft_final);
      formData.append("version", data.version);
      formData.append("comment", data.comments);

      const response = await axiosInstance.post(
        "/v1/file/upload_file",
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      console.log("response", response, "&&", response.data.message);
      onFileUploaded(response.data.message || "File uploaded successfully!");
      fetchFiles();
      setUploading(false);
      setShow(false);
    } catch (error) {
      setUploading(false);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      <Modal show={show} onHide={() => setShow(false)} centered size="lg">
        <form
          noValidate
          onSubmit={handleSubmit(onSubmit)}
          className="form contact-form"
        >
          <Modal.Header closeButton>
            <Modal.Title>
              {isView ? "View File" : "Upload New File"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Row>
              {!isView && (
                <Col>
                  <div className="form-group">
                    <label htmlFor="fileInput" className="btn btn-primary">
                      Select File
                    </label>
                    <Controller
                      name="files"
                      control={control}
                      render={({ field }) => (
                        <>
                          <input
                            id="fileInput"
                            type="file"
                            accept=".pdf"
                            style={{ display: "none" }}
                            onChange={(e) =>
                              handleFileChange(e, field.onChange)
                            }
                            onBlur={field.onBlur}
                            ref={field.ref}
                            disabled={uploading || isView}
                          />
                          {field.value && (
                            <div className="mt-1">
                              <strong>{field.value.name}</strong>
                            </div>
                          )}
                        </>
                      )}
                    />
                    {errors?.files && (
                      <div className="text-red mt-2">
                        {errors.files.message}
                      </div>
                    )}
                  </div>
                </Col>
              )}
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="fy_end">FY End</label>
                  <Controller
                    name="fy_end"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        id="fy_end"
                        className="input-md round form-control"
                        placeholder="Enter FY End"
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.fy_end && (
                    <span className="text-red">{errors.fy_end.message}</span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label>Draft/Final</label>
                  <Controller
                    name="draft_final"
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="input-md round form-control"
                        disabled={isView}
                      >
                        <option value="Draft">Draft</option>
                        <option value="Final">Final</option>
                      </select>
                    )}
                  />
                  {errors?.draft_final && (
                    <span className="text-red">
                      {errors.draft_final.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label>Version</label>
                  <Controller
                    name="version"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        id="version"
                        className="input-md round form-control"
                        placeholder="Enter version"
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.version && (
                    <span className="text-red">{errors.version.message}</span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label>Comments</label>
                  <Controller
                    name="comments"
                    control={control}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        id="comments"
                        className="input-md round form-control"
                        placeholder="Enter comments"
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.comments && (
                    <span className="text-red">{errors.comments.message}</span>
                  )}
                </div>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShow(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={uploading || isView}
            >
              {uploading ? "Uploading..." : "Upload"}
            </Button>
          </Modal.Footer>
        </form>
      </Modal>
    </>
  );
};

export default UploadNewFilesModal;
