# The File contains  methods that handle exceptions in whole project

class ExceptionHandling:
    """
    class to lof exception and send message to user
    """

    def __init__(self, function_name, e):
        self.function_name = function_name
        self.e = e

    def exception_handling(self):
        """
        function to call methods to handle exception
        :return: dict contain exception details
        """
        return {"class-method": self.function_name, "exception_msg": self.e}
