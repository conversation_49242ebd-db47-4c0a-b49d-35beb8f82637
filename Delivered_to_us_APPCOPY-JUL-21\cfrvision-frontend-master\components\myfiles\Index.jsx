"use client";
import TableWithPagination from "@/components/common/TableWithPagination/page";
import React, { useEffect, useState } from "react";
import UploadNewFilesModal from "./UploadNewFilesModal";
import { Icon } from "@iconify/react";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import DeleteFileModal from "../common/DeleteModal";
import Toaster from "../common/Toaster";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const MyFiles = () => {
  const [show, setShow] = useState(false);
  const [isView, setIsView] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState(false);
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fileData, setFileData] = useState({});
  const [searchText, setSearchText] = useState("");
  const { contextHolder, showToast } = Toaster();

  const columns = [
    {
      headerName: "FILE NAME",
      valueGetter: (p) => p.data.file_name,
      flex: 2,
    },
    {
      headerName: "TYPE",
      valueGetter: (p) => p.data.draft_final,
      flex: 2,
    },
    {
      headerName: "VERSION",
      valueGetter: (p) => p.data.version,
      flex: 2,
    },
    {
      headerName: "STATUS",
      valueGetter: (p) => p.data.status,
      flex: 2,
      cellRenderer: (params) => {
        const status = params.value;

        return (
          <div
            className={`badge badge-${
              status === "Complete" ? "complete" : "draft"
            }`}
          >
            {status}
          </div>
        );
      },
    },
    {
      field: "SIZE",
      valueFormatter: (p) => p.data.size,
      flex: 2,
    },
    // {
    //   headerName: "FINANCIAL YEAR",
    //   valueGetter: (p) => p.data.fy_end,
    //   flex: 2,
    // },
    // {
    //   headerName: "NO OF PAGES",
    //   valueGetter: (p) => p.data.pages,
    //   flex: 2,
    // },
    {
      field: "UPLOAD DATE",
      valueFormatter: (p) => p.data.upload_date,
      flex: 2,
    },
    {
      headerName: "ACTIONS",
      field: "actions",
      valueGetter: (p) => p.data.id,
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="mdi-eye"
            width={24}
            height={24}
            className="cursor-pointer"
            cursor={"pointer"}
            onClick={() => {
              setShow(true);
              setIsView(true);
              setFileData(params.data);
            }}
          />
          <Icon
            icon="mdi:download"
            width="24"
            height="24"
            color="blue"
            cursor={"pointer"}
            onClick={() => {
              getFilePath(params.value);
            }}
          />
          <Icon
            icon="mdi:trash-outline"
            width="24"
            height="24"
            color="red"
            cursor={"pointer"}
            onClick={() => {
              setShowDeleteModal(true);
              setDeleteId(params.value);
            }}
          />
        </div>
      ),
    },
  ];

  const getFilePath = async (id) => {
    setLoading(true);
    try {
      const response = await axiosInstance.post("/v1/file/get-file-path", {
        id,
      });
      window.open(
        response?.data?.data?.file_path,
        "_blank",
        "noopener,noreferrer"
      );
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFiles = async (
    page_no = 1,
    page_size = 10,
    search = searchText || ""
  ) => {
    setLoading(true);
    try {
      const requestBody = {
        // id: 1,
        page_no,
        page_size,
        is_pagination: true,
        search_text: search,
      };
      const response = await axiosInstance.post(
        "/v1/file/my-files",
        requestBody
      );
      console.log("Res", response);
      setFiles(response.data.data || []);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();

    // Set interval for every 30sec
    const interval = setInterval(fetchFiles, 30000);

    // Cleanup on unmount
    return () => clearInterval(interval);
  }, []);

  const handleSearchChange = (value) => {
    setSearchText(value);
    fetchFiles(1, 10, value);
  };

  const handlePaginationChange = (page_no, page_size) => {
    fetchFiles(page_no, page_size);
  };

  const onFileUploaded = (successMsg) => {
    showToast({
      type: "success",
      message: successMsg,
    });
  };

  const onFileDeleted = (deleteMsg) => {
    showToast({
      type: "success",
      message: deleteMsg,
    });
  };

  const onFileDeletedError = (deleteMsg) => {
    showToast({
      type: "error",
      message: deleteMsg,
    });
  };

  return (
    <>
      {contextHolder}
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="d-flex align-items-center justify-content-between ">
              <h5 className="page-title text-center mb-0">My Files</h5>
              <button
                className="btn btn-mod btn-color btn-small btn-circle btn-hover-anim mb-xs-10"
                id="upload-button"
                onClick={() => {
                  setShow(true);
                  setIsView(false);
                  setFileData({});
                }}
              >
                <span>Upload Files</span>
              </button>
              <input type="file" style={{ display: "none" }} multiple />
            </div>
            <TableWithPagination
              data={files}
              columns={columns}
              onPaginationChange={handlePaginationChange}
              loading={files?.length ? false : loading}
              onSearchChange={handleSearchChange}
            />
          </ParallaxContainer>
        </section>
      </main>
      {show && (
        <UploadNewFilesModal
          show={show}
          setShow={setShow}
          fetchFiles={fetchFiles}
          onFileUploaded={onFileUploaded}
          isView={isView}
          data={fileData}
        />
      )}
      {showDeleteModal && (
        <DeleteFileModal
          show={showDeleteModal}
          setShow={setShowDeleteModal}
          refetch={fetchFiles}
          id={deleteId}
          apiUrl="/v1/file/delete_file"
          name="File"
          onDeleted={onFileDeleted}
          onDeletedError={onFileDeletedError}
        />
      )}
    </>
  );
};

export default MyFiles;
