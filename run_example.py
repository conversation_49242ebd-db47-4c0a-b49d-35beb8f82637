#!/usr/bin/env python3
"""
Example script to run the Startup Ops Indexer on the current directory.
This demonstrates how to use the system with your business files.
"""
import os
import sys
from pathlib import Path

def main():
    """Run the indexer on the current directory."""
    current_dir = Path.cwd()
    
    print("🚀 Startup Ops Indexer - Example Run")
    print(f"📁 Scanning directory: {current_dir}")
    print()
    
    # Check if we're in a directory with business files
    business_folders = [
        "AWS", "Branding", "Chargebee", "Delivered_to_us_APPCOPY-JUL-21",
        "Openxcell", "Possible_Grants_For _Us", "Pricing_tiers_to_Customers",
        "Required_Compliance and Security", "Week 1 Accomplished", 
        "Week 2 Accomplished", "Week 3 Accomplished", "Week 4 Accomplished"
    ]
    
    found_folders = [folder for folder in business_folders if (current_dir / folder).exists()]
    
    if found_folders:
        print(f"✅ Found {len(found_folders)} business folders:")
        for folder in found_folders[:5]:  # Show first 5
            print(f"   📂 {folder}")
        if len(found_folders) > 5:
            print(f"   ... and {len(found_folders) - 5} more")
        print()
    else:
        print("ℹ️  No recognized business folders found in current directory.")
        print("   The indexer will still process all files it finds.")
        print()
    
    # Import and run the indexer
    try:
        from ops_indexer import StartupOpsIndexer
        
        # Create indexer instance
        indexer = StartupOpsIndexer(
            root_path=str(current_dir),
            config_path="config.yml",
            output_dir=".ops_out",
            enable_ocr=False  # Disable OCR for faster processing
        )
        
        # Run the pipeline
        indexer.run_full_pipeline()
        
        print()
        print("🎉 Success! Check these files:")
        print(f"   🌐 Dashboard: {current_dir / '.ops_out' / 'dashboard.html'}")
        print(f"   📋 Documents: {current_dir / '.ops_out' / 'documents.txt'}")
        print(f"   ⚙️  Processes: {current_dir / '.ops_out' / 'processes.txt'}")
        print(f"   💡 Opportunities: {current_dir / '.ops_out' / 'opportunities.txt'}")
        print()
        print("💡 Next steps:")
        print("   1. Open dashboard.html in your browser")
        print("   2. Use 'python ops_cli.py summary' to see task overview")
        print("   3. Use 'python ops_cli.py list' to see all tasks")
        
    except ImportError as e:
        print(f"❌ Error importing modules: {e}")
        print("   Make sure you've installed the requirements:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error running indexer: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
