from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import llm_check_request_router
from app.database import get_db
from app.operations.ai_checks_operations.ai_checks_operations import AIChecks
from app.operations.category_operations.category_operations import AICheckCategory
from app.operations.llm_check_operations.llm_check_operations import LLMCheckRequest
from app.schemas.common import FetchSchemaSearchFilter, FetchSchemaSearch
from app.schemas.llm_check import LLMCheckRequestCreate, LLMCheckRequestDelete, LLMCheckID
from app.utils.jwt_authentication.jwt_handler import JWTBearer


@llm_check_request_router.post("/get-llm-check-request")
async def get_llm_check_request_api(data: FetchSchemaSearchFilter,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    """
    Retrieve LLM check requests based on the given filter criteria.

    This endpoint fetches LLM check requests based on the provided search filter data.

    **Args**:
        data (FetchSchemaSearchFilter): The search filter criteria for fetching LLM check requests.
        db (AsyncSession): The database session used to query and retrieve LLM check request data.

    **Returns**:
        JSON: A list of LLM check requests that match the filter criteria.
    """
    return await LLMCheckRequest(db).get_llm_check_request(data, user_id=user.get('user_id'))


@llm_check_request_router.post("/create-llm-check-request")
async def create_llm_check_request_api(data: LLMCheckRequestCreate,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    """
    Create a new LLM check request.

    This endpoint creates a new LLM check request entry in the database based on the provided input data.

    **Args**:
        data (LLMCheckRequestCreate): The schema containing the necessary information to create an LLM check request.
        db (AsyncSession): The database session used to create the LLM check request in the database.

    **Returns**:
        JSON: The created LLM check request data.
    """
    return await LLMCheckRequest(db).create_llm_check_request(data, user_id=user.get('user_id'))


@llm_check_request_router.post("/run-llm-check-request")
async def run_llm_check_request_api(data: LLMCheckID,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    # Needs request_id,
    """
    Create a new LLM check request.

    This endpoint creates a new LLM check request entry in the database based on the provided input data.

    **Args**:
        data (LLMCheckRequestCreate): The schema containing the necessary information to create an LLM check request.
        db (AsyncSession): The database session used to create the LLM check request in the database.

    **Returns**:
        JSON: The created LLM check request data.
    """
    return await LLMCheckRequest(db).re_run_llm_check_request(data, user_id=user.get('user_id'))

@llm_check_request_router.post("/get-ai-checks", dependencies=[Depends(JWTBearer())])
async def list_ai_checks_api(db: AsyncSession = Depends(get_db)):
    """
    Fetch AI checks based on the given filter criteria.

    This endpoint retrieves AI checks based on the provided search filter data.

    **Args**:
        data (FetchSchemaSearchFilter): The search filter criteria for fetching AI checks.
        db (AsyncSession): The database session used to query and retrieve AI check data.

    **Returns**:
        JSON: A list of AI checks that match the filter criteria.
    """
    data = FetchSchemaSearchFilter(filters=[],page_no=1,page_size=10, is_pagination=False, search_text="")
    return await AIChecks(db).ai_checks(data)

@llm_check_request_router.post("/get-ai-check-category", dependencies=[Depends(JWTBearer())])
async def list_ai_check_category_api(db: AsyncSession = Depends(get_db)):
    """
    Fetch AI checks based on the given filter criteria.

    This endpoint retrieves AI checks based on the provided search filter data.

    **Args**:
        data (FetchSchemaSearchFilter): The search filter criteria for fetching AI checks.
        db (AsyncSession): The database session used to query and retrieve AI check data.

    **Returns**:
        JSON: A list of AI checks that match the filter criteria.
    """
    data = FetchSchemaSearch(page_no=1,page_size=10, is_pagination=False, search_text="")
    return await AICheckCategory(db).get_ai_check_categories(data)


@llm_check_request_router.post("/delete-llm-check-request")
async def delete_llm_check_request_api(data: LLMCheckRequestDelete,user: dict = Depends(JWTBearer()),  db: AsyncSession = Depends(get_db)):
    return await LLMCheckRequest(db).delete_llm_check_request(data, user_id=user.get('user_id'))

@llm_check_request_router.post("/download-result")
async def download_result_llm_check_request_api(data: LLMCheckID,user: dict = Depends(JWTBearer()),  db: AsyncSession = Depends(get_db)):
    return await LLMCheckRequest(db).download_result_llm_check_request_api(data, user_id=user.get('user_id'))


@llm_check_request_router.post("/available-models", dependencies=[Depends(JWTBearer())])
async def get_available_models(db: AsyncSession = Depends(get_db)):
    return await LLMCheckRequest(db).get_list_of_models()
