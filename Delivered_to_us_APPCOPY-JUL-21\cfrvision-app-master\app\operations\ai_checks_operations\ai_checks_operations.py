import logging
import time

from fastapi import Depends
from fastapi.responses import JSONResponse
from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import noload

from app import logger
from app.database import get_db
from app.database.models import <PERSON><PERSON>heck, AiCheckCategory
from app.utils.constants import (checks_categories_fetched, retrieve_checks_failed, check_name_exists,
                                 add_check_success, add_check_failed, check_not_found, check_id_required,
                                 check_deactivated, check_update_success, check_updated_failed, check_delete_success,
                                 check_delete_failed, check_delete_rejected, dependent_llm_check_exists)


class AIChecks:

    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def ai_checks(self, data):
        """
        Retrieve AI checks and their associated categories based on provided filters.

        This method fetches AI checks and their categories from the database, optionally filtering them
        based on the provided filter criteria and search text.

        **Args**:
            data (FetchSchemaSearchFilter): The filter and search criteria for retrieving AI checks and categories.

        **Returns**:
            JSONResponse: A response containing the list of AI checks and categories that match the filter criteria.
        """
        try:
            # Retrieve all AI checks with their categories
            stmt_checks = select(AiCheck, AiCheckCategory).join(AiCheckCategory).options(noload(AiCheck.llm_check_requests)).where(
                AiCheck.is_deleted == False)
            if len(data.filters) > 0:
                filter_conditions = []
                for filter_entity in data.filters:
                    if filter_entity.filter_column.lower() in [column.name.lower() for column in
                                                               AiCheck.__table__.columns]:
                        if filter_entity.filter_column.lower() == "category_id":
                            filter_conditions.append(
                                getattr(AiCheck, filter_entity.filter_column) == int(filter_entity.filter_text))
                        elif filter_entity.filter_column.lower() == "status":
                            filter_conditions.append(AiCheck.is_active == True if filter_entity.filter_text.lower()=='active' else False)
                        else:
                            filter_conditions.append(
                                getattr(AiCheck, filter_entity.filter_column) == filter_entity.filter_text)
                    elif filter_entity.filter_column.lower() in [column.name.lower() for column in
                                                                 AiCheckCategory.__table__.columns]:
                        filter_conditions.append(
                            getattr(AiCheckCategory, filter_entity.filter_column) == filter_entity.filter_text)
                stmt_checks = stmt_checks.filter(*filter_conditions)

            if data.search_text:
                stmt_checks = stmt_checks.filter(or_(AiCheck.check_name.ilike(f"%{data.search_text}%"),
                                                     AiCheckCategory.category_name.ilike(f"%{data.search_text}%")))
            start_time = time.perf_counter()
            result_checks = await self.db.execute(stmt_checks)
            checks = result_checks.scalars().all()
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            print(f"Query execution for ai check took {execution_time:.4f} seconds")
            start_time = time.perf_counter()
            stmt_categories = select(AiCheckCategory).options(noload(AiCheckCategory.ai_checks)).where(AiCheckCategory.is_deleted==False)
            result_categories = await self.db.execute(stmt_categories)
            categories = result_categories.scalars().all()
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            print(f"Query execution for ai check category took {execution_time:.4f} seconds")
            # Format data for response
            start_time = time.perf_counter()
            formatted_checks = [
                {
                    "check_id": check.check_id,
                    "check_display_number": check.check_display_number,
                    "check_name": check.check_name,
                    "check_prompt": check.check_prompt,
                    "description": check.description,
                    "check_expected_result_format": check.check_expected_result_format,
                    "check_relevant_sections": check.check_relevant_sections,
                    "check_extended_description": check.check_extended_description,
                    "is_active": check.is_active,
                    "category_id": check.category_id,
                    "category_name": check.category.category_name if check.category else None,
                }
                for check in checks
            ]
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            print(f"Query execution for ai check category took {execution_time:.4f} seconds")
            start_time = time.perf_counter()
            formatted_categories = [
                {"category_id": category.category_id, "category_name": category.category_name}
                for category in categories
            ]
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            print(f"Query execution for ai check category took {execution_time:.4f} seconds")
            api_response = {
                "data": {"checks": formatted_checks, "categories": formatted_categories},
                "error": "",
                "message": checks_categories_fetched,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error retrieving AI Checks: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": retrieve_checks_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def create_ai_check(self, data):
        """
        Create a new AI check.

        This method creates a new AI check entry in the database after verifying that an AI check with the
        same name and category does not already exist.

        **Args**:
            data (AICheckCreateSchema): The data required to create a new AI check.

        **Returns**:
            JSONResponse: A response containing the ID of the newly created AI check.
        """
        try:
            # Check if AI Check with the same name already exists
            stmt = select(AiCheck).where(AiCheck.check_name == data.check_name,
                                         AiCheck.category_id == data.category_id, AiCheck.is_deleted == False)
            result = await self.db.execute(stmt)
            existing_check = result.scalar_one_or_none()

            if existing_check:
                api_response = {
                    "data": {},
                    "error": check_name_exists,
                    "message": check_name_exists,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)

            # Create a new AI Check
            check = AiCheck(
                check_display_number=data.check_display_number,
                check_name=data.check_name,
                check_prompt=data.check_prompt,
                category_id=data.category_id,
                description=data.description,
                check_expected_result_format=data.check_expected_result_format,
                check_relevant_sections=data.check_relevant_sections,
                check_extended_description=data.check_extended_description,
                is_active=data.is_active

            )
            self.db.add(check)
            await self.db.commit()

            api_response = {
                "data": {"id": check.check_id},
                "error": "",
                "message": add_check_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error creating AI Check: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": add_check_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def update_ai_check(self, data):
        """
        Update an existing AI check.

        This method updates an existing AI check in the database. It checks if the AI check exists and whether
        the updated check name is unique within the category. It also handles soft deletion by setting the
        `is_active` flag to False.

        **Args**:
            data (AICheckUpdateSchema): The data required to update the AI check.

        **Returns**:
            JSONResponse: A response confirming the update or providing an error message if the update fails.
        """
        try:
            if data.id:  # Update existing AI check
                stmt = select(AiCheck).where(AiCheck.check_id == data.id, AiCheck.is_deleted == False)
                result = await self.db.execute(stmt)
                check = result.scalar_one_or_none()

                if not check:
                    api_response = {
                        "data": {},
                        "error": check_not_found,
                        "message": check_not_found,
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=404)

                stmt_check_name = select(AiCheck).where(AiCheck.check_id != data.id,
                                                        AiCheck.check_name == data.check_name,
                                                        AiCheck.category_id == data.category_id,
                                                        AiCheck.is_deleted == False,
                                                        AiCheck.is_active == data.is_active
                                                        )
                result = await self.db.execute(stmt_check_name)
                exist_check = result.scalar_one_or_none()

                if not data.is_active:  # Soft delete (set is_active to False)
                    message = check_deactivated
                else:
                    if exist_check:
                        api_response = {
                            "data": {},
                            "error": check_name_exists,
                            "message": check_name_exists,
                            "status": False,
                        }
                        return JSONResponse(content=api_response, status_code=400)

                # Update existing check data
                check.check_display_number = data.check_display_number
                check.check_name = data.check_name
                check.check_prompt = data.check_prompt
                check.category_id = data.category_id
                check.is_active = data.is_active
                check.check_relevant_sections = data.check_relevant_sections
                check.check_expected_result_format=data.check_expected_result_format
                message = check_update_success

                await self.db.commit()

                api_response = {
                    "data": {"id": check.check_id},
                    "error": "",
                    "message": message,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)

            else:
                api_response = {
                    "data": {},
                    "error": check_id_required,
                    "message": check_id_required,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)

        except Exception as e:
            logger.exception(f"Error updating AI Check: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": check_updated_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def delete_ai_check(self, data):
        """
        Delete an AI check.

        This method marks an AI check as deleted in the database, provided that no dependent LLM check requests
        exist. If dependent LLM check requests are found, deletion is rejected.

        **Args**:
            data (AICheckDeleteSchema): The data containing the ID of the AI check to be deleted.

        **Returns**:
            JSONResponse: A response confirming the deletion or providing an error message if the check cannot be deleted.
        """
        try:  # Update existing AI check
            stmt = select(AiCheck).where(AiCheck.check_id == data.id, AiCheck.is_deleted == False)
            result = await self.db.execute(stmt)
            check = result.scalar_one_or_none()

            if not check:
                api_response = {
                    "data": {},
                    "error": check_not_found,
                    "message": check_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            if len(check.llm_check_requests) > 0:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": f"{check_delete_rejected} {dependent_llm_check_exists}",
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
            # Delete existing check data
            check.is_deleted = True
            await self.db.commit()

            message = check_delete_success
            api_response = {
                "data": {"id": check.check_id},
                "error": "",
                "message": message,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error deleting AI Check: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": check_delete_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)


