import React, { useEffect } from "react";
import { Button, Col, Modal, Row } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import { valibotResolver } from "@hookform/resolvers/valibot";

const schema = v.object({
  notes: v.pipe(v.union([v.string(), v.literal(""), v.null_()])),
  status: v.pipe(v.string(), v.nonEmpty("Status is required")),
});

const ContactUsModal = ({ show, setShow, contactData, onSave }) => {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      notes: "",
      status: "",
    },
  });
  useEffect(() => {
    setValue("status", contactData?.status);
    setValue("notes", contactData?.notes);
  }, [contactData, show, setValue]);

  const onSubmit = async (data) => {
    try {
      await onSave({ ...data, id: contactData?.id });
      setShow(false); // Close the modal after saving
    } catch (error) {
      console.log("Error saving category:", error);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  return (
    <Modal show={show} onHide={() => setShow(false)} centered size="lg">
      <form
        noValidate
        autoComplete="off"
        onSubmit={handleSubmit(onSubmit)}
        className="form contact-form"
      >
        <Modal.Header closeButton>
          <Modal.Title className="text-center">
            Update Contact Us Status
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Row>
            <Col md={12}>
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <Controller
                  name="status"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <input
                      {...field}
                      name="status"
                      id="status"
                      className="input-md round form-control"
                      placeholder="Enter status"
                      required
                    />
                  )}
                />
                {errors?.status && (
                  <span className="text-red">{errors?.status.message}</span>
                )}
              </div>
            </Col>
            <Col md={12}>
              <div className="form-group">
                <label htmlFor="notes">Notes</label>
                <Controller
                  name="notes"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      name="notes"
                      id="notes"
                      className="input-md round form-control"
                      placeholder="Enter notes"
                      required
                    />
                  )}
                />
                {errors?.notes && (
                  <span className="text-red">{errors?.notes.message}</span>
                )}
              </div>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShow(false)}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            Update
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default ContactUsModal;
