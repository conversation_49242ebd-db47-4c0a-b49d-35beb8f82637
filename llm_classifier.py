"""
LLM-powered classification and task extraction for enhanced intelligence.
"""
import json
import logging
from typing import List, Dict, Optional, Set
from datetime import date, datetime
import re

from models import FileInfo, Task, TaskType, Priority

logger = logging.getLogger(__name__)

# Optional LLM imports - gracefully handle missing dependencies
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False


class LLMClassifier:
    """Enhanced file classification using LLM intelligence."""
    
    def __init__(self, provider: str = "openai", api_key: Optional[str] = None, 
                 model: str = None, max_tokens: int = 500):
        self.provider = provider.lower()
        self.api_key = api_key
        self.max_tokens = max_tokens
        
        # Set default models
        if model is None:
            if self.provider == "openai":
                self.model = "gpt-3.5-turbo"
            elif self.provider == "anthropic":
                self.model = "claude-3-haiku-20240307"
            else:
                self.model = "gpt-3.5-turbo"
        else:
            self.model = model
        
        self._setup_client()
    
    def _setup_client(self):
        """Setup the LLM client."""
        if self.provider == "openai" and OPENAI_AVAILABLE:
            if self.api_key:
                openai.api_key = self.api_key
            self.client = openai.OpenAI()
        elif self.provider == "anthropic" and ANTHROPIC_AVAILABLE:
            if self.api_key:
                self.client = anthropic.Anthropic(api_key=self.api_key)
            else:
                self.client = anthropic.Anthropic()  # Uses ANTHROPIC_API_KEY env var
        else:
            logger.warning(f"LLM provider {self.provider} not available or not configured")
            self.client = None
    
    def classify_file_content(self, file_info: FileInfo) -> Dict:
        """Use LLM to classify file content and extract insights."""
        if not self.client:
            return {"topics": [], "confidence": 0.0, "insights": "LLM not available"}
        
        try:
            prompt = self._build_classification_prompt(file_info)
            response = self._call_llm(prompt)
            return self._parse_classification_response(response)
        except Exception as e:
            logger.warning(f"LLM classification failed for {file_info.path}: {e}")
            return {"topics": [], "confidence": 0.0, "insights": f"Error: {str(e)[:100]}"}
    
    def extract_tasks_from_content(self, file_info: FileInfo) -> List[Dict]:
        """Extract actionable tasks from file content using LLM."""
        if not self.client:
            return []
        
        try:
            prompt = self._build_task_extraction_prompt(file_info)
            response = self._call_llm(prompt)
            return self._parse_task_response(response)
        except Exception as e:
            logger.warning(f"LLM task extraction failed for {file_info.path}: {e}")
            return []
    
    def _build_classification_prompt(self, file_info: FileInfo) -> str:
        """Build prompt for file classification."""
        return f"""
Analyze this business document and classify it into relevant categories.

File: {file_info.path}
Folder: {file_info.folder}
Content: {file_info.extracted_text[:1000]}

Classify into these business categories (select all that apply):
- expenses (receipts, invoices, bills, payments)
- grants (funding opportunities, applications, proposals)
- deliverables (completed work, milestones, submissions)
- branding (logos, marketing materials, design)
- compliance (legal, security, regulations, requirements)
- pricing (cost structures, subscription plans, tiers)
- aws (cloud infrastructure, amazon services)
- development (code, software, programming, apps)
- business (strategy, planning, models, revenue)
- legal (contracts, agreements, incorporation)
- finance (accounting, banking, taxes, financial)
- opportunities (potential improvements, follow-ups)

Respond in JSON format:
{
  "topics": ["category1", "category2"],
  "confidence": 0.85,
  "insights": "Brief explanation of what this document is about and why it matters",
  "priority": "high|medium|low",
  "actionable": true/false
}
"""
    
    def _build_task_extraction_prompt(self, file_info: FileInfo) -> str:
        """Build prompt for task extraction."""
        return f"""
Analyze this business document and extract actionable tasks or follow-ups.

File: {file_info.path}
Folder: {file_info.folder}
Content: {file_info.extracted_text[:1000]}

Look for:
- Explicit tasks (TODO, action items, deadlines)
- Implicit tasks (things that need follow-up)
- Recurring processes (things done regularly)
- Opportunities (improvements, optimizations)

For each task found, respond in JSON format:
[
  {
    "title": "Clear, actionable task title",
    "description": "Why this task matters",
    "priority": "high|medium|low",
    "type": "process|opportunity|oneoff",
    "due_in_days": 7,
    "confidence": 0.8,
    "tags": ["tag1", "tag2"]
  }
]

If no clear tasks are found, return an empty array [].
"""
    
    def _call_llm(self, prompt: str) -> str:
        """Call the configured LLM with the prompt."""
        if self.provider == "openai":
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.max_tokens,
                temperature=0.1  # Low temperature for consistent results
            )
            return response.choices[0].message.content
        
        elif self.provider == "anthropic":
            response = self.client.messages.create(
                model=self.model,
                max_tokens=self.max_tokens,
                temperature=0.1,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        
        else:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")
    
    def _parse_classification_response(self, response: str) -> Dict:
        """Parse LLM classification response."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                return {
                    "topics": data.get("topics", []),
                    "confidence": data.get("confidence", 0.5),
                    "insights": data.get("insights", ""),
                    "priority": data.get("priority", "medium"),
                    "actionable": data.get("actionable", False)
                }
        except Exception as e:
            logger.warning(f"Failed to parse classification response: {e}")
        
        # Fallback parsing
        return {
            "topics": [],
            "confidence": 0.3,
            "insights": response[:200] if response else "No response",
            "priority": "medium",
            "actionable": False
        }
    
    def _parse_task_response(self, response: str) -> List[Dict]:
        """Parse LLM task extraction response."""
        try:
            # Extract JSON array from response
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                tasks = json.loads(json_match.group())
                return tasks if isinstance(tasks, list) else []
        except Exception as e:
            logger.warning(f"Failed to parse task response: {e}")
        
        return []


class EnhancedClassifier:
    """Combines rule-based and LLM classification for best results."""
    
    def __init__(self, config, llm_classifier: Optional[LLMClassifier] = None):
        self.config = config
        self.llm_classifier = llm_classifier
        
        # Import the original classifier
        from classifier import FileClassifier
        self.rule_classifier = FileClassifier(config)
    
    def classify_file(self, file_info: FileInfo) -> FileInfo:
        """Enhanced classification using both rules and LLM."""
        # Start with rule-based classification
        file_info = self.rule_classifier.classify_file(file_info)
        
        # Enhance with LLM if available
        if self.llm_classifier and file_info.extracted_text.strip():
            llm_result = self.llm_classifier.classify_file_content(file_info)
            
            # Merge topics
            llm_topics = set(llm_result.get("topics", []))
            rule_topics = set(file_info.topics)
            file_info.topics = list(rule_topics.union(llm_topics))
            
            # Update confidence and description
            if llm_result.get("confidence", 0) > 0.5:
                file_info.confidence = llm_result["confidence"]
                if llm_result.get("insights"):
                    file_info.description = llm_result["insights"][:120]
        
        return file_info
    
    def extract_enhanced_tasks(self, files: List[FileInfo]) -> List[Task]:
        """Extract tasks using LLM intelligence."""
        if not self.llm_classifier:
            return []
        
        tasks = []
        for file_info in files:
            if not file_info.extracted_text.strip():
                continue
            
            llm_tasks = self.llm_classifier.extract_tasks_from_content(file_info)
            
            for task_data in llm_tasks:
                if task_data.get("confidence", 0) > 0.6:  # Only high-confidence tasks
                    task = Task(
                        id=Task.generate_id(task_data["title"], [file_info.path]),
                        title=task_data["title"],
                        type=TaskType(task_data.get("type", "oneoff")),
                        priority=Priority(task_data.get("priority", "medium")),
                        source=[file_info.path],
                        notes=task_data.get("description", ""),
                        confidence=task_data.get("confidence", 0.7),
                        tags=task_data.get("tags", []),
                        due=date.today() + timedelta(days=task_data.get("due_in_days", 7)) if task_data.get("due_in_days") else None
                    )
                    tasks.append(task)
        
        return tasks
