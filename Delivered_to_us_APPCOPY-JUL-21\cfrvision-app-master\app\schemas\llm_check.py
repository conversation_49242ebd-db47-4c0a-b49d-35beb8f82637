from typing import Optional, List

from pydantic import BaseModel, Field, model_validator

from app.schemas.common import ObjectID


class LLMCheckID(ObjectID, BaseModel):
    pass

class LLMCheckResponses(BaseModel):
    id: int
    parsed_response: str
    created_at: int

    class Config:
        from_attributes = True
        orm_mode = True

class AICheckCategory(BaseModel):
    category_name: str
    class Config:
        from_attributes = True
        orm_mode = True

class AIChecksSchema(BaseModel):
    check_name: str
    category: AICheckCategory

    class Config:
        from_attributes = True
        orm_mode = True


# CheckCategories
class LLMCheckRequestResponse(BaseModel):
    request_id: Optional[int] = Field(
        None,
        description="Unique identifier of the request.",
        examples=[1, 101]
    )
    document_id: int = Field(
        None,
        description="Unique identifier of the document.",
        examples=[1, 101]
    )
    check_id: int = Field(
        None,
        description="Foreign key reference to check.",
        examples=[1, 101]
    )
    prompt: Optional[str] = Field(
        "",
        description="Order number for the category, must be greater than or equal to 1.",
        examples=["Prompt here."]
    )
    status: Optional[str] = Field(
        "",
        description="Order number for the category, must be greater than or equal to 1.",
        examples=["Status here."]
    )
    llm_provider: str
    model_used: str
    llm_check_response: List[LLMCheckResponses]
    ai_check: AIChecksSchema
    created_at: int

    class Config:
        from_attributes = True
        orm_mode = True

    # @model_validator(mode='before')
    # def set_llm_check_responses(self, values):
    #     try:
    #         values['llm_check_response'] = values.get('latest_llm_check_response')
    #     except Exception as e:
    #         logger.error(f"Error in set_llm_check_responses: {e}")
    #         pass
    #     return values


class LLMCheckRequestCreate(BaseModel):
    document_id: int
    check_id: int
    model_used: str


class LLMCheckRequestDelete(ObjectID, BaseModel):
    pass
