// import ReactQuill from "react-quill";
import dynamic from "next/dynamic";
import { useMemo } from "react";
import "react-quill/dist/quill.snow.css"; // Import Quill styles

export const TextEditor = ({ values, handleChange, readOnly, ...rest }) => {
  const ReactQuill = useMemo(
    () => dynamic(() => import("react-quill"), { ssr: false }),
    []
  );
  return (
    <>
      <ReactQuill
        theme="snow"
        value={values ?? ""}
        modules={{
          toolbar: [
            [{ header: "1" }, { header: "2" }, { font: [] }],
            [{ size: [] }],
            ["bold", "italic", "underline", "strike", "blockquote"],
            [
              { list: "ordered" },
              { list: "bullet" },
              { indent: "-1" },
              { indent: "+1" },
            ],

            ["clean"],
          ],
        }}
        readOnly={readOnly}
        {...rest}
      />
    </>
  );
};
