import hashlib
import json
import os
import secrets
import time

from fastapi import Depends
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import noload

from app import logger
from app.database import get_db
from app.database.models import User
from app.schemas.auth import UserResponseSchema
from app.utils.constants import (exception_occurred, user_email_not_found, user_incorrect_password, login_success,
                                 invalid_refresh_token_error, refresh_token_success, user_not_found,
                                 reset_pass_link_sent_success,
                                 token_expired, password_updated,
                                 reset_pass_link_verification_success, user_logout_success, user_logout_fail)
from app.utils.exception_handling import ExceptionHandling
from app.utils.jwt_authentication.jwt_handler import generate_auth_tokens, regenerate_access_token, \
    verify_password, get_password_hash
from app.utils.mail_utils import MailOperations

reset_exp_sec = int(os.getenv('FORGOT_PASS_TOKEN_EXP_SEC'))


class AuthOperations:
    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def login_operation(self, data):
        try:
            stmt_user = select(User).options(noload(User.files),noload(User.llm_check_requests)).where(User.email == data.email, User.is_deleted == False)
            result_data = await self.db.execute(stmt_user)
            user_obj = result_data.scalars().first()
            if not user_obj:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": user_email_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=401)
            current_time = int(time.time())
            if user_obj.role.value == 'user':
                if not user_obj.subscription_start or not user_obj.subscription_end:
                    # If the subscription start or end time is not set, treat it as invalid
                    api_response = {
                        "data": {},
                        "error": "",
                        "message": "Your subscription details are not set. Please try checking your subscription.",
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=401)
                if current_time > user_obj.subscription_end or current_time < user_obj.subscription_start:
                    # If the subscription is expired or not started, update the subscription details
                    api_response = {
                        "data": {},
                        "error": "",
                        "message": "Your subscription is either expired or not started yet. Please renew your subscription.",
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=401)
            if not verify_password(data.password, user_obj.password_hash):
                api_response = {
                    "data": {},
                    "error": "",
                    "message": user_incorrect_password,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=401)
            else:
                tokens = generate_auth_tokens(user_obj.id)
                user_obj.access_token = tokens.get('access_token')
                user_obj.refresh_token = tokens.get('refresh_token')
                await self.db.commit()

                user_response = json.loads(UserResponseSchema.model_validate(user_obj.__dict__).model_dump_json())
                api_response = {
                    "data": user_response,
                    "error": "",
                    "message": login_success,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="AuthOperations.login_operation").exception_handling()
            api_response = {
                "data": {},
                "error": "",
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def refresh_token_operation(self, user_id: str, token: str):
        try:
            auth_tokens = await regenerate_access_token(user_id, token, self.db)
            if auth_tokens:
                api_response = {
                    "data": auth_tokens,
                    "error": "",
                    "message": refresh_token_success,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:
                api_response = {
                    "data": {},
                    "error": invalid_refresh_token_error,
                    "message": invalid_refresh_token_error,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=403)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e),
                                      function_name="AuthOperations.refresh_token_operation").exception_handling()
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    @staticmethod
    def generate_reset_token():
        # Generate a random token
        token = secrets.token_hex(16)  # 16 bytes => 32 characters in hex
        # Optionally hash the token for security (to store in the database)
        hashed_token = hashlib.sha256(token.encode()).hexdigest()

        # Set an expiration time (e.g., 1 hour)
        expiration_time = time.time() + reset_exp_sec

        return {
            "token": token,
            "hashed_token": hashed_token,
            "expiration_time": expiration_time
        }

    async def forgot_password_operation(self, data, background_tasks):
        try:
            stmt_user = select(User).where(User.email == data.email, User.is_deleted == False)
            result_data = await self.db.execute(stmt_user)
            user_obj = result_data.scalars().first()
            if not user_obj:
                api_response = {
                    "data": {},
                    "error": user_not_found,
                    "message": user_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            token = self.generate_reset_token()
            user_obj.reset_token = token.get('hashed_token')
            user_obj.reset_token_exp = token.get('expiration_time')
            final_token =  token.get('token','') + f'_{user_obj.id}'
            background_tasks.add_task(MailOperations().send_otp_verification_mail, data.email,
                                      f"{user_obj.first_name} {user_obj.last_name}", final_token)
            await self.db.commit()
            api_response = {
                "data": {},
                "error": reset_pass_link_sent_success,
                "message": reset_pass_link_sent_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e),
                                      function_name="AuthOperations.forgot_password_operation").exception_handling()
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    @staticmethod
    def verify_token(input_token, stored_token, stored_exp):
        hashed_input_token = hashlib.sha256(input_token.encode()).hexdigest()

        if hashed_input_token != stored_token:
            return False, token_expired

        if time.time() > stored_exp:
            return False, token_expired

        return True, reset_pass_link_verification_success


    async def token_verification_operation(self, data, background_tasks):
        try:
            token, user_id = data.token.rsplit('_', 1)
            stmt_user = select(User).where(User.id == user_id, User.is_deleted == False)
            result_data = await self.db.execute(stmt_user)
            user_obj = result_data.scalars().first()
            if not user_obj:
                api_response = {
                    "data": {},
                    "error": user_not_found,
                    "message": user_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)

            token_flag, msg = self.verify_token(token, user_obj.reset_token, user_obj.reset_token_exp)
            if token_flag:
                api_response = {
                    "data": {'user_id':user_id},
                    "error": msg,
                    "message": msg,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:
                api_response = {
                    "data": {},
                    "error": msg,
                    "message": msg,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e),
                                      function_name="AuthOperations.token_verification_operation").exception_handling()
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def set_password_operation(self, data):
        try:
            stmt_user = select(User).where(User.id == data.user_id, User.is_deleted == False)
            result_data = await self.db.execute(stmt_user)
            user_obj = result_data.scalars().first()
            if not user_obj:
                api_response = {
                    "data": {},
                    "error": user_not_found,
                    "message": user_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            hashed_password = get_password_hash(data.password)
            user_obj.password_hash = hashed_password
            user_obj.reset_otp_exp = time.time()
            user_obj.access_token = ""
            user_obj.refresh_token = ""
            await self.db.commit()
            api_response = {
                "data": {},
                "error": password_updated,
                "message": password_updated,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e),
                                      function_name='AuthOperations.set_password_operation').exception_handling()
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def logout_operations(self, user_id):
        try:
            stmt_user = select(User).where(User.id == user_id, User.is_deleted == False)
            result_data = await self.db.execute(stmt_user)
            user_obj = result_data.scalars().first()
            if not user_obj:
                api_response = {
                    "data": {},
                    "error": user_not_found,
                    "message": user_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            user_obj.access_token = ""
            user_obj.refresh_token = ""
            await self.db.commit()
            api_response = {
                "data": {},
                "error": user_logout_success,
                "message": user_logout_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e),
                                      function_name='AuthOperations.logout_operations').exception_handling()
            api_response = {
                "data": {},
                "error": str(e),
                "message": user_logout_fail,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)
