<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Startup Ops Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 { font-size: 2rem; margin-bottom: 0.5rem; }
        .header .stats { display: flex; gap: 2rem; font-size: 0.9rem; }
        .stat { background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 2rem; }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            border: none;
            background: white;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .tab:hover { background: #f8f9fa; }
        .tab.active { background: #667eea; color: white; }
        
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .task-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #ddd;
            transition: transform 0.2s;
        }
        
        .task-card:hover { transform: translateY(-2px); }
        .task-card.high { border-left-color: #e74c3c; }
        .task-card.med { border-left-color: #f39c12; }
        .task-card.low { border-left-color: #27ae60; }
        
        .task-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .task-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.85rem;
            color: #7f8c8d;
        }
        
        .task-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.85rem;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-done { background: #27ae60; color: white; }
        .btn-done:hover { background: #229954; }
        
        .btn-snooze { background: #f39c12; color: white; }
        .btn-snooze:hover { background: #e67e22; }
        
        .btn-note { background: #3498db; color: white; }
        .btn-note:hover { background: #2980b9; }
        
        .priority-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-high { background: #fee; color: #e74c3c; }
        .priority-med { background: #fef9e7; color: #f39c12; }
        .priority-low { background: #eafaf1; color: #27ae60; }
        
        .type-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            background: #ecf0f1;
            color: #34495e;
        }
        
        .overdue { background: #fee !important; border-left-color: #e74c3c !important; }
        
        .task-notes {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
        }
        
        .empty-state h3 { margin-bottom: 1rem; }
        
        .calendar-mini {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        
        .refresh-btn:hover { transform: scale(1.1); }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Startup Ops Dashboard</h1>
        <div class="stats">
            <div class="stat">📋 {{ total_pending }} Pending</div>
            <div class="stat">🔥 {{ priority_counts.high }} High Priority</div>
            <div class="stat">⚠️ {{ overdue_tasks|length }} Overdue</div>
            <div class="stat">✅ {{ accomplished|length }} Recent Wins</div>
        </div>
    </div>

    <div class="container">
        <div class="tabs">
            <button class="tab active" onclick="showTab('today')">Today</button>
            <button class="tab" onclick="showTab('overdue')">Overdue</button>
            <button class="tab" onclick="showTab('week')">This Week</button>
            <button class="tab" onclick="showTab('opportunities')">Opportunities</button>
            <button class="tab" onclick="showTab('accomplished')">Accomplished</button>
        </div>

        <!-- Today Tab -->
        <div id="today" class="tab-content active">
            {% if today_tasks %}
                <div class="task-grid">
                    {% for task in today_tasks %}
                        {{ render_task_card(task) }}
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <h3>🎉 No tasks due today!</h3>
                    <p>Great job staying on top of things.</p>
                </div>
            {% endif %}
        </div>

        <!-- Overdue Tab -->
        <div id="overdue" class="tab-content">
            {% if overdue_tasks %}
                <div class="task-grid">
                    {% for task in overdue_tasks %}
                        {{ render_task_card(task, overdue=true) }}
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <h3>✨ Nothing overdue!</h3>
                    <p>You're all caught up.</p>
                </div>
            {% endif %}
        </div>

        <!-- This Week Tab -->
        <div id="week" class="tab-content">
            {% if this_week_tasks %}
                <div class="task-grid">
                    {% for task in this_week_tasks %}
                        {{ render_task_card(task) }}
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <h3>📅 Light week ahead!</h3>
                    <p>Perfect time to tackle some opportunities.</p>
                </div>
            {% endif %}
        </div>

        <!-- Opportunities Tab -->
        <div id="opportunities" class="tab-content">
            {% if opportunities %}
                <div class="task-grid">
                    {% for task in opportunities %}
                        {{ render_task_card(task) }}
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <h3>🔍 No opportunities identified</h3>
                    <p>Run the indexer to discover new opportunities.</p>
                </div>
            {% endif %}
        </div>

        <!-- Accomplished Tab -->
        <div id="accomplished" class="tab-content">
            {% if accomplished %}
                <div class="task-grid">
                    {% for task in accomplished %}
                        <div class="task-card">
                            <div class="task-title">✅ {{ task.title }}</div>
                            <div class="task-meta">
                                <span>Completed: {{ task.last_completed or 'Recently' }}</span>
                                <span class="type-badge">{{ task.type.value }}</span>
                            </div>
                            {% if task.notes %}
                                <div class="task-notes">{{ task.notes }}</div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <h3>🎯 Start accomplishing!</h3>
                    <p>Completed tasks will appear here.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <button class="refresh-btn" onclick="location.reload()" title="Refresh Dashboard">🔄</button>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function markDone(taskId) {
            // Simple approach: redirect to CLI command URL
            const url = `dashboard.html?action=done&id=${taskId}`;
            window.location.href = url;
        }

        function snoozeTask(taskId, days) {
            const url = `dashboard.html?action=snooze&id=${taskId}&days=${days}`;
            window.location.href = url;
        }

        function addNote(taskId) {
            const note = prompt('Add a note:');
            if (note) {
                const url = `dashboard.html?action=note&id=${taskId}&text=${encodeURIComponent(note)}`;
                window.location.href = url;
            }
        }

        // Handle URL parameters for actions
        window.addEventListener('load', function() {
            const params = new URLSearchParams(window.location.search);
            const action = params.get('action');
            const taskId = params.get('id');
            
            if (action && taskId) {
                // Show a message that the action was processed
                const message = document.createElement('div');
                message.style.cssText = 'position:fixed;top:20px;right:20px;background:#27ae60;color:white;padding:1rem;border-radius:5px;z-index:1000;';
                message.textContent = `Action "${action}" queued for task ${taskId}. Use ops_cli.py to process.`;
                document.body.appendChild(message);
                
                setTimeout(() => message.remove(), 5000);
                
                // Clear URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        });
    </script>
</body>
</html>

{% macro render_task_card(task, overdue=false) %}
<div class="task-card {{ task.priority.value }} {% if overdue %}overdue{% endif %}">
    <div class="task-title">{{ task.title }}</div>
    <div class="task-meta">
        {% if task.due %}
            <span>📅 {{ task.due }}</span>
        {% endif %}
        <span class="priority-badge priority-{{ task.priority.value }}">{{ task.priority.value }}</span>
        <span class="type-badge">{{ task.type.value }}</span>
        {% if task.confidence < 1.0 %}
            <span>🎯 {{ (task.confidence * 100)|round }}%</span>
        {% endif %}
    </div>
    {% if task.notes %}
        <div class="task-notes">{{ task.notes }}</div>
    {% endif %}
    <div class="task-actions">
        <button class="btn btn-done" onclick="markDone('{{ task.id }}')">✅ Done</button>
        <button class="btn btn-snooze" onclick="snoozeTask('{{ task.id }}', 3)">😴 3d</button>
        <button class="btn btn-snooze" onclick="snoozeTask('{{ task.id }}', 7)">📅 1w</button>
        <button class="btn btn-note" onclick="addNote('{{ task.id }}')">📝 Note</button>
    </div>
</div>
{% endmacro %}
