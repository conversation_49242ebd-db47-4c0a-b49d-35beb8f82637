body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

header {
    background-color: #003366; /* Dark blue */
    color: white;
    padding: 10px 0;
}

nav ul {
    list-style: none;
    padding: 0;
    text-align: center;
}

nav ul li {
    display: inline;
    margin: 0 15px;
}

nav ul li a {
    color: white;
    text-decoration: none;
    font-weight: bold;
}

.hero {
    background-color: #e6e6e6; /* Light gray */
    padding: 20px;
    text-align: center;
}

h1, h2 {
    color: #003366; /* Dark blue */
}

footer {
    text-align: center;
    padding: 10px 0;
    background-color: #003366; /* Dark blue */
    color: white;
    position: relative;
    bottom: 0;
    width: 100%;
}

/* Responsive Styles */
@media (max-width: 768px) {
    nav ul li {
        display: block;
        margin: 10px 0;
    }

    form {
        width: 90%;
        margin: auto;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.5em;
    }

    h2 {
        font-size: 1.2em;
    }
}
