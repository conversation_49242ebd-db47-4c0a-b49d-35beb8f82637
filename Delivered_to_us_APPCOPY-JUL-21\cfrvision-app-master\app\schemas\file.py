from datetime import datetime
from typing import Optional

from fastapi import Form
from pydantic import BaseModel, Field, model_validator

from app.schemas.common import ObjectID


class FileParameterSchema():
    fy_end: str = Field(
        ...,
        description="Fiscal year end date in YYYY-MM-DD format.",
        examples=["2023-12-31"]
    )
    draft_final: str = Field(
        ...,
        description="Indicates whether the document is a draft or final version.",
        examples=["draft", "final"]
    )
    version: str = Field(
        ...,
        description="Version of the document, typically a string identifier.",
        examples=["1.0", "2.1"]
    )
    comment: str = Field(
        "",
        description="Optional comment or additional information about the document.",
        examples=["Initial upload", "Updated draft"]
    )


class FileUploadSchema(FileParameterSchema, BaseModel):
    @classmethod
    def as_form(cls,
                fy_end: str = Form(...),
                draft_final: str = Form(...),
                version: str = Form(...),
                comment: str = Form("")) -> "FileUploadSchema":
        return cls(fy_end=fy_end, draft_final=draft_final, version=version, comment=comment)


# DeleteFileRequest
class DeleteFileRequest(ObjectID, BaseModel):
    pass


class FileSchema(FileParameterSchema, BaseModel):
    id: Optional[int]
    file_name: str = Field(
        "",
        description="name of the file.",
        examples=["file.pdf"]
    )
    size: str = Field(
        0,
        description="File  size , as a string.",
        examples=["10 MB"]
    )
    pages: int = Field(
        0,
        description="total Page count.",
        examples=[100, 200]
    )
    status: str = Field(
        "",
        description="Status of the file, e.g. 'uploaded', 'processed', 'error'.",
        examples=["uploaded", "processed", "error"]
    )
    upload_date: str = Field(
        "",
        description="Upload Date",
        examples=["2025-02-10 11:54:02"]
    )

    class Config:
        from_attributes = True
        orm_mode = True

    @model_validator(mode='before')
    def update_file_size(cls, values):
        values.size = f"{values.size / (1024 * 1024):.2f} MB"
        dt_object = datetime.strptime(values.upload_date, "%Y-%m-%d %H:%M:%S")

        # Convert to desired format (mmddyyyy)
        formatted_date = dt_object.strftime("%m/%d/%Y %H:%M")
        values.upload_date = formatted_date
        return values

class FileIDInput(ObjectID, BaseModel):
    pass