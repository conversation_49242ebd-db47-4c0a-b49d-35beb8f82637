import os
from email.message import EmailMessage

import aiosmtplib
from jinja2 import Environment, FileSystemLoader

from app import logger


class MailOperations:
    async def send_mail_util(self, message):
        try:

            # Create an SMTP client and send the email
            async with aiosmtplib.SMTP(hostname=os.getenv('SMTP_HOST', ''), port=os.getenv('SMTP_PORT'),
                                       use_tls=True) as client:
                await client.login(os.getenv('FROM_EMAIL', ''), os.getenv('SMTP_PASS', ''))
                await client.send_message(message)

            # await aiosmtplib.send(
            #     message,
            #     hostname=SMTP_HOST,
            #     port=SMTP_PORT,
            #     start_tls=True,
            #     username=SMTP_USER,
            #     password=SMTP_PASS,
            # )
        except Exception as e:
            logger.exception(f"Error sending mail: {str(e)}", exc_info=True)

    async def send_otp_verification_mail(self, email: str, full_name: str, token: str):
        print(f"Token:::{token}")
        try:
            env = Environment(loader=FileSystemLoader('templates'))
            template = env.get_template('forgot_password_template.html')
            logo_url = os.getenv('LOGO_URL')
            absolute_url = os.getenv('FRONTEND_URL')
            support_mail = os.getenv('FROM_EMAIL')
            email_content = template.render(user_name=full_name,
                                            logo_url=logo_url,
                                            verification_url=f'{absolute_url}/update-password?token={token}',
                                            support_mail=support_mail)
            message = EmailMessage()
            message["From"] = os.getenv('FROM_EMAIL')
            message["To"] = email
            message["Subject"] = "Token Verification"
            message.set_content(email_content, subtype="html")
            await self.send_mail_util(message)
        except Exception as e:
            logger.exception(f"Error sending otp verification mail: {str(e)}", exc_info=True)

    async def send_user_registration_mail(self, customer_email: str, temporary_password: str,customer_name: str ):
        try:
            env = Environment(loader=FileSystemLoader('templates'))
            template = env.get_template('welcome.html')
            logo_url = os.getenv('LOGO_URL')
            absolute_url = os.getenv('FRONTEND_URL')
            support_mail = os.getenv('FROM_EMAIL')
            email_content = template.render(customer_email=customer_email,
                                            temporary_password=temporary_password,
                                            customer_name=customer_name,
                                            logo_url=logo_url,
                                            login_url=f'{absolute_url}/login',
                                            support_mail=support_mail)
            message = EmailMessage()
            message["From"] = os.getenv('FROM_EMAIL')
            message["To"] = customer_email
            message["Subject"] = "Thanks for signing up"
            message.set_content(email_content, subtype="html")
            await self.send_mail_util(message)
        except Exception as e:
            logger.exception(f"Error sending registration mail: {str(e)}", exc_info=True)
