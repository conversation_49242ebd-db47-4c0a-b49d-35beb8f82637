from typing import Optional

from pydantic import BaseModel, Field

class AddContactUsSchema(BaseModel):
    name: str = Field(
        ...,
        description="FullName.",
        examples=["<PERSON><PERSON><PERSON>"]
    )
    email: str = Field(
        ...,
        description="Email.",
        examples=["<EMAIL>"]
    )
    organization: str = Field(
        ...,
        description="Organization name.",
        examples=["OX"]
    )
    message: str = Field(
        ...,
        description="Message.",
        examples=["Hi, there."]
    )
    status: Optional[str] = Field(
        default="pending",
        description="Message.",
        examples=["Hi, there."]
    )

class GetContactUsSchema(BaseModel):
    id: int
    name: str
    email: str
    organization: str
    message: str
    status: Optional[str] = ""
    notes: Optional[str]=""

    class Config:
        from_attributes = True
        orm_mode = True

class UpdateContactUsStatus(BaseModel):
    id: int
    status: Optional[str] = None
    notes: Optional[str] = None