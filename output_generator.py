"""
Output file generation for the Startup Ops Indexer.
"""
from pathlib import Path
from typing import List, Dict, Any
from collections import defaultdict
import logging

from models import Task, FileInfo, ProcessCandidate, OpportunityCandidate, TaskType

logger = logging.getLogger(__name__)


class OutputGenerator:
    """Generates all output files for the indexer."""
    
    def __init__(self, output_dir: str = ".ops_out"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_documents_txt(self, files: List[FileInfo]) -> None:
        """Generate documents.txt with file index grouped by folder."""
        output_file = self.output_dir / "documents.txt"
        
        # Group files by top-level folder
        folders = defaultdict(list)
        for file_info in files:
            folder = file_info.folder or "root"
            folders[folder].append(file_info)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for folder in sorted(folders.keys()):
                    f.write(f"\n=== {folder} ===\n")
                    
                    for file_info in sorted(folders[folder], key=lambda x: x.path):
                        # Format: <relative_path> | <filetype> | <created_date> | <brief_description>
                        created_date = file_info.created.strftime("%Y-%m-%d")
                        description = file_info.description or "No description"
                        
                        # Ensure description is max 120 chars
                        if len(description) > 120:
                            description = description[:117] + "..."
                        
                        line = f"{file_info.path} | {file_info.extension} | {created_date} | {description}\n"
                        f.write(line)
            
            logger.info(f"Generated documents.txt with {len(files)} files")
            
        except Exception as e:
            logger.error(f"Failed to generate documents.txt: {e}")
            raise
    
    def generate_processes_txt(self, candidates: List[ProcessCandidate]) -> None:
        """Generate processes.txt with recurring processes."""
        output_file = self.output_dir / "processes.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# Recurring Processes\n")
                f.write("# Format: <process_name> | <source_hint> | <cadence> | <next_due> | <evidence_paths_csv>\n\n")
                
                for candidate in sorted(candidates, key=lambda x: x.next_due or "9999-12-31"):
                    cadence = candidate.cadence.value if candidate.cadence else "manual"
                    next_due = candidate.next_due.isoformat() if candidate.next_due else "TBD"
                    evidence_csv = ",".join(candidate.evidence_paths)
                    
                    line = f"{candidate.name} | {candidate.source_hint} | {cadence} | {next_due} | {evidence_csv}\n"
                    f.write(line)
            
            logger.info(f"Generated processes.txt with {len(candidates)} processes")
            
        except Exception as e:
            logger.error(f"Failed to generate processes.txt: {e}")
            raise
    
    def generate_opportunities_txt(self, candidates: List[OpportunityCandidate]) -> None:
        """Generate opportunities.txt with non-dated follow-ups."""
        output_file = self.output_dir / "opportunities.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# Opportunities\n")
                f.write("# Format: <title> | <why_it_matters> | <suggested_next_step> | <evidence_paths_csv> | confidence(0-1)\n\n")
                
                for candidate in sorted(candidates, key=lambda x: x.confidence, reverse=True):
                    evidence_csv = ",".join(candidate.evidence_paths)
                    
                    line = (f"{candidate.title} | {candidate.why_it_matters} | "
                           f"{candidate.suggested_next_step} | {evidence_csv} | {candidate.confidence:.2f}\n")
                    f.write(line)
            
            logger.info(f"Generated opportunities.txt with {len(candidates)} opportunities")
            
        except Exception as e:
            logger.error(f"Failed to generate opportunities.txt: {e}")
            raise
    
    def generate_summary_report(self, files: List[FileInfo], tasks: List[Task]) -> None:
        """Generate a summary report with key statistics."""
        output_file = self.output_dir / "summary.txt"
        
        try:
            # Calculate statistics
            total_files = len(files)
            folders = len(set(f.folder for f in files if f.folder))
            
            task_stats = {
                'total': len(tasks),
                'pending': len([t for t in tasks if t.status.value == 'pending']),
                'in_progress': len([t for t in tasks if t.status.value == 'in_progress']),
                'done': len([t for t in tasks if t.status.value == 'done']),
                'snoozed': len([t for t in tasks if t.status.value == 'snoozed'])
            }
            
            type_stats = {
                'processes': len([t for t in tasks if t.type == TaskType.PROCESS]),
                'opportunities': len([t for t in tasks if t.type == TaskType.OPPORTUNITY]),
                'oneoffs': len([t for t in tasks if t.type == TaskType.ONEOFF])
            }
            
            # Get overdue tasks
            from datetime import date
            today = date.today()
            overdue_tasks = [t for t in tasks if t.due and t.due < today and t.status.value == 'pending']
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# Startup Ops Summary Report\n")
                f.write(f"Generated: {today.isoformat()}\n\n")
                
                f.write("## File Statistics\n")
                f.write(f"Total files processed: {total_files}\n")
                f.write(f"Folders scanned: {folders}\n\n")
                
                f.write("## Task Statistics\n")
                f.write(f"Total tasks: {task_stats['total']}\n")
                f.write(f"  - Pending: {task_stats['pending']}\n")
                f.write(f"  - In Progress: {task_stats['in_progress']}\n")
                f.write(f"  - Done: {task_stats['done']}\n")
                f.write(f"  - Snoozed: {task_stats['snoozed']}\n\n")
                
                f.write("## Task Types\n")
                f.write(f"  - Processes: {type_stats['processes']}\n")
                f.write(f"  - Opportunities: {type_stats['opportunities']}\n")
                f.write(f"  - One-offs: {type_stats['oneoffs']}\n\n")
                
                if overdue_tasks:
                    f.write("## Overdue Tasks\n")
                    for task in sorted(overdue_tasks, key=lambda x: x.due):
                        f.write(f"  - {task.title} (due: {task.due})\n")
                else:
                    f.write("## No Overdue Tasks\n")
                
                f.write("\n## Next Steps\n")
                f.write("1. Review dashboard.html for interactive task management\n")
                f.write("2. Use ops_cli.py to mark tasks complete or snooze them\n")
                f.write("3. Re-run indexer to update with new files\n")
            
            logger.info("Generated summary report")
            
        except Exception as e:
            logger.error(f"Failed to generate summary report: {e}")
            raise
    
    def generate_all_outputs(self, files: List[FileInfo], tasks: List[Task], 
                           process_candidates: List[ProcessCandidate],
                           opportunity_candidates: List[OpportunityCandidate]) -> None:
        """Generate all output files."""
        try:
            self.generate_documents_txt(files)
            self.generate_processes_txt(process_candidates)
            self.generate_opportunities_txt(opportunity_candidates)
            self.generate_summary_report(files, tasks)
            
            logger.info("All output files generated successfully")
            
        except Exception as e:
            logger.error(f"Failed to generate outputs: {e}")
            raise


class DashboardDataGenerator:
    """Generates data structures for the dashboard."""
    
    @staticmethod
    def prepare_dashboard_data(tasks: List[Task]) -> Dict[str, Any]:
        """Prepare data structure for dashboard rendering."""
        from datetime import date, timedelta
        
        today = date.today()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)
        
        # Categorize tasks
        today_tasks = [t for t in tasks if t.due == today and t.status.value == 'pending']
        overdue_tasks = [t for t in tasks if t.due and t.due < today and t.status.value == 'pending']
        this_week_tasks = [t for t in tasks if t.due and week_start <= t.due <= week_end and t.status.value == 'pending']
        opportunities = [t for t in tasks if t.type == TaskType.OPPORTUNITY and t.status.value == 'pending']
        accomplished = [t for t in tasks if t.status.value == 'done']
        
        # Priority counts
        priority_counts = {
            'high': len([t for t in tasks if t.priority.value == 'high' and t.status.value == 'pending']),
            'medium': len([t for t in tasks if t.priority.value == 'med' and t.status.value == 'pending']),
            'low': len([t for t in tasks if t.priority.value == 'low' and t.status.value == 'pending'])
        }
        
        return {
            'today_tasks': today_tasks,
            'overdue_tasks': overdue_tasks,
            'this_week_tasks': this_week_tasks,
            'opportunities': opportunities,
            'accomplished': accomplished[-10:],  # Last 10 accomplished
            'priority_counts': priority_counts,
            'total_pending': len([t for t in tasks if t.status.value == 'pending']),
            'generated_date': today.isoformat()
        }
