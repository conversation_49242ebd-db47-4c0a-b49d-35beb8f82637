"use client";
import AnimatedText from "@/components/common/AnimatedText";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const schema = v.object({
  email: v.pipe(
    v.string(),
    v.nonEmpty("Email is required"),
    v.email("Email is invalid")
  ),
  password: v.pipe(
    v.string(),
    v.nonEmpty("Password is required"),
    v.minLength(8, "Password must be at least 5 characters long")
    // v.regex(
    //   /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{5,}$/,
    //   "Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character"
    // )
  ),
});

const Login = () => {
  const { contextHolder, showToast } = Toaster();
  const [isDisabled, setIsDisabled] = useState(false);
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const router = useRouter();

  const onSubmit = async (data) => {
    try {
      setIsDisabled(true);
      const response = await axiosInstance.post("/v1/auth/login", data);
      localStorage?.setItem("auth", "true");
      localStorage?.setItem("auth_token", response?.data?.data?.access_token);
      localStorage?.setItem(
        "refresh_token",
        response?.data?.data?.refresh_token
      );
      localStorage?.setItem("role", response?.data?.data?.role);
      setIsDisabled(false);
      router.push("/");
    } catch (error) {
      setIsDisabled(false);
      console.log("err", error?.response);
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      <main id="main" className="auth-cover">
        <section className="page-section pt-0 pb-0">
          <ParallaxContainer
            className="page-section bg-gray-light-1 bg-light-alpha-90 parallax-5"
            style={{
              backgroundImage: "url(/assets/background.png)",
            }}
          >
            <>
              <div className="position-absolute top-0 bottom-0 start-0 end-0 bg-gradient-white background-img" />
              <div className="container position-relative pt-50">
                {/* Section Content */}
                <div className="text-center">
                  <div className="row">
                    {/* Page Title */}
                    <div className="col-md-8 offset-md-2">
                      <h2
                        className="section-caption-slick mb-30 mb-sm-20 wow fadeInUp"
                        data-wow-duration="1.2s"
                      >
                        Account
                      </h2>
                      <h1 className="mb-0">
                        <span
                          className="wow charsAnimIn"
                          data-splitting="chars"
                        >
                          <AnimatedText text="Log in to access your account." />
                        </span>
                      </h1>
                    </div>
                    {/* End Page Title */}
                  </div>
                </div>
                {/* End Section Content */}
              </div>
            </>
          </ParallaxContainer>
        </section>
        <>
          <>
            {/* Section */}
            <section className="page-section container pt-0">
              <div className="row">
                <div className="col-md-6 offset-md-3">
                  <form
                    noValidate
                    autoComplete="off"
                    onSubmit={handleSubmit(onSubmit)}
                    className="form contact-form"
                  >
                    <div className="mb-30">
                      {/* Name */}
                      <div className="form-group">
                        <label htmlFor="username">Email Address</label>
                        <Controller
                          name="email"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="email"
                              name="email"
                              id="email"
                              className="input-md round-large form-control"
                              placeholder="Enter email address"
                              required
                              aria-required="true"
                              value={field.value}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              ref={field.ref}
                            />
                          )}
                        />
                        {errors?.email && touchedFields.email && (
                          <span className="text-red">
                            {errors.email.message}
                          </span>
                        )}
                      </div>
                      {/* Password */}
                      <div className="form-group">
                        <label htmlFor="password">Password</label>
                        <Controller
                          name="password"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="password"
                              name="password"
                              id="password"
                              className="input-md round-large form-control"
                              placeholder="Enter password"
                              pattern=".{5,100}"
                              required
                              aria-required="true"
                              value={field.value}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              ref={field.ref}
                            />
                          )}
                        />
                        {errors?.password && touchedFields.password && (
                          <span className="text-red">
                            {errors.password.message}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="row mb-30">
                      <div className="col-6">
                        {/* Inform Tip */}
                        <div className="form-tip pt-10">
                          <a
                            href="/forgot-password"
                            style={{ textDecoration: "none" }}
                          >
                            Forgot Password?
                          </a>
                        </div>
                      </div>
                      <div className="col-6">
                        {/* Send Button */}
                        <div className="text-end">
                          <button
                            className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10"
                            id="login-btn"
                            disabled={!isValid || isDisabled}
                          >
                            <span>Login</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </section>
            {/* End Section */}
            {/* Divider */}
          </>

          {/* End Divider */}
        </>
      </main>
    </>
  );
};

export default Login;
