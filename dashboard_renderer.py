"""
Dashboard HTML rendering for the Startup Ops Indexer.
"""
import json
from pathlib import Path
from typing import List, Dict, Any
from jinja2 import Environment, FileSystemLoader
import logging

from models import Task
from output_generator import DashboardDataGenerator

logger = logging.getLogger(__name__)


class DashboardRenderer:
    """Renders the HTML dashboard from task data."""
    
    def __init__(self, template_dir: str = "templates", output_dir: str = ".ops_out"):
        self.template_dir = Path(template_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Setup Jinja2 environment
        self.env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )
    
    def render_dashboard(self, tasks: List[Task]) -> None:
        """Render the dashboard HTML file."""
        try:
            # Prepare data for dashboard
            dashboard_data = DashboardDataGenerator.prepare_dashboard_data(tasks)
            
            # Load template
            template = self.env.get_template('dashboard.html.j2')
            
            # Render HTML
            html_content = template.render(**dashboard_data)
            
            # Write to file
            output_file = self.output_dir / "dashboard.html"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Dashboard rendered to {output_file}")
            
        except Exception as e:
            logger.error(f"Failed to render dashboard: {e}")
            raise
    
    def render_from_tasks_json(self, tasks_json_path: str = None) -> None:
        """Render dashboard directly from tasks.json file."""
        if tasks_json_path is None:
            tasks_json_path = self.output_dir / "tasks.json"
        
        tasks_file = Path(tasks_json_path)
        if not tasks_file.exists():
            logger.warning(f"Tasks file not found: {tasks_file}")
            return
        
        try:
            with open(tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)
            
            # Convert back to Task objects
            tasks = []
            for task_data in tasks_data:
                # Convert date strings back to date objects
                from datetime import datetime
                if task_data.get('due'):
                    task_data['due'] = datetime.fromisoformat(task_data['due']).date()
                if task_data.get('created'):
                    task_data['created'] = datetime.fromisoformat(task_data['created']).date()
                if task_data.get('last_completed'):
                    task_data['last_completed'] = datetime.fromisoformat(task_data['last_completed']).date()
                
                task = Task(**task_data)
                tasks.append(task)
            
            self.render_dashboard(tasks)
            
        except Exception as e:
            logger.error(f"Failed to render dashboard from JSON: {e}")
            raise


def create_standalone_dashboard() -> None:
    """Create a standalone dashboard that can be opened directly in browser."""
    output_dir = Path(".ops_out")
    tasks_file = output_dir / "tasks.json"
    
    if not tasks_file.exists():
        logger.warning("No tasks.json found. Run the indexer first.")
        return
    
    renderer = DashboardRenderer()
    renderer.render_from_tasks_json()
    
    dashboard_file = output_dir / "dashboard.html"
    print(f"Dashboard created: {dashboard_file.absolute()}")
    print(f"Open in browser: file://{dashboard_file.absolute()}")


if __name__ == "__main__":
    create_standalone_dashboard()
