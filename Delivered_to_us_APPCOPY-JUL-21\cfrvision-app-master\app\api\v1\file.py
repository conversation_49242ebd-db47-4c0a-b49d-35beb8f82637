from fastapi import Depends, UploadFile, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import file_router
from app.database import get_db
from app.operations.file_operations.file_operations import FileOperations
from app.schemas.common import FetchSchemaSearch
from app.schemas.file import FileUploadSchema, DeleteFileRequest, FileIDInput
from app.utils.jwt_authentication.jwt_handler import JW<PERSON><PERSON>earer


@file_router.post("/upload_file")
async def upload_file(file: UploadFile, file_data: FileUploadSchema = Depends(FileUploadSchema.as_form),
                      user: dict = Depends(JWTBearer()),
                      db: AsyncSession = Depends(get_db), background_tasks: BackgroundTasks = BackgroundTasks()):
    """
    Upload a file to the system.

    This endpoint allows the user to upload a file along with additional metadata. The file is processed
    in the background for further tasks.

    **Args**:
        file (UploadFile): The file to be uploaded.
        file_data (FileUploadSchema): Metadata associated with the file, provided via a form.
        db (AsyncSession): The database session used for database operations.
        background_tasks (BackgroundTasks): A background task manager to handle background processing.

    **Returns**:
        JSON: The result of the file upload operation.
    """
    return await FileOperations(db).upload_file(file, file_data, user.get('user_id'), background_tasks)


@file_router.post("/delete_file", status_code=200, dependencies=[Depends(JWTBearer())])
async def delete_file(input_data: DeleteFileRequest, db: AsyncSession = Depends(get_db)):
    """
    Delete a file from the system.

    This endpoint deletes the file specified by the input data, removing it from the system.

    **Args**:
        input_data (DeleteFileRequest): The request containing the file's identifying information to be deleted.
        db (AsyncSession): The database session used to perform the file deletion operation.

    **Returns**:
        JSON: Confirmation of the file deletion.
    """
    return await FileOperations(db).delete_file(input_data)


@file_router.post("/my-files")
async def get_files(input_data: FetchSchemaSearch,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    """
    Retrieve files from the system based on search criteria.

    This endpoint fetches a list of files from the system based on the provided search filter.

    **Args**:
        input_data (FetchSchemaSearch): The search criteria used to filter the files.
        db (AsyncSession): The database session used to query and retrieve files.

    **Returns**:
        JSON: A list of files that match the provided search criteria.
    """
    return await FileOperations(db).get_files(input_data, user_id=user.get('user_id'))

@file_router.post("/get-file-path")
async def get_file_url(input_data: FileIDInput,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    """
    Retrieve file path from the system based on file id.

    This endpoint fetches the url to access file from the system based on the provided file ID filter.

    **Args**:
        input_data (FileIDInput): The file ID to fetch the file.
        db (AsyncSession): The database session used to query and retrieve files.

    **Returns**:
        JSON: A file_path for the provided file ID.
    """
    return await FileOperations(db).get_file_path(input_data, user_id=user.get('user_id'))
