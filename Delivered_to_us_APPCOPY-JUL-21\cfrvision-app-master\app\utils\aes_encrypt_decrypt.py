import base64
import os

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from dotenv import load_dotenv

load_dotenv()


class AESEncryptDecrypt:

    def __init__(self):
        self.KEY = eval(os.getenv('AES_SECRET_KEY_HEX'))
        self.iv = os.urandom(16)


    def encrypt(self, data: str):
        # Initialize a random 16-byte IV (Initialization Vector)

        # Padding the plaintext to make its length a multiple of the block size (16 bytes for AES)
        padder = padding.PKCS7(algorithms.AES.block_size).padder()
        padded_data = padder.update(data.encode()) + padder.finalize()

        # Encrypt the padded data
        cipher = Cipher(algorithms.AES(self.KEY)
                        , modes.CBC(self.iv), backend=default_backend())
        encryptor = cipher.encryptor()
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        # Return IV + encrypted data, Base64-encoded
        return base64.b64encode(self.iv + encrypted_data).decode()

    def decrypt(self, encrypted_data: str):
        # Decode the Base64-encoded encrypted data
        encrypted_data_bytes = base64.b64decode(encrypted_data)

        # Extract IV and the encrypted message
        self.iv = encrypted_data_bytes[:16]
        encrypted_message = encrypted_data_bytes[16:]

        # Decrypt the data
        cipher = Cipher(algorithms.AES(self.KEY)
                        , modes.CBC(self.iv), backend=default_backend())
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(encrypted_message) + decryptor.finalize()

        # Unpad the plaintext
        unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
        data = unpadder.update(padded_data) + unpadder.finalize()

        return data.decode()