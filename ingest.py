"""
File discovery and content extraction for the Startup Ops Indexer.
"""
import os
import hashlib
import mimetypes
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Generator, Set
import logging

from models import FileInfo, Config
from content_extractors import (
    extract_text_content, 
    extract_pdf_content, 
    extract_image_content,
    extract_office_content
)

logger = logging.getLogger(__name__)


class FileDiscovery:
    """Handles file discovery and basic metadata extraction."""
    
    def __init__(self, config: Config):
        self.config = config
        self.ignore_patterns = set(config.ignore_patterns)
    
    def should_ignore(self, path: Path) -> bool:
        """Check if a file/folder should be ignored based on patterns."""
        path_str = str(path)
        name = path.name
        
        for pattern in self.ignore_patterns:
            if pattern.startswith('*'):
                # Extension pattern
                if name.endswith(pattern[1:]):
                    return True
            elif pattern in path_str or pattern == name:
                return True
        
        return False
    
    def discover_files(self, root_path: str) -> Generator[FileInfo, None, None]:
        """Walk directory tree and yield FileInfo objects."""
        root = Path(root_path)
        
        if not root.exists():
            logger.error(f"Root path does not exist: {root_path}")
            return
        
        for file_path in root.rglob('*'):
            if file_path.is_file() and not self.should_ignore(file_path):
                try:
                    file_info = self._create_file_info(file_path, root)
                    yield file_info
                except Exception as e:
                    logger.warning(f"Error processing file {file_path}: {e}")
                    continue
    
    def _create_file_info(self, file_path: Path, root: Path) -> FileInfo:
        """Create FileInfo object from file path."""
        stat = file_path.stat()
        relative_path = file_path.relative_to(root)
        
        # Get top-level folder
        parts = relative_path.parts
        folder = parts[0] if len(parts) > 1 else ""
        
        return FileInfo(
            path=str(relative_path),
            size=stat.st_size,
            created=datetime.fromtimestamp(stat.st_ctime),
            modified=datetime.fromtimestamp(stat.st_mtime),
            extension=file_path.suffix.lower(),
            folder=folder,
            content_hash=self._calculate_content_hash(file_path)
        )
    
    def _calculate_content_hash(self, file_path: Path) -> str:
        """Calculate SHA256 hash of file content."""
        try:
            hasher = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()[:16]  # First 16 chars for brevity
        except Exception as e:
            logger.warning(f"Could not hash file {file_path}: {e}")
            return f"error_{file_path.stat().st_size}_{file_path.stat().st_mtime}"


class ContentExtractor:
    """Handles content extraction from various file types."""

    def __init__(self, enable_ocr: bool = False, max_file_size: int = 10 * 1024 * 1024,
                 use_google_vision: bool = False, google_ocr=None):
        self.enable_ocr = enable_ocr
        self.max_file_size = max_file_size
        self.use_google_vision = use_google_vision
        self.google_ocr = google_ocr
    
    def extract_content(self, file_info: FileInfo, root_path: str) -> FileInfo:
        """Extract content from file and update FileInfo."""
        file_path = Path(root_path) / file_info.path
        
        # Skip large files
        if file_info.size > self.max_file_size:
            file_info.description = f"Large file ({file_info.size // 1024}KB) - skipped extraction"
            return file_info
        
        try:
            content = self._extract_by_type(file_path, file_info.extension)
            file_info.extracted_text = content[:1000]  # Limit to first 1000 chars
            file_info.description = self._generate_description(content, file_info)
        except Exception as e:
            logger.warning(f"Content extraction failed for {file_path}: {e}")
            file_info.description = f"Content extraction failed: {str(e)[:50]}"
        
        return file_info
    
    def _extract_by_type(self, file_path: Path, extension: str) -> str:
        """Extract content based on file type."""
        if extension in ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.yml', '.yaml']:
            return extract_text_content(file_path)
        elif extension == '.pdf':
            return extract_pdf_content(file_path, self.use_google_vision, self.google_ocr)
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp'] and self.enable_ocr:
            return extract_image_content(file_path, use_google_vision=self.use_google_vision,
                                       google_ocr=self.google_ocr)
        elif extension in ['.docx', '.xlsx', '.pptx']:
            return extract_office_content(file_path)
        else:
            return ""
    
    def _generate_description(self, content: str, file_info: FileInfo) -> str:
        """Generate a brief description from content."""
        if not content.strip():
            return "No extractable content"
        
        # Take first meaningful line or sentence
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        if not lines:
            return "Empty content"
        
        description = lines[0]
        if len(description) > 120:
            description = description[:117] + "..."
        
        return description or "No description available"
