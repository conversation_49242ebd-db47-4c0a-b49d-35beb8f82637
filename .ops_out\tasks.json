[{"id": "5b215575c60fe5ba", "title": "Enter receipts in QuickBooks", "type": "process", "status": "pending", "priority": "high", "due": "2025-08-10", "created": "2025-08-09", "source": ["classifier.py", "config.yml", "run_example.py", "Chargebee\\Account_1.jpg", "Chargebee\\Account_2.jpg"], "notes": "", "confidence": 1.0, "recurrence": "DAILY", "last_completed": null, "tags": []}, {"id": "71ef16ade4295a53", "title": "Grant follow-up", "type": "process", "status": "pending", "priority": "med", "due": "2025-08-23", "created": "2025-08-09", "source": ["Possible_Grants_For _Us\\Eligible Grant Opportunities.docx", "Possible_Grants_For _Us\\Grants_Starter_List.docx"], "notes": "", "confidence": 0.****************, "recurrence": "BIWEEKLY", "last_completed": null, "tags": ["grants", "funding"]}, {"id": "bfe0873206c98739", "title": "Test deliverable and send feedback", "type": "process", "status": "pending", "priority": "high", "due": "2025-08-14", "created": "2025-08-09", "source": ["Delivered_to_us_APPCOPY-JUL-21\\cfrvision-app-master\\.env", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-app-master\\Dockerfile", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-app-master\\logo-img.png", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-app-master\\main.py", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-app-master\\README.md"], "notes": "", "confidence": 1.0, "recurrence": null, "last_completed": null, "tags": []}, {"id": "35ccea15651545f6", "title": "Review/update pricing deck", "type": "process", "status": "pending", "priority": "med", "due": "2025-09-08", "created": "2025-08-09", "source": ["Pricing_tiers_to_Customers\\Pricing_tiers.xlsx", "Pricing_tiers_to_Customers\\Tiers.jpg"], "notes": "", "confidence": 0.****************, "recurrence": "MONTHLY", "last_completed": null, "tags": []}, {"id": "76c8eb6f6738306c", "title": "Check compliance actions", "type": "process", "status": "pending", "priority": "high", "due": "2025-09-08", "created": "2025-08-09", "source": ["Required_Compliance and Security\\Master List Specifications Long Version.docx", "Required_Compliance and Security\\archive\\ADA Compliance.docx", "Required_Compliance and Security\\archive\\Collection 1.docx", "Required_Compliance and Security\\archive\\Locals-Resource-Packet-2023v1.1.pdf", "Required_Compliance and Security\\archive\\Master List.docx"], "notes": "", "confidence": 1.0, "recurrence": "MONTHLY", "last_completed": null, "tags": []}, {"id": "8d54688475380a71", "title": "Review AWS access and security", "type": "process", "status": "pending", "priority": "high", "due": "2025-09-08", "created": "2025-08-09", "source": ["classifier.py", "config.yml", "run_example.py", "setup.py", "AWS\\Access key.txt"], "notes": "", "confidence": 1.0, "recurrence": "MONTHLY", "last_completed": null, "tags": []}, {"id": "9aeee39d24c58ec7", "title": "Review development progress and invoices", "type": "process", "status": "pending", "priority": "high", "due": "2025-08-16", "created": "2025-08-09", "source": ["Openxcell\\2024-12-07 10-58_page_1.pdf", "Openxcell\\2024-12-07 10-58_page_2 (1).pdf", "Openxcell\\AWS_Users.jpg", "Openxcell\\Madia Web App - Design Questionnaire.docx", "Openxcell\\OpenXcell - AWS User 1.pdf"], "notes": "", "confidence": 1.0, "recurrence": "WEEKLY", "last_completed": null, "tags": []}, {"id": "07240a37d70d0c06", "title": "Branding Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["run_example.py", "Branding\\Media Application Solution\\3D Mocups\\Apple_Watch_Mockup_1.jpg", "Branding\\Media Application Solution\\3D Mocups\\bag.jpg"], "notes": "Why: Keep branding materials current and optimized\nNext: Review and update brand materials", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity", "branding"]}, {"id": "6b6f50695bc994f4", "title": "Linkedin Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["linkedin\\mmadia_profile.jpg", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\data\\footer.js"], "notes": "Why: Keep linkedin materials current and optimized\nNext: Update LinkedIn profile and post content", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity", "social"]}, {"id": "c79275440f75ce65", "title": "Openxcell Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["config.yml", "run_example.py", "Openxcell\\2024-12-07 10-58_page_1.pdf"], "notes": "Why: Keep Openxcell materials current and optimized\nNext: Review Openxcell related materials", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity"]}, {"id": "581ff098f119c429", "title": "Aws Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["config.yml", "run_example.py", "AWS\\Access key.txt"], "notes": "Why: Keep AWS materials current and optimized\nNext: Review AWS configuration and costs", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity", "infrastructure"]}, {"id": "afd1bccca224b8e7", "title": "Github Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["girhub\\GitHub.txt", "girhub\\madiasolutions_cfrvision-app.pdf", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\public\\assets\\css\\bootstrap.min.css"], "notes": "Why: Keep github materials current and optimized\nNext: Review code repositories and documentation", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity"]}, {"id": "53362148e0b0b268", "title": "Architecture Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Architecture.pdf", "Functional_Specs_Misc_Docs_Archive\\Architecture.pptx", "Functional_Specs_Misc_Docs_Archive\\Architecture_AWS.pdf"], "notes": "Why: Keep architecture materials current and optimized\nNext: Review system architecture documentation", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity"]}, {"id": "f1b7753253195fac", "title": "Proposal Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["classifier.py", "Openxcell\\OpenXcell - Business Proposal Madia application Signed.pdf", "Openxcell\\OpenXcell - Business Proposal Madia application.pdf"], "notes": "Why: Keep proposal materials current and optimized\nNext: Review proposal related materials", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity"]}, {"id": "7b7b52be310239eb", "title": "Design Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Openxcell\\Madia Web App - Design Questionnaire.docx", "Openxcell\\ProjectPlanTable - 12 Jan.docx", "Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\package.json"], "notes": "Why: Keep design materials current and optimized\nNext: Review design related materials", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity"]}, {"id": "66da6b53582d9094", "title": "Consultation Review", "type": "opportunity", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Request fo Consultation.docx", "Week 2 Accomplished\\MyFLmarletplace\\MyFloridaMarket Place page2.pdf"], "notes": "Why: Keep consultation materials current and optimized\nNext: Review consultation related materials", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["opportunity"]}, {"id": "c8eaf2cdb805d8d7", "title": "for the startup ops indexer.", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["classifier.py"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "grants", "deliverables"]}, {"id": "ac397c766371a32c", "title": "utilities for different file types.", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["content_extractors.py"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "8178e6ffa769785c", "title": "disabled\")", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["content_extractors.py"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "abdecd680d00a30b", "title": "for the startup ops indexer.", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["ingest.py"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "651302b4dca90100", "title": "optional[date] = none", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["models.py"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["business"]}, {"id": "cef187cb628a1876", "title": "due dates", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["README.md"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "9ad6ba4ca5dbdaa5", "title": "billing date", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Chargebee\\Invoice.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses"]}, {"id": "05a527b53f539617", "title": "renewal on", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Chargebee\\Mail - Subscription Confirmation.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "pricing"]}, {"id": "0cb682006266d0f0", "title": "renewal", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Chargebee\\Mail - Subscription Confirmation.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "pricing"]}, {"id": "4d6b3c0fcaf50706", "title": "process. this thread executes the ` run_extraction ` f", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\File Upload Process 10 march.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "0f01256fe8e793dc", "title": "the user logs into the app or self-service portal, navigates to their subscription settings, and sel", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Process Flow User Cancellation.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses"]}, {"id": "f6d572ccf258c6fe", "title": "a city government representative logs into the app or self-service portal, navigates to their subscr", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Process Flow User Changes Tier.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "pricing"]}, {"id": "37cb3d102f05c794", "title": "the user visits your website (e.g., url.com), selects one of the three subscription tiers (e.g., bas", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Process Flow User Registration Immeduate Payment.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "pricing"]}, {"id": "a1df6fa35afd7c22", "title": "the city government representative visits your website, selects a subscription plan (e.g., basic, pr", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Process Flow User Registration Invoiced Payment.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "grants", "pricing"]}, {"id": "a3cbfe15efc1a787", "title": "failed: no /root object! - is this really a pdf?", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Branding\\Media Application Solution\\Letterhead\\~$tteehead.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["branding"]}, {"id": "74f54c2ff445b10f", "title": "build\",", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\package.json"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["deliverables", "development"]}, {"id": "7777c52cdae91ca5", "title": "start\",", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\package.json"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["deliverables", "development"]}, {"id": "ea6b398d1b80f174", "title": "steps.", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\README.md"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["deliverables", "development"]}, {"id": "852483e05e16f76e", "title": "pan-y;", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Delivered_to_us_APPCOPY-JUL-21\\cfrvision-frontend-master\\public\\assets\\css\\owl.carousel.css"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["deliverables", "development"]}, {"id": "b295f951fbc38944", "title": "amazon  textract : since you mentioned there will be no ocr", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Architecture feedback\\Architecture Feedback Qwen.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["aws"]}, {"id": "e88b91ac013dee2b", "title": "to its scalability and durability. file size limit : you mentioned a maximum file size of 80 mb per ", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Architecture feedback\\Architecture Feedback Qwen.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["aws"]}, {"id": "0868c13a23a2aa16", "title": "= $(this).attr('action');", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Madia Website\\Live_Site\\Live_Site\\js\\jquery.contact.js"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "2b3352714d6ba533", "title": "= $(this).attr('action');", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Madia Website\\Live_Site\\Site - Copy\\js\\jquery.contact.js"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "5a1516dbdc03acd4", "title": "= $(this).attr('action');", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Functional_Specs_Misc_Docs_Archive\\Madia Website\\Live_Site\\Site - Copy (2)\\js\\jquery.contact.js"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": []}, {"id": "9a04766c6044de4f", "title": "$2,000.00", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Openxcell\\Invoices\\Invoice 7333.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses"]}, {"id": "668b7ec185cc7e36", "title": "$2,000.00", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Openxcell\\Invoices\\Invoice_7234_from_OpenXcell_Inc.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses"]}, {"id": "c1a9efca973f2110", "title": "report submit a report summarizing the incident resolution within one week of remediat", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Required_Compliance and Security\\archive\\Master List.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["compliance"]}, {"id": "ad94c243030e697f", "title": "open your web browser and go to  www.godaddy.com . 2. search for your first domain action: on the ho", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 1 Accomplished\\Domains\\Domains-Godaddy_Steps.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}, {"id": "3ff4b5d9966ceb6f", "title": "to the domain name. a confirmation will appear, showing that the domain has been added to your cart.", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 1 Accomplished\\Domains\\Domains-Godaddy_Steps.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}, {"id": "16dd06917166f435", "title": "move is,", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 1 Accomplished\\Domains\\Domains-Mail - <PERSON> - Outlook.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}, {"id": "9bfe8e24e9a4ad09", "title": "or pilot programs. 2. company description (2–3 pages) company background : information on madia appl", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 1 Accomplished\\High Level Business Plan\\Business Plan Structure.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished", "pricing", "business"]}, {"id": "f88966cd51383b4f", "title": "details", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 1 Accomplished\\WOB\\Item E\\Initial contribution.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}, {"id": "645c717f9d5ef465", "title": "category:", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 1 Accomplished\\WOB\\Item E\\Initial contribution.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}, {"id": "2f631517dc2b7291", "title": "go to the  microsoft 365 for business  page on microsoft’s website. choose the plan that best suits ", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 2 Accomplished\\Business Email Microsoft 365 closed\\Microsoft 365 Features.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished", "business"]}, {"id": "959b7dc2b67695a9", "title": "steps (for llc)?", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 2 Accomplished\\EIN\\EIN Individual Request - INFO 1.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished", "finance"]}, {"id": "017cb111f5b723cd", "title": "summary", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 2 Accomplished\\LLC\\Annual_Filing_April_2025.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished", "expenses"]}, {"id": "c8abc4c87c666d54", "title": "detail", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 2 Accomplished\\LLC\\Annual_Filing_April_2025.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished", "expenses"]}, {"id": "cf44e4153de09557", "title": "failed: 'descendantfonts'", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 2 Accomplished\\LLC\\Operating Agreement - LLC Single Member -- Madia Application Solutions LLC.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished", "grants", "legal", "development"]}, {"id": "17702267dd335b50", "title": "failed: 'mediabox'", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 2 Accomplished\\MyFLmarletplace\\WFServlet.pdf"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}, {"id": "c00ce56cba3f44cc", "title": "notes create aws account go to the  aws website  and create a free account. follow the prompts to se", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 4 Accomplished\\AWS\\AWS_Plan_Daily.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["expenses", "business", "aws", "accomplished", "pricing"]}, {"id": "e58ef7b7156b126b", "title": "llm processing guidance statement of net position 1. verify that total assets, total liabilities, an", "type": "oneoff", "status": "pending", "priority": "med", "due": null, "created": "2025-08-09", "source": ["Week 4 Accomplished\\Developers\\Sample checks for ACFR document.docx"], "notes": "", "confidence": 0.7, "recurrence": null, "last_completed": null, "tags": ["accomplished"]}]