from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import category_router
from app.database import get_db
from app.operations.category_operations.category_operations import AICheckCategory
from app.schemas.category import CheckCategoriesEdit, CheckCategoryID, CheckCategoriesCreate
from app.schemas.common import FetchSchemaSearch
from app.utils.jwt_authentication.jwt_handler import JWTBearer


@category_router.post("/ai-check-categories", dependencies=[Depends(JWTBearer())])
async def get_ai_check_categories(
        data: FetchSchemaSearch,
        db: AsyncSession = Depends(get_db),
):
    """
    Fetch AI check categories based on the provided search criteria.

    This endpoint retrieves AI check categories based on the given filter criteria.

    **Args**:
        data (FetchSchemaSearch): The search filter criteria for fetching AI check categories.
        db (AsyncSession): The database session used to query and retrieve the categories.

    **Returns**:
        JSON: A list of AI check categories that match the filter criteria.
    """
    return await AICheckCategory(db).get_ai_check_categories(data)


@category_router.post("/ai-check-categories/create", dependencies=[Depends(JWTBearer())])
async def create_ai_check_categories(
        categories: CheckCategoriesCreate,
        db: AsyncSession = Depends(get_db),
):
    """
    Create new AI check categories.

    This endpoint creates new AI check categories based on the provided input data.

    **Args**:
        categories (CheckCategoriesCreate): The data for creating AI check categories.
        db (AsyncSession): The database session used to create the categories in the database.

    **Returns**:
        JSON: The created AI check categories data.
    """
    return await AICheckCategory(db).create_ai_check_categories(categories)


@category_router.post("/ai-check-categories/edit", dependencies=[Depends(JWTBearer())])
async def update_ai_check_categories(
        categories: CheckCategoriesEdit,
        db: AsyncSession = Depends(get_db),
):
    """
    Update existing AI check categories.

    This endpoint updates existing AI check categories with the provided input data.

    **Args**:
        categories (CheckCategoriesEdit): The data for editing AI check categories.
        db (AsyncSession): The database session used to update the categories in the database.

    **Returns**:
        JSON: The updated AI check categories data.
    """
    return await AICheckCategory(db).update_ai_check_categories(categories)


@category_router.post("/ai-check-categories/delete", dependencies=[Depends(JWTBearer())])
async def delete_ai_check_categories(
        categories: CheckCategoryID,
        db: AsyncSession = Depends(get_db),
):
    """
    Delete AI check categories based on the provided category ID.

    This endpoint deletes AI check categories using the provided category ID.

    **Args**:
        categories (CheckCategoryID): The ID of the AI check category to be deleted.
        db (AsyncSession): The database session used to delete the category from the database.

    **Returns**:
        JSON: Confirmation of the category deletion.
    """
    return await AICheckCategory(db).delete_ai_check_categories(categories)
