import copy
import functools
import traceback

from app import logger
from main import exception_queue, content_bkp


def async_exception_logger(func):
    """
    An async decorator that catches exceptions in the decorated async function.
    When an exception occurs, it stores:
      - the function name,
      - its positional and keyword arguments,
      - the error message and traceback.
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        global content_bkp

        try:
            content_bkp.update({kwargs.get('event_type'):copy.deepcopy(kwargs.get('content'))})
            return await func(*args, **kwargs)
        except Exception as e:
            kwargs.update({'content': copy.deepcopy(content_bkp.get(kwargs.get('event_type')))})
            logger.info(msg=f"webhook execution failed : event type : {kwargs.get('event_type', 'None')} \n with error: {str(e)}")
            record = {
                "function": func,  # Store function name instead of function object
                "args": args,
                "kwargs": kwargs,  # Use backup to avoid mutation issues
                "error": str(e),
                "traceback": traceback.format_exc()
            }
            exception_queue.append(record)

    return wrapper


async def retry_failed_operations(db=None):
    """
    Attempts to re-run any previously failed operations.

    - If a retry succeeds, the failed record is removed from the queue.
    - If it fails again, the record is kept for future retries.
    """
    global exception_queue
    global content_bkp
    if not exception_queue:
        logger.info(msg="No failed operations to retry.")
        return
    new_queue = []
    for record in exception_queue:
        if db:
            record["kwargs"].update({'db': db})

        func = record["function"]
        args = record["args"]
        kwargs = record["kwargs"]
        content_bkp = copy.deepcopy(kwargs.get('content'))

        try:
            if kwargs.get('re_run_count', 0) < 5:
                result = await func(*args, **kwargs)
                logger.info(
                    msg=f"success re-run event type : {kwargs.get('event_type', 'None')}")

                print(f"success re-run event type : {kwargs.get('event_type', 'None')}")
            else:
                logger.info(msg='mail admin for failed ops')
        except Exception as e:
            kwargs.update({'content': copy.deepcopy(content_bkp), 're_run_count': kwargs.get('re_run_count', 0) + 1})
            logger.info(msg=f"re-run event type : {kwargs.get('event_type', 'None')},  re-run count : {kwargs.get('re_run_count', 0)},  content : {kwargs.get('content', {})}")

            new_queue.append(record)

    exception_queue = new_queue
    logger.info(msg=f"Remaining failed operations: {len(exception_queue)}")
