"use client";

import { AgGridReact } from "ag-grid-react";
import React, { useEffect, useMemo, useRef, useState } from "react";

import {
  AllCommunityModule,
  ModuleRegistry,
  themeQuartz,
} from "ag-grid-community";

ModuleRegistry.registerModules([AllCommunityModule]);

const paginationPageSizeSelector = [5, 10, 20];

const TableWithPagination = ({
  data,
  columns,
  onPaginationChange,
  loading,
  onSearchChange,
}) => {
  const gridRef = useRef(null);
  const [searchText, setSearchText] = useState("");
  const searchTimeoutRef = useRef(null);

  const autoSizeStrategy = useMemo(
    () => ({
      type: "fitGridWidth",
    }),
    []
  );

  const defaultColDef = useMemo(
    () => ({
      resizable: false,
    }),
    []
  );

  const handlePaginationChange = (event) => {
    if (event.newPage || event.newPageSize) {
      const currentPage = event.api.paginationGetCurrentPage() + 1;
      const pageSize = event.api.paginationGetPageSize();
      onPaginationChange(currentPage, pageSize);
    }
  };

  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchText(value);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      onSearchChange(value);
    }, 500);
  };

  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.showLoadingOverlay();
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!loading && gridRef.current && gridRef.current.api) {
      gridRef.current.api.hideOverlay();
      if (!data || data.length === 0) {
        gridRef.current.api.showNoRowsOverlay();
      }
    } else if (loading && gridRef.current && gridRef.current.api) {
      gridRef.current.api.showLoadingOverlay();
    }
  }, [loading]);

  const onGridReady = (params) => {
    params.api.showLoadingOverlay();
  };

  return (
    <div style={{ width: "100%", height: "100%", marginTop: "20px" }}>
      <div style={{ marginBottom: "15px" }}>
        <input
          type="text"
          value={searchText}
          onChange={handleSearch}
          placeholder="Search..."
          style={{
            padding: "3px 10px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            width: "300px",
            outline: "none",
          }}
        />
      </div>
      <AgGridReact
        ref={gridRef}
        rowData={data}
        columnDefs={columns}
        pagination={true}
        rowHeight={50}
        paginationPageSize={10}
        domLayout="autoHeight"
        paginationPageSizeSelector={paginationPageSizeSelector}
        theme={themeQuartz.withParams({
          spacing: 12,
          fontFamily: "Montserrat",
        })}
        detailRowAutoHeight
        autoSizeStrategy={autoSizeStrategy}
        masterDetail
        defaultColDef={defaultColDef}
        onPaginationChanged={handlePaginationChange}
        overlayLoadingTemplate={"<b>Loading data...</b>"}
        overlayNoRowsTemplate={"<b>No rows to show</b>"}
        onGridReady={onGridReady}
      />
    </div>
  );
};

export default TableWithPagination;
