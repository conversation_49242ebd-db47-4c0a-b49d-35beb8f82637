import asyncio
import re
from typing import List, <PERSON><PERSON>, Dict, Any
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>oolExecutor
import numpy as np

import bcrypt
from PyPDF2 import PdfReader
from fastapi import Depends
from langchain.text_splitter import RecursiveCharacterTextSplitter
from openai import OpenAI, NOT_GIVEN
from sentence_transformers import SentenceTransformer
from sqlalchemy import update, delete
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.dialects.postgresql import insert

from app import logger
from app.database import get_db
from app.database.models import File, ExtractedFullText, PdfChunk, ChunkEmbedding
from app.utils.exception_handling import ExceptionHandling
from app.utils.s3_operations import AWSFileOperations

# Configuration constants
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 100
BATCH_SIZE = 5  # Number of pages to process in each batch
MAX_WORKERS = 3  # Number of parallel workers for page processing
EMBEDDING_MODEL = 'all-mpnet-base-v2'

class OpenAIOperations:

    def __init__(self, ):
        self.client = OpenAI()

    async def openai_communication(self, messages, response_format=NOT_GIVEN):
        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                response_format=response_format
            )
            return completion
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())}\nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="openai_communication_util").exception_handling()
            raise


class FilePreProcessing:

    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def store_chunk_in_db(self, file_id, chunk_index, chunk_text, page_number):
        try:  # Assuming you have a model named PdfChunk
            new_chunk = PdfChunk(
                file_id=file_id,
                chunk_index=chunk_index,
                chunk_text=chunk_text,
                page_number=page_number
            )
            self.db.add(new_chunk)
            await self.db.commit()
            return new_chunk.id  # Returning the chunk id
        except SQLAlchemyError as e:
            # Log the error and return the structured response
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="store_chunk_in_db").exception_handling()
            return error

    # Async function to store embeddings in DB
    async def store_embedding_in_db(self, chunk_id, embedding):
        try:
            # Assuming you have a model for chunk embeddings
            new_embedding = ChunkEmbedding(
                chunk_id=chunk_id,
                embedding=embedding.tobytes()  # Convert the numpy array to bytes
            )
            self.db.add(new_embedding)
            await self.db.commit()
            return new_embedding.id  # Returning the embedding id
        except SQLAlchemyError as e:
            # Log the error and return the structured response
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="store_embedding_in_db").exception_handling()
            return error

    # Async function to store full text data in DB
    async def store_full_text_in_db(self, file_id, full_text, extraction_date):
        try:
            # Assuming you have a model for the full text
            new_full_text = ExtractedFullText(
                file_id=file_id,
                full_text=full_text,
                extraction_date=extraction_date
            )
            self.db.add(new_full_text)
            await self.db.commit()
            return new_full_text.id  # Returning the full text id
        except SQLAlchemyError as e:
            # Log the error and return the structured response
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="store_full_text_in_db").exception_handling()
            return error

    # Clean the extracted text to remove unwanted characters
    async def clean_text(self, text):
        """
        Cleans the text by removing unwanted characters, normalizing whitespace, and line breaks.
        """
        try:
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)
            # Normalize whitespace (replace multiple spaces with single space)
            text = re.sub(r'\s+', ' ', text)
            # Normalize line breaks (replace multiple newlines with single newline)
            text = re.sub(r'\n+', '\n', text)
            return text.strip()
        except Exception as e:
            logger.exception(f"Error cleaning text: {str(e)}", exc_info=True)
            return ""  # Return empty string if cleaning fails

    async def extract_and_store_text(self, file_id: int, file_path: str, user_id: str):
        """
        Optimized function to extract and store PDF text with improved performance and error handling.
        """
        # Initialize resources
        model = None
        text_splitter = None
        file_obj = None
        
        try:
            logger.info(f"Extraction and storing started for file ID: {file_id}")
            
            # Update file status to processing
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(status="Processing")
            )
            await self.db.commit()
            
            # Initialize resources
            file_obj = await AWSFileOperations().read_pdf_from_s3(user_id, file_path)
            logger.info(f"File received from S3: {file_id}")
            
            reader = PdfReader(file_obj)
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=CHUNK_SIZE,
                chunk_overlap=CHUNK_OVERLAP,
                length_function=len,
            )
            
            # Initialize model once
            model = SentenceTransformer(EMBEDDING_MODEL)
            
            total_pages = len(reader.pages)
            
            # Update file with page count
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(pages=total_pages)
            )
            await self.db.commit()
            
            # Process pages in batches for better memory management
            all_chunks = []
            all_embeddings = []
            full_text_parts = []
            
            for batch_start in range(0, total_pages, BATCH_SIZE):
                batch_end = min(batch_start + BATCH_SIZE, total_pages)
                batch_pages = reader.pages[batch_start:batch_end]
                
                # Process batch of pages
                batch_results = await self._process_page_batch(
                    batch_pages, batch_start, file_id, text_splitter, model
                )
                
                all_chunks.extend(batch_results['chunks'])
                all_embeddings.extend(batch_results['embeddings'])
                full_text_parts.extend(batch_results['full_text_parts'])
                
                # Log progress
                logger.info(f"Processed pages {batch_start + 1}-{batch_end} of {total_pages} for file {file_id}")
            
            # Bulk insert chunks
            if all_chunks:
                await self._bulk_insert_chunks(all_chunks)
                logger.info(f"Inserted {len(all_chunks)} chunks for file {file_id}")
            
            # Link embeddings to chunks and bulk insert
            if all_embeddings:
                # Update embedding chunk_ids with the actual chunk IDs
                for chunk_entry, embedding_entry in all_embeddings:
                    embedding_entry.chunk_id = chunk_entry.id
                
                # Extract just the embedding objects for bulk insert
                embedding_objects = [emb[1] for emb in all_embeddings]
                await self._bulk_insert_embeddings(embedding_objects)
                logger.info(f"Inserted {len(embedding_objects)} embeddings for file {file_id}")
            
            # Store full text
            full_text = "\n".join(full_text_parts)
            full_text_entry = ExtractedFullText(
                file_id=file_id,
                full_text=full_text
            )
            self.db.add(full_text_entry)
            await self.db.commit()
            
            # Update file status to complete
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(status="Complete", comment="Text extraction completed successfully")
            )
            await self.db.commit()
            
            logger.info(f"Text extracted and stored in DB for file Id: {file_id}")
            return {
                "data": {"file_id": file_id},
                "error": "",
                "message": "Text extraction completed successfully.",
                "status": True
            }, 200

        except SQLAlchemyError as e:
            logger.error(f"Database error in extract and store text: {str(e)}")
            error_msg = f"Database error during text extraction: {str(e)}"
            await self._cleanup_on_error(file_id, error_msg)
            return {
                "data": {},
                "error": error_msg,
                "message": "Database operation failed.",
                "status": False
            }, 500
        except Exception as e:
            logger.error(f"Error in extract and store text: {str(e)}")
            error_msg = f"Error during text extraction: {str(e)}"
            await self._cleanup_on_error(file_id, error_msg)
            return {
                "data": {},
                "error": error_msg,
                "message": "Text extraction failed.",
                "status": False
            }, 500

    async def _process_page_batch(
        self, 
        pages: List, 
        batch_start: int, 
        file_id: int, 
        text_splitter: RecursiveCharacterTextSplitter,
        model: SentenceTransformer
    ) -> Dict[str, List]:
        """
        Process a batch of pages concurrently for better performance.
        """
        chunks = []
        embeddings = []
        full_text_parts = []
        
        # Process pages in parallel using ThreadPoolExecutor for CPU-bound tasks
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Create tasks for each page
            tasks = []
            for page_index, page in enumerate(pages):
                global_page_index = batch_start + page_index
                task = asyncio.get_event_loop().run_in_executor(
                    executor,
                    self._process_single_page,
                    page,
                    global_page_index,
                    file_id,
                    text_splitter,
                    model
                )
                tasks.append(task)
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Error processing page: {str(result)}")
                    continue
                
                if result:
                    chunks.extend(result['chunks'])
                    embeddings.extend(result['embeddings'])
                    full_text_parts.append(result['full_text'])
        
        return {
            'chunks': chunks,
            'embeddings': embeddings,
            'full_text_parts': full_text_parts
        }

    def _process_single_page(
        self,
        page,
        page_index: int,
        file_id: int,
        text_splitter: RecursiveCharacterTextSplitter,
        model: SentenceTransformer
    ) -> Dict[str, Any]:
        """
        Process a single page (synchronous function for ThreadPoolExecutor).
        """
        try:
            page_text = page.extract_text() or ""
            cleaned_text = self._clean_text_sync(page_text)
            
            if not cleaned_text.strip():
                return None
            
            chunks = text_splitter.split_text(cleaned_text)
            
            page_chunks = []
            page_embeddings = []
            
            for chunk_index, chunk in enumerate(chunks):
                if not chunk.strip():
                    continue
                
                # Create chunk entry
                chunk_entry = PdfChunk(
                    file_id=file_id,
                    chunk_index=chunk_index,
                    chunk_text=chunk,
                    page_number=page_index + 1
                )
                page_chunks.append(chunk_entry)
                
                # Generate embedding
                embedding = model.encode(chunk)
                embedding_entry = ChunkEmbedding(
                    chunk_id=None,  # Will be set after chunk is inserted
                    embedding=embedding.tobytes()
                )
                page_embeddings.append((chunk_entry, embedding_entry))
            
            return {
                'chunks': page_chunks,
                'embeddings': page_embeddings,
                'full_text': cleaned_text
            }
            
        except Exception as e:
            logger.error(f"Error processing page {page_index + 1}: {str(e)}")
            return None

    def _clean_text_sync(self, text: str) -> str:
        """
        Synchronous version of clean_text for use in ThreadPoolExecutor.
        """
        try:
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'\n+', '\n', text)
            return text.strip()
        except Exception as e:
            logger.exception(f"Error cleaning text: {str(e)}", exc_info=True)
            return ""

    async def _bulk_insert_chunks(self, chunks: List[PdfChunk]):
        """
        Bulk insert chunks for better performance.
        """
        try:
            # Use bulk_insert_mappings for better performance
            chunk_data = [
                {
                    'file_id': chunk.file_id,
                    'chunk_index': chunk.chunk_index,
                    'chunk_text': chunk.chunk_text,
                    'page_number': chunk.page_number
                }
                for chunk in chunks
            ]
            
            # Insert chunks and get their IDs
            result = await self.db.execute(
                insert(PdfChunk).values(chunk_data).returning(PdfChunk.id)
            )
            chunk_ids = [row[0] for row in result.fetchall()]
            await self.db.commit()
            
            # Update the chunk objects with their IDs for embedding references
            for chunk, chunk_id in zip(chunks, chunk_ids):
                chunk.id = chunk_id
            
        except SQLAlchemyError as e:
            logger.error(f"Error in bulk insert chunks: {str(e)}")
            await self.db.rollback()
            raise

    async def _bulk_insert_embeddings(self, embeddings: List[ChunkEmbedding]):
        """
        Bulk insert embeddings for better performance.
        """
        try:
            embedding_data = [
                {
                    'chunk_id': embedding.chunk_id,
                    'embedding': embedding.embedding
                }
                for embedding in embeddings
                if embedding.chunk_id is not None
            ]
            
            if embedding_data:
                await self.db.execute(
                    insert(ChunkEmbedding).values(embedding_data)
                )
                await self.db.commit()
                
        except SQLAlchemyError as e:
            logger.error(f"Error in bulk insert embeddings: {str(e)}")
            await self.db.rollback()
            raise

    async def _cleanup_on_error(self, file_id: int, error_msg: str):
        """
        Cleanup function to handle errors and rollback changes.
        """
        try:
            await self.db.rollback()
            
            # Clean up any partially processed data
            await self.db.execute(
                delete(ExtractedFullText).where(ExtractedFullText.file_id == file_id)
            )
            await self.db.execute(
                delete(PdfChunk).where(PdfChunk.file_id == file_id)
            )
            await self.db.execute(
                delete(ChunkEmbedding).where(
                    ChunkEmbedding.chunk_id.in_(
                        select(PdfChunk.id).where(PdfChunk.file_id == file_id)
                    )
                )
            )
            
            # Update file status to failed
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(status="Failed", comment=error_msg)
            )
            await self.db.commit()
            
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {str(cleanup_error)}")
            await self.db.rollback()

    async def run_extraction(self, file_id: int, file_path: str, user_id: str):
        try:
            print("extraction started")
            await asyncio.sleep(60)

            # Update file status to 'Extracting Text'
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(status="Extracting Text")
            )
            await self.db.commit()
            print("extraction started 1")
            # Call the text extraction function
            success, response = await self.extract_and_store_text(file_id, file_path, user_id)
            print("extraction started 2")
            # Update status based on the extraction result
            new_status = "Complete" if success.get('status') else "Failed"
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(status=new_status, comment=success.get("message", ""))
            )
            await self.db.commit()
            print("extraction started 3")
            return response

        except Exception as e:
            print("extraction started 4")
            logger.error(f"Error in run extraction: {str(e)}")
            await self.db.execute(
                update(File)
                .where(File.id == file_id)
                .values(status="Failed", comment=str(e))
            )
            await self.db.commit()

            return {
                "data": {},
                "error": str(e),
                "message": "Text extraction failed.",
                "status": False
            }, 500


# class ChargeBeeOperations:
#
#     def __init__(self):
#         pass
#
#     def verify_signature(self, payload: str, chargebee_signature: str) -> bool:
#         """Verify the signature using HMAC SHA256."""
#         computed_signature = hmac.new(
#             CHARGEBEE_WEBHOOK_SECRET.encode(),
#             payload.encode(),
#             hashlib.sha256
#         ).hexdigest()
#
#         return hmac.compare_digest(computed_signature, chargebee_signature)
#
#

class CommonOperations:
    def __init__(self):
        pass


    def hash_password(self,password: str) -> str:

        try:
            # Convert the first name to bytes
            password_bytes = password.encode('utf-8')

            # Generate a salt
            salt = bcrypt.gensalt()

            # Hash the password
            hashed_password = bcrypt.hashpw(password_bytes, salt)

            return hashed_password.decode('utf-8')
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())}\nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="hash_password").exception_handling()
            raise

    def verify_password(self,password: str, hashed_password: str) -> bool:
        try:
            # Convert the first name and hashed password to bytes
            password_bytes = password.encode('utf-8')
            hashed_password_bytes = hashed_password.encode('utf-8')

            # Verify the password
            return bcrypt.checkpw(password_bytes, hashed_password_bytes)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())}\nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="verify_password").exception_handling()
            raise
