name: Build and deploy website
on:
  push:
    branches: [ master ]
    
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: Madia-Application-Solutions/cfrvision-frontend
 
jobs:
  build-and-push-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Log in to the Container registry
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
     # - name: Create dev env
     #   run: |
     #     mv .env.dev .env
     #     rm -rf .env.*
      - name: Get Github action IP
        id: ip
        uses: haythem/public-ip@v1.2
 
      - name: Setting staging environment variables..
        run: |
          echo "AWS_DEFAULT_REGION=eu-west-1" >> $GITHUB_ENV
          echo "AWS_SG_NAME_STG=sg-06de77780e54de0ca" >> $GITHUB_ENV
      - name: Add Github Actions IP to Security group
        run: |
          aws ec2 authorize-security-group-ingress --group-id ${{ env.AWS_SG_NAME_STG }} --protocol tcp --port 22 --cidr ${{ steps.ip.outputs.ipv4 }}/32    
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}
 
      - name: Build and push test Docker image
        uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
        with:
          context: .
          push: true
          build-args: |
            APP_NAME=cfrvision-app
            NODE_ENV=production
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
 
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh/
          echo "$SSH_KEY" > ~/.ssh/prod.key
          chmod 600 ~/.ssh/prod.key
          cat >>~/.ssh/config <<END
            Host prod
            HostName $SSH_HOST
            User $SSH_USER
            IdentityFile ~/.ssh/prod.key
            StrictHostKeyChecking no
          END
        env:
          SSH_USER: ${{ secrets.SSH_USER_PROD }}
          SSH_KEY: ${{ secrets.SSH_KEY_PROD }}
          SSH_HOST: ${{ secrets.SSH_HOST_PROD }}
      - name: Deploy Stage Build
        run: |
          ssh prod 'echo "${{ secrets.GITHUB_TOKEN }}" | sudo docker login ghcr.io -u "${{ github.actor }}" --password-stdin'
          ssh prod 'cd /var/www/html/cfrvision-fronted; sudo docker system prune -f; sudo docker-compose pull; sudo docker-compose down; sudo docker-compose up -d --force-recreate'
 
 
      - name: Remove Github Actions IP from security group
        run: |
          aws ec2 revoke-security-group-ingress --group-id ${{ env.AWS_SG_NAME_STG }} --protocol tcp --port 22 --cidr ${{ steps.ip.outputs.ipv4 }}/32
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}
