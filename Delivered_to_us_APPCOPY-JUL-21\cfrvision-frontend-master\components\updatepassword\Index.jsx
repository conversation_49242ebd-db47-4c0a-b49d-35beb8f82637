"use client";

import AnimatedText from "@/components/common/AnimatedText";
import React, { useEffect, useState } from "react";

import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import { valibotResolver } from "@hookform/resolvers/valibot";
import dynamic from "next/dynamic";
import { useSearchParams } from "next/navigation";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const schema = v.pipe(
  v.object({
    new_password: v.pipe(
      v.string(),
      v.nonEmpty("Password is required"),
      v.minLength(8, "Password must be at least 8 characters long"),
      v.regex(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{5,}$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character"
      )
    ),
    confirm_password: v.pipe(
      v.string(),
      v.nonEmpty("Confirm password is required")
    ),
  }),
  v.forward(
    v.partialCheck(
      [["new_password"], ["confirm_password"]],

      (input) => input.new_password === input.confirm_password,
      "Confirm password should match with password"
    ),
    ["confirm_password"]
  )
);

const UpdatePassword = () => {
  const { contextHolder, showToast } = Toaster();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [userId, setUserId] = useState("");

  useEffect(() => {
    if (token) {
      verifyToken();
    }
  }, []);

  const verifyToken = async () => {
    try {
      const response = await axiosInstance.post("/v1/auth/verify-token", {
        token,
      });
      setUserId(response?.data?.data?.user_id);
    } catch (error) {
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
      console.log("err", error);
      setTimeout(() => {
        window.location.href = "/login";
      }, 2000);
    }
  };
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      new_password: "",
      confirm_password: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      const response = await axiosInstance.post("/v1/auth/set-password", {
        user_id: userId,
        password: data?.new_password,
      });
      showToast({
        type: "sucess",
        message: response?.data?.message,
      });
      setTimeout(() => {
        window.location.href = "/login";
      }, 2000);
    } catch (error) {
      console.log("err", error);
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      <main id="main">
        <section className="page-section pt-0 pb-0">
          <ParallaxContainer
            className="page-section bg-gray-light-1 bg-light-alpha-90 parallax-5"
            style={{
              backgroundImage: "url(/assets/background.png)",
            }}
          >
            <>
              <div className="position-absolute top-0 bottom-0 start-0 end-0 bg-gradient-white background-img" />
              <div className="container position-relative pt-50">
                {/* Section Content */}
                <div className="text-center">
                  <div className="row">
                    {/* Page Title */}
                    <div className="col-md-8 offset-md-2">
                      <h2
                        className="section-caption-slick mb-30 mb-sm-20 wow fadeInUp"
                        data-wow-duration="1.2s"
                      >
                        Account
                      </h2>
                      <h1 className="mb-0">
                        <span
                          className="wow charsAnimIn"
                          data-splitting="chars"
                        >
                          <AnimatedText text="Update your password." />
                        </span>
                      </h1>
                    </div>
                    {/* End Page Title */}
                  </div>
                </div>
                {/* End Section Content */}
              </div>
            </>
          </ParallaxContainer>
        </section>
        <>
          <>
            {/* Section */}
            <section className="page-section container pt-0">
              <div className="row">
                <div className="col-md-6 offset-md-3">
                  <form
                    noValidate
                    autoComplete="off"
                    onSubmit={handleSubmit(onSubmit)}
                    className="form contact-form"
                  >
                    <div className="mb-30">
                      {/*New Password */}
                      <div className="form-group">
                        <label htmlFor="newpassword">New Password</label>
                        <Controller
                          name="new_password"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="password"
                              name="new_password"
                              id="new_password"
                              className="input-md round-large form-control"
                              placeholder="Enter new password"
                              required
                              value={field.value}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              ref={field.ref}
                            />
                          )}
                        />
                        {errors?.new_password && (
                          <span className="text-red">
                            {errors?.new_password.message}
                          </span>
                        )}
                      </div>
                      {/*Confirm Password */}
                      <div className="form-group">
                        <label htmlFor="confirmpassword">
                          Confirm Password
                        </label>
                        <Controller
                          name="confirm_password"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="password"
                              name="confirm_password"
                              id="confirm_password"
                              className="input-md round-large form-control"
                              placeholder="Re-enter new password"
                              required
                              value={field.value}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              ref={field.ref}
                            />
                          )}
                        />
                        {errors?.confirm_password && (
                          <span className="text-red">
                            {errors?.confirm_password.message}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="row mb-30">
                      <div className="col-6">
                        {/* Inform Tip */}
                        <div className="form-tip pt-10 back-to-login text-secondary">
                          <i
                            className="mi-arrow-left size-24"
                            style={{ marginRight: "5px" }}
                          />
                          <a
                            href="/login"
                            style={{ textDecoration: "none" }}
                            className="text-secondary"
                          >
                            Back to Login
                          </a>
                        </div>
                      </div>
                      <div className="col-6">
                        {/* Send Button */}
                        <div className="text-end">
                          <button
                            className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10"
                            id="update_password-btn"
                          >
                            <span>Save Changes</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </section>
            {/* End Section */}
            {/* Divider */}
          </>

          {/* End Divider */}
        </>
      </main>
    </>
  );
};

export default UpdatePassword;
