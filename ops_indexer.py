#!/usr/bin/env python3
"""
Startup Ops Indexer & Daily Dashboard
Main entry point for the indexing system.
"""
import argparse
import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import List

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from config_loader import load_config
from ingest import FileDiscovery, ContentExtractor
from classifier import FileClassifier, RulesEngine
from task_manager import TaskGenerator, TaskStateManager
from output_generator import OutputGenerator
from dashboard_renderer import DashboardRenderer
from models import ActivityLogEntry

# Setup rich console and logging
console = Console()
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(console=console, rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)


class StartupOpsIndexer:
    """Main indexer class that orchestrates the entire pipeline."""

    def __init__(self, root_path: str, config_path: str = None,
                 output_dir: str = ".ops_out", enable_ocr: bool = False,
                 enable_llm: bool = False, llm_provider: str = "openai",
                 llm_api_key: str = None, google_credentials_path: str = None,
                 enable_google_vision: bool = False):
        self.root_path = Path(root_path)
        self.output_dir = Path(output_dir)
        self.enable_ocr = enable_ocr
        self.enable_llm = enable_llm
        self.llm_provider = llm_provider

        # Load configuration
        self.config = load_config(config_path)

        # Initialize Google services if using Google provider
        google_ocr = None
        if llm_provider == "google" or enable_google_vision:
            try:
                from google_llm_classifier import GoogleVisionOCR
                google_ocr = GoogleVisionOCR(credentials_path=google_credentials_path)
                logger.info("Google Vision OCR initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Google Vision OCR: {e}")

        # Initialize components
        self.file_discovery = FileDiscovery(self.config)
        self.content_extractor = ContentExtractor(
            enable_ocr=enable_ocr,
            use_google_vision=(llm_provider == "google" or enable_google_vision),
            google_ocr=google_ocr
        )

        # Initialize classifier (enhanced with LLM if enabled)
        if enable_llm:
            if llm_provider == "google":
                try:
                    from enhanced_google_classifier import GoogleEnhancedClassifier
                    self.classifier = GoogleEnhancedClassifier(
                        self.config,
                        gemini_api_key=llm_api_key,
                        google_credentials_path=google_credentials_path,
                        enable_vision=enable_google_vision
                    )
                    logger.info("Google Gemini classification enabled")
                except Exception as e:
                    logger.warning(f"Failed to initialize Google classifier: {e}")
                    self.classifier = FileClassifier(self.config)
            else:
                try:
                    from llm_classifier import LLMClassifier, EnhancedClassifier
                    llm_classifier = LLMClassifier(
                        provider=llm_provider,
                        api_key=llm_api_key
                    )
                    self.classifier = EnhancedClassifier(self.config, llm_classifier)
                    logger.info(f"LLM classification enabled with {llm_provider}")
                except Exception as e:
                    logger.warning(f"Failed to initialize LLM classifier: {e}")
                    self.classifier = FileClassifier(self.config)
        else:
            self.classifier = FileClassifier(self.config)

        self.rules_engine = RulesEngine(self.config)
        self.task_generator = TaskGenerator(self.config)
        self.state_manager = TaskStateManager(str(self.output_dir))
        self.output_generator = OutputGenerator(str(self.output_dir))
        self.dashboard_renderer = DashboardRenderer(output_dir=str(self.output_dir))

        # Ensure output directory exists
        self.output_dir.mkdir(exist_ok=True)
    
    def run_full_pipeline(self) -> None:
        """Run the complete indexing pipeline."""
        start_time = datetime.now()
        
        console.print(f"\n🚀 [bold blue]Starting Startup Ops Indexer[/bold blue]")
        console.print(f"📁 Root path: {self.root_path}")
        console.print(f"📤 Output dir: {self.output_dir}")
        console.print(f"🔍 OCR enabled: {self.enable_ocr}")
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                
                # Step 1: File Discovery
                task1 = progress.add_task("🔍 Discovering files...", total=None)
                files = list(self.file_discovery.discover_files(str(self.root_path)))
                progress.update(task1, description=f"✅ Found {len(files)} files")
                
                # Step 2: Content Extraction
                task2 = progress.add_task("📄 Extracting content...", total=len(files))
                for i, file_info in enumerate(files):
                    files[i] = self.content_extractor.extract_content(file_info, str(self.root_path))
                    progress.update(task2, advance=1)
                
                # Step 3: Classification
                task3 = progress.add_task("🏷️  Classifying files...", total=len(files))
                for i, file_info in enumerate(files):
                    files[i] = self.classifier.classify_file(file_info)
                    progress.update(task3, advance=1)
                
                # Step 4: Generate Candidates
                task4 = progress.add_task("⚙️  Applying rules...", total=None)
                process_candidates = self.rules_engine.generate_process_candidates(files)
                opportunity_candidates = self.rules_engine.generate_opportunity_candidates(files)
                progress.update(task4, description=f"✅ Generated {len(process_candidates)} processes, {len(opportunity_candidates)} opportunities")
                
                # Step 5: Generate Tasks
                task5 = progress.add_task("📋 Generating tasks...", total=None)
                new_tasks = []
                new_tasks.extend(self.task_generator.generate_tasks_from_processes(process_candidates))
                new_tasks.extend(self.task_generator.generate_tasks_from_opportunities(opportunity_candidates))
                new_tasks.extend(self.task_generator.generate_oneoff_tasks(files))
                progress.update(task5, description=f"✅ Generated {len(new_tasks)} tasks")
                
                # Step 6: State Merge
                task6 = progress.add_task("🔄 Merging with existing state...", total=None)
                existing_tasks = self.state_manager.load_existing_tasks()
                merged_tasks = self.state_manager.merge_tasks(new_tasks, existing_tasks)
                progress.update(task6, description=f"✅ Merged to {len(merged_tasks)} total tasks")
                
                # Step 7: Save Tasks
                task7 = progress.add_task("💾 Saving tasks...", total=None)
                self.state_manager.save_tasks(merged_tasks)
                progress.update(task7, description="✅ Tasks saved")
                
                # Step 8: Generate Outputs
                task8 = progress.add_task("📝 Generating outputs...", total=None)
                self.output_generator.generate_all_outputs(files, merged_tasks, process_candidates, opportunity_candidates)
                progress.update(task8, description="✅ Output files generated")
                
                # Step 9: Render Dashboard
                task9 = progress.add_task("🌐 Rendering dashboard...", total=None)
                self.dashboard_renderer.render_dashboard(merged_tasks)
                progress.update(task9, description="✅ Dashboard rendered")
        
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        console.print(f"\n✨ [bold green]Indexing Complete![/bold green]")
        console.print(f"⏱️  Duration: {duration.total_seconds():.1f} seconds")
        console.print(f"📁 Files processed: {len(files)}")
        console.print(f"📋 Total tasks: {len(merged_tasks)}")
        console.print(f"⏳ Pending tasks: {len([t for t in merged_tasks if t.status.value == 'pending'])}")
        
        # Show output files
        console.print(f"\n📤 [bold]Output Files:[/bold]")
        output_files = [
            "documents.txt", "processes.txt", "opportunities.txt", 
            "tasks.json", "dashboard.html", "summary.txt"
        ]
        for filename in output_files:
            file_path = self.output_dir / filename
            if file_path.exists():
                console.print(f"   ✅ {filename}")
            else:
                console.print(f"   ❌ {filename} (missing)")
        
        # Dashboard link
        dashboard_path = self.output_dir / "dashboard.html"
        console.print(f"\n🌐 [bold]Dashboard:[/bold] file://{dashboard_path.absolute()}")
        
        # Log activity
        self._log_run_completion(len(files), len(merged_tasks), duration)
    
    def _log_run_completion(self, file_count: int, task_count: int, duration) -> None:
        """Log the completion of a run."""
        try:
            activity_log = self.output_dir / "activity.log"
            entry = ActivityLogEntry(
                action="indexer_run_completed",
                details={
                    "files_processed": file_count,
                    "total_tasks": task_count,
                    "duration_seconds": duration.total_seconds(),
                    "ocr_enabled": self.enable_ocr
                }
            )
            
            with open(activity_log, 'a', encoding='utf-8') as f:
                f.write(str(entry) + '\n')
                
        except Exception as e:
            logger.warning(f"Failed to log activity: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Startup Ops Indexer & Daily Dashboard",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python ops_indexer.py --root /path/to/startup/files
  python ops_indexer.py --root . --config custom_config.yml --enable-ocr
  python ops_indexer.py --root ~/Documents/Business --output-dir ~/ops_dashboard
        """
    )
    
    parser.add_argument(
        "--root", 
        required=True,
        help="Root directory to scan for files (STARTUP_ROOT)"
    )
    
    parser.add_argument(
        "--config",
        help="Path to config.yml file (default: config.yml)"
    )
    
    parser.add_argument(
        "--output-dir",
        default=".ops_out",
        help="Output directory for generated files (default: .ops_out)"
    )
    
    parser.add_argument(
        "--enable-ocr",
        action="store_true",
        help="Enable OCR for images and PDFs (requires pytesseract)"
    )

    parser.add_argument(
        "--enable-llm",
        action="store_true",
        help="Enable LLM-powered classification and task extraction"
    )

    parser.add_argument(
        "--llm-provider",
        choices=["openai", "anthropic", "google"],
        default="openai",
        help="LLM provider to use (default: openai)"
    )

    parser.add_argument(
        "--llm-api-key",
        help="API key for LLM provider (or set OPENAI_API_KEY/ANTHROPIC_API_KEY/GOOGLE_API_KEY env var)"
    )

    parser.add_argument(
        "--google-credentials",
        help="Path to Google Cloud service account credentials JSON file"
    )

    parser.add_argument(
        "--enable-google-vision",
        action="store_true",
        help="Enable Google Cloud Vision API for enhanced OCR (requires credentials)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate root path
    root_path = Path(args.root)
    if not root_path.exists():
        console.print(f"❌ [red]Error:[/red] Root path does not exist: {root_path}")
        sys.exit(1)
    
    if not root_path.is_dir():
        console.print(f"❌ [red]Error:[/red] Root path is not a directory: {root_path}")
        sys.exit(1)
    
    try:
        # Create and run indexer
        indexer = StartupOpsIndexer(
            root_path=str(root_path),
            config_path=args.config,
            output_dir=args.output_dir,
            enable_ocr=args.enable_ocr,
            enable_llm=args.enable_llm,
            llm_provider=args.llm_provider,
            llm_api_key=args.llm_api_key,
            google_credentials_path=args.google_credentials,
            enable_google_vision=args.enable_google_vision
        )
        
        indexer.run_full_pipeline()
        
    except KeyboardInterrupt:
        console.print("\n⚠️  [yellow]Operation cancelled by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n❌ [red]Error:[/red] {e}")
        if args.verbose:
            console.print_exception()
        sys.exit(1)


if __name__ == "__main__":
    main()
