"use client";

import moment from "moment";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from "react-bootstrap";
import "./parsedResponse.css";
import axiosInstance from "@/utlis/axios";
import { useState } from "react";
import Toaster from "../common/Toaster";

const ViewValidationRequestModal = ({ show, setShow, data }) => {
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [printLoading, setPrintLoading] = useState(false);
  const { contextHolder, showToast } = Toaster();

  const handleDownload = async (id) => {
    try {
      setDownloadLoading(true);
      const response = await axiosInstance.post(
        "/v1/llm_check_request/download-result",
        { id },
        { responseType: "blob" }
      );
      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = url;
      a.download = "validation_request_details.pdf"; // 👈 rename as needed
      document.body.appendChild(a);
      a.click();
      a.remove();

      window.URL.revokeObjectURL(url);
      setDownloadLoading(false);
    } catch (error) {
      console.error("Failed to fetch files:", error);
      setDownloadLoading(false);
      showToast({
        type: "error",
        message: "llm-check not found.",
      });
    }
  };

  const handlePrint = async (id) => {
    try {
      setPrintLoading(true);
      const response = await axiosInstance.post(
        "/v1/llm_check_request/download-result",
        { id },
        { responseType: "blob" }
      );
      const blob = new Blob([response.data], { type: "application/pdf" });
      const blobUrl = URL.createObjectURL(blob);

      // Open the PDF in a new window for print preview
      const printWindow = window.open(blobUrl);

      // Optional: automatically open the print dialog
      printWindow.onload = () => {
        printWindow.focus();
        printWindow.print();
      };
      setPrintLoading(false);
    } catch (error) {
      console.error("Failed to fetch files:", error);
      showToast({
        type: "error",
        message: "llm-check not found.",
      });
      setPrintLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <Modal
        show={show}
        onHide={() => setShow(false)}
        centered
        size="lg"
        backdrop="static"
      >
        <Modal.Header closeButton>
          <Modal.Title>Validation Request Detail</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row className="g-3">
            <Col md={12}>
              <h3>{data?.institution_name}</h3>
            </Col>
            <Col md={12}>
              <strong>File Name:</strong> <span>{data?.file_name}</span>
            </Col>
            <Col md={4}>
              <strong>Draft/Final:</strong> <span>{data?.draft_final}</span>
            </Col>
            <Col md={4}>
              <strong>FY:</strong> <span>{data?.fy_end}</span>
            </Col>
            <Col md={4}>
              <strong>Version:</strong> <span>{data?.version}</span>
            </Col>
            <Col md={12}>
              <strong>Validation Category:</strong>{" "}
              <span>{data?.ai_check?.category?.category_name}</span>
            </Col>
            <Col md={12}>
              <strong>Validation Check Text :</strong>{" "}
              <span>{data?.ai_check?.check_name}</span>
            </Col>

            <Col md={12}>
              <strong>Result:</strong>
              {data?.llm_check_response?.length ? (
                data?.llm_check_response?.map((item) => (
                  <div className="mb-4">
                    <p className="mb-1">
                      Created At:{" "}
                      {moment.unix(item?.created_at).format("MM/DD/YYYY HH:MM")}
                    </p>
                    <div
                      className="paresdResponseTable"
                      dangerouslySetInnerHTML={{
                        __html: item?.parsed_response,
                      }}
                    />
                    <hr />
                  </div>
                ))
              ) : (
                <p>No Result Found.</p>
              )}
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShow(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => handleDownload(data?.request_id)}
            disabled={downloadLoading}
          >
            Download
          </Button>
          <Button
            variant="success"
            onClick={() => handlePrint(data?.request_id)}
            disabled={printLoading}
          >
            Print
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ViewValidationRequestModal;
