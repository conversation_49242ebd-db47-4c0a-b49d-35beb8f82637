import copy
import datetime
import secrets

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import logger
from app.database.db_operations.crud import <PERSON>bcrud
from app.database.models import (
    User
)
from app.operations.common_operations.common_operations import CommonOperations
from app.utils.decorators import async_exception_logger
from app.utils.exception_handling import ExceptionHandling
from app.utils.mail_utils import MailOperations


class HandleWebhook:

    def __init__(self):
        pass

    @async_exception_logger
    async def parse_chargebee_request(self, event_type, content, db, re_run_count=0):
        """
        Parse and handle different Chargebee event types to update or create records in the database.

        This method listens to various Chargebee webhook event types such as item creation, subscription changes,
        payment source updates, customer modifications, and invoice generation. Based on the event type, it processes
        the data and calls corresponding operations to update or create records in the database. It also handles customer
        and payment-related events such as adding new cards, updating customer information, or deleting customers.

        **Args**:
            event_type (str): The type of event received from Chargebee (e.g., "item_created", "subscription_created").
            content (dict): The event content containing the data to be processed (e.g., customer, subscription, invoice).
            db (AsyncSession): The database session used to query and store data.

        **Returns**:
            None: This function does not return any value. It performs database operations based on the event type.
        """
        customer_ops = CustomerOperations()
        # subscription_ops = SubscriptionOperations()
        # item_ops = ItemOperations()
        # invoice_ops = InvoiceOperations()
        logger.info(msg=f'received chargebee webhook for {event_type} at {str(datetime.datetime.now())}')
        logger.info(msg=f'{content} ')

        try:
            # if event_type == "item_family_created":
            #
            #     item_family_data = content.get("item_family", {})
            #     await item_ops.item_family_create(data=item_family_data, db=db)
            #
            # elif event_type == "item_created":
            #
            #     item_data = content.get("item", {})
            #     await item_ops.item_create(data=item_data, db=db)
            #
            # elif event_type == "item_price_created":
            #
            #     item_price_data = content.get("item_price", {})
            #     await item_ops.item_price_create(data=item_price_data, db=db)
            #
            # elif event_type in ["item_family_updated", "item_family_deleted"]:
            #
            #     item_family_data = content.get("item_family", {})
            #     await item_ops.item_family_update(data=item_family_data, db=db)
            #
            # elif event_type in ["item_updated", "item_deleted"]:
            #
            #     item_data = content.get("item", {})
            #     await item_ops.item_update(data=item_data, db=db)
            #
            # elif event_type in ["item_price_deleted", "item_price_updated"]:
            #
            #     item_price_data = content.get("item_price", {})
            #     await item_ops.item_price_update(data=item_price_data, db=db)
            #
            # elif event_type == "card_added":
            #
            #     customer_info = content.get("customer", {})
            #     card_info = content.get("card", {})
            #     payment_method = customer_info.get("payment_method", {})
            #
            #     payment_method["id"] = card_info.get("payment_source_id")
            #
            #     result = await db.execute(
            #         select(User).where(User.id == customer_info.get("id"), User.is_deleted == False)
            #     )
            #     user_obj = result.scalars().first()
            #
            #     if not user_obj:
            #
            #         user_obj = await customer_ops.customer_create(data=content, db=db)
            #     else:
            #
            #         await customer_ops.customer_update(data=content, db=db, user=user_obj)
            #
            #     result = await db.execute(
            #         select(Card).where(
            #             Card.user_id == user_obj.id,
            #             Card.payment_source_id == card_info.get("payment_source_id"),
            #             Card.is_deleted == False,
            #         )
            #     )
            #     card_obj = result.scalars().first()
            #
            #     if card_obj:
            #         await customer_ops.update_card_data(data=card_info, user_id=user_obj.id, db=db)
            #     else:
            #         await customer_ops.create_card_data(data=card_info, user_id=user_obj.id, db=db)
            #
            if event_type in ["subscription_created","subscription_changed","subscription_activated","subscription_reactivated","subscription_renewed","subscription_canceled","subscription_resumed", "subscription_cancelled"]:

                customer_info = content.get("customer", {})
                subscription_info = content.get("subscription", {})

                customer_info['id'] = customer_info.get("id", subscription_info.get('customer_id'))

                result = await db.execute(
                    select(User).where(User.is_deleted == False, User.id == customer_info.get("id"))
                )
                existing_data = result.scalars().first()


                if existing_data:
                    await customer_ops.customer_update(data=content, db=db, user=existing_data)

                else:
                    await customer_ops.customer_create(data=content, db=db)
                    full_name = f"{customer_info.get("billing_address", {}).get('first_name', '')} {customer_info.get("billing_address", {}).get('last_name', '')}" or customer_info.get("email", "")
                    await MailOperations().send_user_registration_mail(customer_email=customer_info.get("email"),customer_name=full_name, temporary_password=customer_info.get("id"))


            if event_type in ["customer_created"]:
                print(f"Content: {content}")
                customer_info = content.get("customer", {})
                print(f"Customer Info: {customer_info}")
                result = await db.execute(
                    select(User).where(User.id == customer_info.get("id", customer_info.get("user_id")))
                )
                user_obj = result.scalars().first()
                if user_obj:
                    await customer_ops.customer_update(data=content, db=db, user=user_obj)
                else:
                    await customer_ops.customer_create(data=content, db=db)
            if event_type in ["customer_changed"]:
                print(f"Content: {content}")
                customer_info = content.get("customer", {})
                print(f"Customer Info: {customer_info}")
                result = await db.execute(
                    select(User).where(User.id == customer_info.get("id", customer_info.get("user_id")))
                )
                user_obj = result.scalars().first()
                if user_obj:
                    await customer_ops.customer_update(data=content, db=db, user=user_obj)
                else:
                    await customer_ops.customer_create(data=content, db=db)
                    full_name = f"{customer_info.get("billing_address", {}).get('first_name', '')} {customer_info.get("billing_address", {}).get('last_name', '')}" or customer_info.get(
                        "email", "")
                    await MailOperations().send_user_registration_mail(customer_email=customer_info.get("email"),
                                                                       customer_name=full_name,
                                                                       temporary_password=customer_info.get("id"))

            # elif event_type == "payment_source_added":
            #
            #     payment_source = content.get("payment_source", {})
            #     if payment_source:
            #         customer_info = content.get("customer", {})
            #
            #         payment_source["id"] = payment_source.get("id")
            #         if payment_source.get("card"):
            #             payment_source["card"]["payment_source_id"] = payment_source.get("id")
            #
            #         result = await db.execute(
            #             select(PaymentMethod).where(
            #                 PaymentMethod.id == payment_source.get("id"),
            #                 PaymentMethod.is_deleted == False
            #             )
            #         )
            #         payment_data = result.scalars().first()
            #         if not payment_data:
            #             result = await db.execute(
            #                 select(PaymentMethod).where(
            #                     PaymentMethod.reference_id == payment_source.get("reference_id"),
            #                     PaymentMethod.is_deleted == False
            #                 )
            #             )
            #             payment_data = result.scalars().first()
            #
            #         customer_id = customer_info.get("id")
            #
            #         if not payment_data:
            #             await customer_ops.create_payment_data(data=payment_source, user_id=customer_id, db=db)
            #             if payment_source.get("card"):
            #                 await customer_ops.create_card_data(
            #                     data=payment_source.get("card"), user_id=customer_id, db=db
            #                 )
            #         else:
            #             await customer_ops.update_payment_data(data=payment_source, user_id=customer_id, db=db,
            #                                                    existing_data=payment_data)
            #             if payment_source.get("card"):
            #                 await customer_ops.update_card_data(
            #                     data=payment_source.get("card"), user_id=customer_id, db=db
            #                 )
            #
            elif event_type == "customer_deleted":

                await customer_ops.customer_delete(data=content, db=db)
            #
            # elif event_type == "invoice_generated":
            #
            #     invoice_data = content.get("invoice", {})
            #     result = await db.execute(
            #         select(Invoice).where(Invoice.is_deleted == False, Invoice.id == invoice_data.get("id"))
            #     )
            #     existing_data = result.scalars().first()
            #
            #     if existing_data:
            #         await invoice_ops.invoice_update(data=content, db=db, existing_data=existing_data)
            #     else:
            #         await invoice_ops.invoice_create(data=invoice_data, db=db)
            #
            # elif event_type == "subscription_changed":
            #     print(content)
            #
            else:
                logger.warning(f"Unhandled event type: {event_type}")


        except Exception as e:
            print(f"failed event type :{event_type}")
            await db.rollback()
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="parse_chargebee_request").exception_handling()
            raise
        return None

#
# class SubscriptionOperations:
#     async def subscription_create(self, data: dict, db: AsyncSession):
#         """
#         Creates a subscription along with its associated subscription items and shipping address.
#
#         This method processes the subscription data received, extracts the subscription items and shipping address,
#         and creates the subscription record in the database. It ensures the subscription items and shipping address
#         are linked to the subscription and stored correctly.
#
#         **Args**:
#             data (dict): A dictionary containing the subscription data, including subscription items and shipping address.
#             db (AsyncSession): The database session used to interact with the database and perform the operations.
#
#         **Returns**:
#             None: This function does not return any value. It performs database operations to create the subscription,
#             items, and shipping address.
#         """
#         try:
#
#             subscription_items = data.pop('subscription_items')[0]
#             shipping_address = data.pop('shipping_address') if data.get('shipping_address') else {}
#             subscription_items['subscription_id'] = data.get('id')
#             shipping_address['subscription_id'] = data.get('id')
#
#             await self.create_subscription(data=data, db=db)
#
#             if subscription_items:
#                 await self.create_subscription_items(data=subscription_items, db=db)
#             if shipping_address:
#                 await self.create_shipping_address(data=shipping_address, db=db)
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="subscription_create").exception_handling()
#             raise
#         return None
#
#     async def create_subscription(self, data: dict, db: AsyncSession):
#         """
#         Create a new subscription.
#
#         This method creates a subscription record in the database based on the provided data.
#
#         **Args**:
#             data (dict): The data for creating a new subscription.
#             db (AsyncSession): The database session used to perform the database operation.
#
#         **Returns**:
#             None: This function does not return any value. It performs the database operation to create the subscription.
#         """
#         try:
#
#             fields = {column.name for column in Subscription.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = Subscription(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="create_subscription").exception_handling()
#             raise
#         return None
#
#     async def subscription_update(self, existing_data, data: dict, subscription_id: str, db: AsyncSession):
#         """
#         Update an existing subscription.
#
#         This method updates an existing subscription record in the database, checking if the subscription exists,
#         and applying the updates provided.
#
#         **Args**:
#             existing_data (Subscription): The existing subscription data to be updated.
#             data (dict): The updated subscription data.
#             subscription_id (str): The ID of the subscription to be updated.
#             db (AsyncSession): The database session used to perform the update operation.
#
#         **Returns**:
#             None: This function does not return any value. It performs the database operation to update the subscription.
#         """
#         try:
#
#             if not existing_data:
#                 result = await db.execute(
#                     select(Subscription).where(Subscription.id == subscription_id, Subscription.is_deleted == False)
#                 )
#                 existing_data = result.scalars().first()
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="subscription_update").exception_handling()
#             raise
#         return None
#
#     async def create_shipping_address(self, data: dict, db: AsyncSession):
#         """
#         Create a shipping address for a subscription.
#
#         This method creates a shipping address record and links it to the subscription.
#
#         **Args**:
#             data (dict): The shipping address data to be created.
#             db (AsyncSession): The database session used to perform the operation.
#
#         **Returns**:
#             None: This function does not return any value. It performs the database operation to create the shipping address.
#         """
#         try:
#
#             fields = {column.name for column in ShippingAddress.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = ShippingAddress(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="create_shipping_address").exception_handling()
#             raise
#         return None
#
#     async def create_subscription_items(self, data: dict, db: AsyncSession):
#         """
#         Create subscription items.
#
#         This method creates subscription item records based on the provided data.
#
#         **Args**:
#             data (dict): The subscription item data to be created.
#             db (AsyncSession): The database session used to perform the operation.
#
#         **Returns**:
#             None: This function does not return any value. It performs the database operation to create the subscription items.
#         """
#         try:
#
#             fields = {column.name for column in SubscriptionItem.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = SubscriptionItem(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="create_subscription_items").exception_handling()
#             raise
#         return None


class CustomerOperations:
    async def customer_create(self, data: dict, db: AsyncSession):
        """ Creates a customer record along with associated payment, profile, billing address, and card data in the database.

        This asynchronous method processes customer creation by first extracting and deep copying the customer data from the input dictionary. It then creates a new customer record and, based on the provided data, conditionally creates related entries such as payment data (if a payment method is present), a customer profile, billing address data, and card data. The method updates the customer data with the new customer's ID before returning the customer object. In case of an exception, the method logs detailed information about the error and input, handles the exception using a dedicated exception handler, and then re-raises the exception.

        **Args**: data (dict): A dictionary containing customer details and related information. Expected keys include 'customer', and optionally 'card', with 'customer' potentially containing 'payment_method' and 'billing_address'. db (AsyncSession): The asynchronous database session used to perform database operations.

        **Returns**: The newly created customer object upon successful execution.

        **Raises**: Exception: Propagates any exception encountered during the customer creation process after logging and handling. """
        user = None
        try:
            print(f"Data1: {data}")

            customer_data = copy.deepcopy(data.get('customer', {}))
            subscription_data = copy.deepcopy(data.get('subscription', {}))
            user = await self.create_customer(data=customer_data, db=db)
            subscription = await self.create_subscription(data=subscription_data, db=db)

            # if customer_data.get('payment_method'):
            #     await self.create_payment_data(data=customer_data.get('payment_method'), user_id=user.id, db=db)
            # if customer_data:
            #     result = await db.execute(
            #         select(UserProfile).where(UserProfile.user_id == user.id, UserProfile.is_deleted == False)
            #     )
            #     existing_data = result.scalars().first()
            #     if existing_data:
            #         await self.update_customer_profile(existing_data=existing_data, data=customer_data, user_id=user.id,
            #                                            db=db)
            #     else:
            #         await self.create_customer_profile(data=customer_data, user_id=user.id, db=db)
            #
            # if customer_data.get('billing_address'):
            #     await self.create_billing_data(data=customer_data.get('billing_address'), user_id=user.id, db=db)
            # if data.get('card'):
            #     await self.create_card_data(data=data.get('card'), user_id=user.id, db=db)
            # customer_data['id'] = user.id

            return user
        except Exception as e:
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="customer_create").exception_handling()
            raise
        return None

    async def customer_update(self, data: dict, db: AsyncSession, user):
        """
        Updates an existing customer's information including profile, billing address, payment method, and card data in the database.

        This asynchronous method processes customer updates by extracting updated customer details from the provided dictionary. It separates billing address, payment method, and card details from the main customer data. The method then performs the following operations:
        - Updates the customer record and either updates an existing customer profile or creates a new one if none exists.
        - Checks for an existing billing address record; if found, updates it, otherwise creates a new billing address entry.
        - Processes the payment method by validating its presence and identifier; it updates an existing payment record or creates a new one if necessary.
        - Updates the associated card data.

        If an exception occurs during any of these operations, the method logs the detailed error along with the local context, invokes a custom exception handler, and then re-raises the exception.

        **Args**:
            data (dict): A dictionary containing updated customer details. Expected keys include 'customer' (which may include 'billing_address' and 'payment_method') and 'card'.
            db (AsyncSession): The asynchronous database session used to execute database operations.
            user: The customer object to be updated, which should have an 'id' attribute used for identification.

        **Returns**:
            None: This method performs update operations on the customer's records and does not return any value.

        **Raises**:
            Exception: Any exception encountered during the update process is logged, handled via a custom exception handler, and re-raised.
        """
        try:
            customer_data = data.get('customer', {})
            # billing_address = customer_data.pop('billing_address') if 'billing_address' in customer_data else {}
            # payment_method = customer_data.pop('payment_method') if 'payment_method' in customer_data else {}
            # card = data.pop('card') if data.get('card','') else {}

            if customer_data:
                await self.update_customer(data=customer_data, user_id=user.id, db=db)
                await self.create_subscription(data=data.get('subscription', {}), db=db)
                # result = await db.execute(
                #     select(UserProfile).where(UserProfile.user_id == user.id, UserProfile.is_deleted == False)
                # )
                # existing_data = result.scalars().first()
                # if existing_data:
                #     await self.update_customer_profile(existing_data=existing_data, data=customer_data, user_id=user.id,
                #                                        db=db)
                # else:
                #     await self.create_customer_profile(data=customer_data, user_id=user.id, db=db)

            # if billing_address:
            #     result = await db.execute(
            #         select(BillingAddress).where(BillingAddress.user_id == user.id, BillingAddress.is_deleted == False)
            #     )
            #     existing_data = result.scalars().first()
            #     if existing_data:
            #         await self.update_billing_data(data=billing_address, user_id=user.id, db=db,
            #                                        existing_data=existing_data)
            #     else:
            #         await self.create_billing_data(data=billing_address, user_id=user.id, db=db)
            #
            # if payment_method:
            #
            #     if not payment_method.get('id'):
            #         payment_method['id'] = card.get('payment_source_id')
            #
            #     result = await db.execute(
            #         select(PaymentMethod).where(PaymentMethod.id == payment_method['id'],
            #                                     PaymentMethod.is_deleted == False)
            #     )
            #     existing_data = result.scalars().first()
            #
            #     if not existing_data:
            #         result = await db.execute(
            #             select(PaymentMethod).where(
            #                 PaymentMethod.reference_id == payment_method.get("reference_id"),
            #                 PaymentMethod.is_deleted == False
            #             )
            #         )
            #         payment_data = result.scalars().first()
            #
            #     if existing_data:
            #         await self.update_payment_data(data=payment_method, user_id=user.id, db=db,
            #                                        existing_data=existing_data)
            #
            #     else:
            #         await self.create_payment_data(data=payment_method, user_id=user.id, db=db)
            #
            # if card:
            #     await self.update_card_data(data=card, user_id=user.id, db=db)

        except Exception as e:
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="customer_update").exception_handling()
            raise
        return None

    async def customer_delete(self, data: dict, db: AsyncSession):
        """
        Soft deletes a customer and associated records by marking them as inactive and deleted.

        This asynchronous method processes a customer deletion request by retrieving the active customer record based on the customer ID provided in the input data. If the customer exists, the method updates the corresponding billing, payment, card, and customer records by setting their 'is_active' flag to False and 'is_deleted' flag to True. This approach ensures that the customer and all associated records remain in the database for potential future auditing or restoration, while effectively disabling them.

        **Args**:
            data (dict): A dictionary containing the deletion details, expected to include a 'customer' key with a nested dictionary that has the customer's 'id'.
            db (AsyncSession): The asynchronous database session used to perform the update operations.

        **Returns**:
            None: The function does not return any value; it performs soft deletion through database update operations.

        **Raises**:
            Exception: Any exceptions encountered during the process are logged, handled using a custom exception handler, and then re-raised.
        """
        try:

            result = await db.execute(
                select(User).where(User.id == data.get('customer')['id'], User.is_deleted == False)
            )
            user = result.scalars().first()
            if user:
                # await self.update_billing_data(
                #     data={'is_active': False, 'is_deleted': True},
                #     user_id=user.id,
                #     db=db
                # )
                # await self.update_payment_data(
                #     data={'is_active': False, 'is_deleted': True},
                #     user_id=user.id,
                #     db=db
                # )
                # await self.update_card_data(
                #     data={'is_active': False, 'is_deleted': True},
                #     user_id=user.id,
                #     db=db
                # )
                await self.update_customer(
                    data={'is_active': False, 'is_deleted': True},
                    user_id=user.id,
                    db=db
                )
        except Exception as e:
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="customer_delete").exception_handling()
            raise
        return None

    async def create_customer(self, data: dict, db: AsyncSession):
        """
        Creates a new customer record in the database.

        This asynchronous method generates a password hash for the customer by constructing a random string from the customer's first name, last name, and email, and then hashing it using a common utility. It filters the input data to include only the fields defined in the User table schema, creates a new User object with this filtered data, adds the object to the database session, and commits the transaction. If successful, it returns the newly created customer object. In the event of an exception, the method logs detailed context and error information, processes the exception with a custom handler, and then re-raises the error.

        **Args**:
            data (dict): A dictionary containing customer details. Expected keys include 'first_name', 'last_name', 'email', and other fields corresponding to the User table columns.
            db (AsyncSession): The asynchronous database session used for executing database operations.

        **Returns**:
            User: The newly created customer object upon successful insertion into the database.

        **Raises**:
            Exception: Any exceptions encountered during the creation process are logged, handled by a custom exception handler, and re-raised.
        """
        try:
            print(f"Data::: {data}")
            data['password_hash'] = CommonOperations().hash_password(data.get('id'))
            fields = {column.name for column in User.__table__.columns}
            filtered_data = {key: value for key, value in data.items() if key in fields}
            print(filtered_data)
            result = await db.execute(
                select(User).where(User.id == data.get('id'), User.is_deleted == False)
            )
            user = result.scalars().first()
            if not user:
                filtered_data['id'] = data.get('id')
                db_obj = User(**filtered_data)
                db.add(db_obj)
                await db.commit()
                return db_obj
            else:
                return user

        except Exception as e:
            print(str(e))
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="create_customer").exception_handling()
            raise
        return None

    async def create_subscription(self, data: dict, db: AsyncSession):
        """
        Creates a new customer record in the database.

        This asynchronous method generates a password hash for the customer by constructing a random string from the customer's first name, last name, and email, and then hashing it using a common utility. It filters the input data to include only the fields defined in the User table schema, creates a new User object with this filtered data, adds the object to the database session, and commits the transaction. If successful, it returns the newly created customer object. In the event of an exception, the method logs detailed context and error information, processes the exception with a custom handler, and then re-raises the error.

        **Args**:
            data (dict): A dictionary containing customer details. Expected keys include 'first_name', 'last_name', 'email', and other fields corresponding to the User table columns.
            db (AsyncSession): The asynchronous database session used for executing database operations.

        **Returns**:
            User: The newly created customer object upon successful insertion into the database.

        **Raises**:
            Exception: Any exceptions encountered during the creation process are logged, handled by a custom exception handler, and re-raised.
        """
        try:
            print(f"Subscription Data::: {data}")
            user_id = data.get('customer_id')
            if user_id:
                result = await db.execute(
                    select(User).where(User.id == user_id, User.is_deleted == False)
                )
                user = result.scalars().first()
                if user:
                    user.subscription_start = data.get('current_term_start')
                    user.subscription_end = data.get('current_term_end')
                    await db.commit()
                    await db.refresh(user)
        except Exception as e:
            print(str(e))
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="create_customer").exception_handling()
            raise

    # async def create_customer_profile(self, data: dict, user_id: str, db: AsyncSession):
    #     """
    #     Creates a customer profile in the database.
    #
    #     This asynchronous method removes the 'id' key from the input data if present and assigns the provided user_id to the data. It then filters the data to include only the fields defined in the UserProfile table schema, creates a new UserProfile object using the filtered data, adds it to the database session, and commits the transaction. If an exception occurs, it logs the input and error details, handles the exception with a custom exception handler, and then re-raises the exception.
    #
    #     **Args**:
    #         data (dict): A dictionary containing customer profile details. The 'id' key is removed if it exists.
    #         user_id (str): The identifier of the user associated with the customer profile.
    #         db (AsyncSession): The asynchronous database session used to execute database operations.
    #
    #     **Returns**:
    #         None: This function does not return any value; it only creates and commits a new customer profile record.
    #
    #     **Raises**:
    #         Exception: Any exceptions encountered during profile creation are logged, handled by a custom exception handler, and re-raised.
    #     """
    #     try:
    #
    #         data.pop('id', None)
    #         data['user_id'] = user_id
    #         fields = {column.name for column in UserProfile.__table__.columns}
    #         filtered_data = {key: value for key, value in data.items() if key in fields}
    #         db_obj = UserProfile(**filtered_data)
    #         db.add(db_obj)
    #         await db.commit()
    #     except Exception as e:
    #         logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
    #         ExceptionHandling(e=str(e), function_name="create_customer_profile").exception_handling()
    #         raise
    #     return None
    #
    # async def create_billing_data(self, data: dict, user_id: str, db: AsyncSession):
    #     """ Creates a billing address record in the database.
    #
    #     This asynchronous method processes the creation of billing address data by first associating it with the provided `user_id`. It filters the input dictionary to retain only valid fields present in the `BillingAddress` model. A new billing address record is then created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
    #
    #     **Args**:
    #         data (dict): A dictionary containing billing address details. Only keys matching the `BillingAddress` model fields are retained.
    #         user_id (str): The unique identifier of the user for whom the billing address is being created.
    #         db (AsyncSession): The asynchronous database session used to perform the database operations.
    #
    #     **Returns**:
    #         None: This method does not return a value upon successful execution.
    #
    #     **Raises**:
    #         Exception: If an error occurs during the billing data creation process, the exception is logged, handled, and re-raised.
    #     """
    #
    #     try:
    #
    #         data['user_id'] = user_id
    #         fields = {column.name for column in BillingAddress.__table__.columns}
    #         filtered_data = {key: value for key, value in data.items() if key in fields}
    #         db_obj = BillingAddress(**filtered_data)
    #         db.add(db_obj)
    #         await db.commit()
    #     except Exception as e:
    #         logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
    #         ExceptionHandling(e=str(e), function_name="create_billing_data").exception_handling()
    #         raise
    #     return None
    #
    # async def create_payment_data(self, data: dict, user_id: str, db: AsyncSession):
    #     """ Creates a payment method record in the database.
    #
    #     This asynchronous method processes the creation of a payment method record by associating it with the provided `user_id`. If an `id` is not present in the input data, it assigns the `reference_id` as the `id`. The method then filters the input dictionary to retain only valid fields present in the `PaymentMethod` model. A new payment method record is created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
    #
    #     **Args**:
    #         data (dict): A dictionary containing payment method details. Only keys matching the `PaymentMethod` model fields are retained.
    #         user_id (str): The unique identifier of the user for whom the payment method is being created.
    #         db (AsyncSession): The asynchronous database session used to perform the database operations.
    #
    #     **Returns**:
    #         None: This method does not return a value upon successful execution.
    #
    #     **Raises**:
    #         Exception: If an error occurs during the payment method creation process, the exception is logged, handled, and re-raised.
    #     """
    #
    #     try:
    #
    #         data['user_id'] = user_id
    #         if not data.get('id'):
    #             data['id'] = data['reference_id']
    #         fields = {column.name for column in PaymentMethod.__table__.columns}
    #         filtered_data = {key: value for key, value in data.items() if key in fields}
    #         db_obj = PaymentMethod(**filtered_data)
    #         db.add(db_obj)
    #         await db.commit()
    #     except Exception as e:
    #         logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
    #         ExceptionHandling(e=str(e), function_name="create_payment_data").exception_handling()
    #         raise
    #     return None
    #
    # async def create_card_data(self, data: dict, user_id: str, db: AsyncSession):
    #     """ Creates a card record in the database.
    #
    #     This asynchronous method processes the creation of a card record by associating it with the provided `user_id`. It ensures that the `brand` field is populated by using the value from `card_type` if present, or falling back to the existing `brand` value. The method then filters the input dictionary to retain only valid fields present in the `Card` model. A new card record is created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
    #
    #     **Args**:
    #         data (dict): A dictionary containing card details. Only keys matching the `Card` model fields are retained.
    #         user_id (str): The unique identifier of the user for whom the card is being created.
    #         db (AsyncSession): The asynchronous database session used to perform the database operations.
    #
    #     **Returns**:
    #         None: This method does not return a value upon successful execution.
    #
    #     **Raises**:
    #         Exception: If an error occurs during the card creation process, the exception is logged, handled, and re-raised.
    #     """
    #
    #     try:
    #
    #         data['user_id'] = user_id
    #         data['brand'] = data.get('card_type', data.get('brand'))
    #         fields = {column.name for column in Card.__table__.columns}
    #         filtered_data = {key: value for key, value in data.items() if key in fields}
    #         db_obj = Card(**filtered_data)
    #         db.add(db_obj)
    #         await db.commit()
    #     except Exception as e:
    #         logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
    #         ExceptionHandling(e=str(e), function_name="create_card_data").exception_handling()
    #         raise
    #     return None

    async def update_customer(self, data: dict, user_id: str, db: AsyncSession):
        """ Updates an existing customer record in the database.

        This asynchronous method retrieves the existing customer record using the provided `user_id`, ensuring that the customer has not been marked as deleted. If the customer exists, it updates the record with the provided data using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.

        **Args**:
            data (dict): A dictionary containing customer details to be updated.
            user_id (str): The unique identifier of the customer whose data is being updated.
            db (AsyncSession): The asynchronous database session used to perform the database operations.

        **Returns**:
            None: This method does not return a value upon successful execution.

        **Raises**:
            Exception: If an error occurs during the customer update process, the exception is logged, handled, and re-raised.
        """

        try:

            result = await db.execute(
                select(User).where(User.id == user_id, User.is_deleted == False)
            )
            existing_data = result.scalars().first()
            await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
        except Exception as e:
            logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
            ExceptionHandling(e=str(e), function_name="update_customer").exception_handling()
            raise
        return None

#     async def update_customer_profile(self, existing_data, data: dict, user_id: str, db: AsyncSession):
#         """ Updates an existing customer profile record in the database.
#
#         This asynchronous method updates a customer's profile data by modifying the relevant attributes of the `existing_data` record. If `existing_data` is not provided, it retrieves the customer's profile using the `user_id`, ensuring that the profile has not been marked as deleted. The function then updates the record using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             existing_data: The existing customer profile record to be updated. If `None`, the function retrieves the profile from the database.
#             data (dict): A dictionary containing customer profile details to be updated. The `id` key is mapped to `user_id` before updating.
#             user_id (str): The unique identifier of the customer whose profile data is being updated.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the customer profile update process, the exception is logged, handled, and re-raised.
#         """
#
#         try:
#
#             data['user_id'] = data.pop('id')
#             if not existing_data:
#                 result = await db.execute(
#                     select(UserProfile).where(UserProfile.user_id == user_id, UserProfile.is_deleted == False)
#                 )
#                 existing_data = result.scalars().first()
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="update_customer_profile").exception_handling()
#             raise
#         return None
#
#     async def update_billing_data(self, data: dict, user_id: str, db: AsyncSession, existing_data=None):
#         """ Updates an existing billing address record in the database.
#
#         This asynchronous method updates a customer's billing address data. If `existing_data` is not provided, it retrieves the billing address associated with the given `user_id`, ensuring that it has not been marked as deleted. The function then updates the record using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing billing address details to be updated.
#             user_id (str): The unique identifier of the customer whose billing address is being updated.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#             existing_data (optional): The existing billing address record to be updated. If `None`, the function retrieves the record from the database.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the billing data update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             if not existing_data:
#                 result = await db.execute(
#                     select(BillingAddress).where(BillingAddress.user_id == user_id, BillingAddress.is_deleted == False)
#                 )
#                 existing_data = result.scalars().first()
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="update_billing_data").exception_handling()
#             raise
#         return None
#
#     async def update_payment_data(self, data: dict, user_id: str, db: AsyncSession, existing_data=None):
#         """ Updates an existing payment method record in the database.
#
#         This asynchronous method updates a customer's payment method details. If `existing_data` is not provided, it retrieves the payment method using the `id` from the input data, ensuring that it has not been marked as deleted. The function then updates the record using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing payment method details to be updated. Must include the `id` of the payment method.
#             user_id (str): The unique identifier of the customer whose payment method is being updated.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#             existing_data (optional): The existing payment method record to be updated. If `None`, the function retrieves the record from the database.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the payment method update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             if not existing_data:
#                 result = await db.execute(
#                     select(PaymentMethod).where(PaymentMethod.id == data['id'], PaymentMethod.is_deleted == False)
#                 )
#                 existing_data = result.scalars().first()
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="update_payment_data").exception_handling()
#             raise
#         return None
#
#     async def update_card_data(self, data: dict, user_id: str, db: AsyncSession):
#         """ Updates an existing card record in the database or creates a new one if none exists.
#
#         This asynchronous method updates a customer's card details. It first attempts to retrieve an existing card record based on `user_id` and `payment_source_id`, ensuring that it has not been marked as deleted. If no matching card is found, it calls `create_card_data` to create a new card record instead. Otherwise, the function updates the existing card record, ensuring the `brand` field is correctly mapped from `card_type` if available. The update is performed using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing card details to be updated. Must include `payment_source_id` to identify the card.
#             user_id (str): The unique identifier of the customer whose card data is being updated.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the card update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             result = await db.execute(
#                 select(Card).where(
#                     Card.user_id == user_id,
#                     Card.payment_source_id == data.get('payment_source_id'),
#                     Card.is_deleted == False
#                 )
#             )
#             existing_data = result.scalars().first()
#             if not existing_data:
#                 await self.create_card_data(data=data, user_id=user_id, db=db)
#                 return None
#
#             data['user_id'] = user_id
#             data['brand'] = data.get('card_type', data.get('brand'))
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="update_card_data").exception_handling()
#             raise
#         return None
#
#
# class ItemOperations:
#     async def item_family_create(self, data: dict, db: AsyncSession):
#         """ Creates a new item family record in the database.
#
#         This asynchronous method processes the creation of an item family record by filtering the input dictionary to retain only valid fields present in the `ItemFamily` model. A new item family record is then created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing item family details. Only keys matching the `ItemFamily` model fields are retained.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the item family creation process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             fields = {column.name for column in ItemFamily.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = ItemFamily(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="item_family_create").exception_handling()
#             raise
#         return None
#
#     async def item_create(self, data: dict, db: AsyncSession):
#         """ Creates a new item record in the database.
#
#         This asynchronous method processes the creation of an item record by filtering the input dictionary to retain only valid fields present in the `Item` model. A new item record is then created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing item details. Only keys matching the `Item` model fields are retained.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the item creation process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             fields = {column.name for column in Item.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = Item(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="item_create").exception_handling()
#             raise
#         return None
#
#     async def item_price_create(self, data: dict, db: AsyncSession):
#         """ Creates a new item price record in the database.
#
#         This asynchronous method processes the creation of an item price record by filtering the input dictionary to retain only valid fields present in the `ItemPrice` model. A new item price record is then created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing item price details. Only keys matching the `ItemPrice` model fields are retained.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the item price creation process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             fields = {column.name for column in ItemPrice.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = ItemPrice(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="item_price_create").exception_handling()
#             raise
#         return None
#
#     async def item_family_update(self, data: dict, db: AsyncSession):
#         """ Updates an existing item family record in the database.
#
#         This asynchronous method updates an item family record by first retrieving the existing record based on the provided `id`, ensuring that it has not been marked as deleted. If the `status` in the input data is set to `"deleted"`, the function updates the `is_active` and `is_deleted` fields accordingly. The record is then updated using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing item family details to be updated. Must include the `id` of the item family.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the item family update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             result = await db.execute(
#                 select(ItemFamily).where(ItemFamily.id == data.get('id'), ItemFamily.is_deleted == False)
#             )
#             existing_data = result.scalars().first()
#             if data.get('status') == "deleted":
#                 data.update({'is_active': False, 'is_deleted': True})
#
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="item_family_update").exception_handling()
#             raise
#         return None
#
#     async def item_update(self, data: dict, db: AsyncSession):
#         """ Updates an existing item record in the database.
#
#         This asynchronous method updates an item record by first retrieving the existing record based on the provided `id`, ensuring that it has not been marked as deleted. If the `status` in the input data is set to `"deleted"`, the function updates the `is_active` and `is_deleted` fields accordingly. The record is then updated using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing item details to be updated. Must include the `id` of the item.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the item update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             result = await db.execute(
#                 select(Item).where(Item.id == data.get('id'), Item.is_deleted == False)
#             )
#             existing_data = result.scalars().first()
#             if data.get('status') == "deleted":
#                 data.update({'is_active': False, 'is_deleted': True})
#
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="item_update").exception_handling()
#             raise
#         return None
#
#     async def item_price_update(self, data: dict, db: AsyncSession):
#         """ Updates an existing item price record in the database.
#
#         This asynchronous method updates an item price record by first retrieving the existing record based on the provided `id`, ensuring that it has not been marked as deleted. If the `status` in the input data is set to `"deleted"`, the function updates the `is_active` and `is_deleted` fields accordingly. The record is then updated using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing item price details to be updated. Must include the `id` of the item price.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the item price update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             result = await db.execute(
#                 select(ItemPrice).where(ItemPrice.id == data.get('id'), ItemPrice.is_deleted == False)
#             )
#             existing_data = result.scalars().first()
#             if data.get('status') == "deleted":
#                 data.update({'is_active': False, 'is_deleted': True})
#
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="item_price_update").exception_handling()
#             raise
#         return None
#
#
# class InvoiceOperations:
#     async def invoice_create(self, data: dict, db: AsyncSession):
#         """ Creates a new invoice record in the database.
#
#         This asynchronous method processes the creation of an invoice record by filtering the input dictionary to retain only valid fields present in the `Invoice` model. A new invoice record is then created and added to the database session. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             data (dict): A dictionary containing invoice details. Only keys matching the `Invoice` model fields are retained.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the invoice creation process, the exception is logged, handled, and re-raised.
#         """
#         try:
#
#             fields = {column.name for column in Invoice.__table__.columns}
#             filtered_data = {key: value for key, value in data.items() if key in fields}
#             db_obj = Invoice(**filtered_data)
#             db.add(db_obj)
#             await db.commit()
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="invoice_create").exception_handling()
#             raise
#         return None
#
#     async def invoice_update(self, existing_data, data: dict, db: AsyncSession, invoice_id: str = ''):
#         """ Updates an existing invoice record in the database.
#
#         This asynchronous method updates an invoice record with new data. If `existing_data` is not provided, it retrieves the invoice using the provided `invoice_id`, ensuring that it has not been marked as deleted. The record is then updated using the `Dbcrud().update` method. If an exception occurs, it logs detailed information about the input and error, handles the exception using a dedicated exception handler, and then re-raises the exception.
#
#         **Args**:
#             existing_data: The existing invoice record to be updated. If `None`, the function retrieves the record using `invoice_id`.
#             data (dict): A dictionary containing invoice details to be updated.
#             db (AsyncSession): The asynchronous database session used to perform the database operations.
#             invoice_id (str, optional): The unique identifier of the invoice to be updated, required if `existing_data` is not provided.
#
#         **Returns**:
#             None: This method does not return a value upon successful execution.
#
#         **Raises**:
#             Exception: If an error occurs during the invoice update process, the exception is logged, handled, and re-raised.
#         """
#         try:
#             if not existing_data:
#                 result = await db.execute(
#                     select(Invoice).where(Invoice.id == invoice_id, Invoice.is_deleted == False)
#                 )
#                 existing_data = result.scalars().first()
#
#             await Dbcrud().update(existing_data=existing_data, update_data=data, db=db)
#
#         except Exception as e:
#             logger.exception(f"Input: {str(locals())}\nException: {str(e)}", exc_info=True)
#             ExceptionHandling(e=str(e), function_name="invoice_create").exception_handling()
#             raise
#         return None
