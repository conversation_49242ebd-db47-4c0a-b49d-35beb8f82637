import dynamic from "next/dynamic";
const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  {
    ssr: false, // Disable server-side rendering
  }
);

import AnimatedText from "@/components/common/AnimatedText";
import { services6 } from "@/data/services";

import Pricing from "@/components/homes/home-1/Pricing";
export const metadata = {
  title: "Services",
  description: "CFRVision Services Page",
};
export default function MainServicesPage3({ dark = false }) {
  return (
    <>
      <div className="theme-main">
        <div className="page" id="top">
          <nav className="main-nav dark light-after-scroll transparent stick-fixed wow-menubar"></nav>
          <main id="main">
            <ParallaxContainer
              className="page-section"
              // style={{
              //   backgroundImage:
              //     "url(/assets/images/full-width-images/section-bg-11.jpg)",
              // }}
            >
              <div
                className="container position-relative pb-sm-20"
                id="features"
              >
                {/* Section Content */}
                <div className="text-center">
                  <div className="row">
                    {/* Page Title */}
                    <div className="col-md-8 offset-md-2">
                      <h2 className="section-caption-slick wow fadeInUp mb-30 mb-sm-20">
                        Feature
                      </h2>
                      <h1 className="hs-title-1 mb-30">
                        <span
                          className="wow charsAnimIn"
                          data-splitting="chars"
                        >
                          <AnimatedText text="Smart Features Designed for the Public Sector" />
                        </span>
                      </h1>
                      <div className="row mb-5">
                        <div className="col-lg-10 offset-lg-1">
                          <p
                            className="section-descr mb-0 wow fadeInUp"
                            data-wow-delay="0.6s"
                            data-wow-duration="1.2s"
                          >
                            Revolutionizing ACFR Preparation with AI-Driven
                            Insights
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* End Page Title */}
                  </div>
                </div>
                {/* End Section Content */}
              </div>
            </ParallaxContainer>
            <section className="page-section pt-0" id="services">
              <div className="container position-relative mt-n120 mt-sm-n60">
                <div className="row mb-n30">
                  {services6.map((elm, i) => (
                    <div
                      key={i}
                      className="col-md-6 col-lg-4 d-flex align-items-stretch mb-30"
                    >
                      <div className="services-3-item round text-center">
                        <div className="wow fadeInUpShort" data-wow-offset={50}>
                          <div className="services-3-icon">
                            {/* <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width={elm.width}
                              height={elm.height}
                              viewBox={`0 0 ${elm.width} ${elm.height}`}
                              aria-hidden="true"
                            >
                              <path d={elm.path} />
                            </svg> */}
                            <img
                              src={elm.path}
                              alt="Not Found"
                              height={elm.height}
                              width={elm.width}
                              aria-hidden="true"
                            />
                          </div>
                          <h3 className="services-3-title fw-bold">
                            {elm.title}
                          </h3>
                          <div className="services-3-text">{elm.text}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>
            <section
              className={`page-section  scrollSpysection  ${
                dark ? "bg-dark-1 light-content" : ""
              } `}
              id="subscription"
            >
              <div className="container">
                <div className="row">
                  <div className="col-md-10 offset-md-1 col-lg-8 offset-lg-2 text-center">
                    <h2 className="section-caption-slick wow fadeInUp mb-30 mb-sm-20">
                      Subscription
                    </h2>
                    <h3 className="section-title mb-50 mb-sm-30">
                      <span className="wow charsAnimIn" data-splitting="chars">
                        Choose the Perfect Plan for Your Needs
                      </span>
                    </h3>
                  </div>
                </div>
                <div className="row wow fadeInUp">
                  <div className="col-xl-10 offset-xl-1">
                    <Pricing />
                  </div>
                </div>
              </div>
            </section>
          </main>
        </div>{" "}
      </div>
    </>
  );
}
