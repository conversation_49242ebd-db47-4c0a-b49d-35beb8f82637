# Build stage
FROM node:19-alpine AS builder
WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install
COPY . .  
COPY .env.master .env 
RUN yarn build

# Run stage
FROM node:19-alpine AS runner
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next  
COPY --from=builder /app/public ./public  
COPY package.json yarn.lock ./

EXPOSE 3000
CMD ["yarn", "run", "start"]
