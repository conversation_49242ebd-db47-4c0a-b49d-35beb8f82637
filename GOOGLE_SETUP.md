# 🤖 Google LLM Integration Setup

This guide shows you how to set up Google's AI services for enhanced document understanding, OCR, and task extraction.

## 🎯 What Google Integration Adds

### **Google Gemini LLM**
- **Smart document classification** - Understands context, not just keywords
- **Intelligent task extraction** - Finds implicit tasks and business opportunities  
- **Business insights** - Explains why documents matter and suggests next steps
- **Priority assessment** - Automatically determines urgency and importance

### **Google Cloud Vision OCR**
- **Superior OCR accuracy** - Better than Tesseract for business documents
- **Receipt/invoice understanding** - Specialized for financial documents
- **Handwriting recognition** - Can read handwritten notes
- **Multi-language support** - Handles various languages automatically

## 🚀 Setup Instructions

### 1. Install Google Dependencies

```bash
pip install google-generativeai google-cloud-vision google-auth
```

### 2. Set Up Google Gemini API

1. **Get API Key:**
   - Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key

2. **Set Environment Variable:**
   ```bash
   export GOOGLE_API_KEY="your-gemini-api-key-here"
   ```

### 3. Set Up Google Cloud Vision (Optional but Recommended)

1. **Create Google Cloud Project:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable Vision API:**
   - Go to APIs & Services > Library
   - Search for "Cloud Vision API"
   - Click Enable

3. **Create Service Account:**
   - Go to IAM & Admin > Service Accounts
   - Click "Create Service Account"
   - Name it "startup-ops-vision"
   - Grant role: "Cloud Vision API User"

4. **Download Credentials:**
   - Click on your service account
   - Go to Keys tab
   - Click "Add Key" > "Create new key"
   - Choose JSON format
   - Save the file as `google-credentials.json`

5. **Set Credentials Path:**
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/google-credentials.json"
   ```

## 🔧 Usage Examples

### Basic Google Gemini Integration

```bash
# Use Google Gemini for intelligent classification
python ops_indexer.py --root . --enable-llm --llm-provider google
```

### Full Google Integration (Gemini + Vision OCR)

```bash
# Use both Gemini LLM and Vision OCR
python ops_indexer.py --root . \
  --enable-llm --llm-provider google \
  --enable-google-vision \
  --google-credentials /path/to/google-credentials.json
```

### With Custom API Key

```bash
# Specify API key directly
python ops_indexer.py --root . \
  --enable-llm --llm-provider google \
  --llm-api-key "your-api-key" \
  --enable-google-vision
```

### Enhanced OCR for Images and PDFs

```bash
# Enable OCR with Google Vision for superior accuracy
python ops_indexer.py --root . \
  --enable-ocr \
  --enable-google-vision \
  --google-credentials /path/to/google-credentials.json
```

## 🧠 What Google Integration Does

### **Smart Document Understanding**

Instead of just looking for keywords like "receipt" or "invoice", Google Gemini:

- **Understands context**: "This appears to be a software development invoice from OpenXcell for $5,000 covering backend API development work"
- **Identifies business impact**: "This deliverable requires testing and feedback within 5 days to maintain project timeline"
- **Suggests actions**: "Schedule code review, test API endpoints, prepare feedback document"

### **Enhanced OCR for Business Documents**

Google Vision OCR excels at:

- **Financial documents**: Receipts, invoices, bank statements
- **Contracts and legal docs**: Even with complex formatting
- **Screenshots**: UI mockups, error messages, system dashboards
- **Handwritten notes**: Meeting notes, sketches, annotations

### **Intelligent Task Generation**

The system will automatically create tasks like:

```
📋 Review OpenXcell API Deliverable
   Priority: High | Due: 2025-08-14
   Why: $5,000 development milestone requires testing
   Next: Test authentication endpoints, validate data models
   
💡 Update AWS Security Configuration  
   Priority: Medium | Due: 2025-08-16
   Why: Access keys found in documentation need rotation
   Next: Generate new keys, update applications, revoke old keys

📊 Prepare Q3 Grant Application
   Priority: High | Due: 2025-08-20
   Why: NSF SBIR deadline approaching, $50K opportunity
   Next: Complete technical narrative, gather financial docs
```

## 💰 Cost Considerations

### **Google Gemini API**
- **Free tier**: 15 requests per minute
- **Paid**: $0.00025 per 1K characters (very affordable)
- **Your usage**: ~$1-5/month for typical startup document processing

### **Google Cloud Vision**
- **Free tier**: 1,000 requests per month
- **Paid**: $1.50 per 1,000 requests
- **Your usage**: ~$5-15/month depending on document volume

## 🔒 Security & Privacy

- **API keys**: Store securely, never commit to version control
- **Document content**: Sent to Google for processing (review their privacy policy)
- **Local processing**: All results stored locally in your `.ops_out` folder
- **No data retention**: Google doesn't store your document content

## 🛠️ Troubleshooting

### "Google Gemini not available"
```bash
pip install google-generativeai
export GOOGLE_API_KEY="your-key"
```

### "Google Cloud Vision not available"
```bash
pip install google-cloud-vision
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/credentials.json"
```

### "Authentication failed"
- Check your API key is correct
- Verify credentials file path
- Ensure Vision API is enabled in your Google Cloud project

### "Quota exceeded"
- Check your API usage in Google Cloud Console
- Consider upgrading to paid tier for higher limits

## 🚀 Advanced Configuration

### Custom Model Selection

```python
# In your config.yml, add:
google_settings:
  gemini_model: "gemini-1.5-pro"  # For more complex analysis
  vision_features: ["TEXT_DETECTION", "DOCUMENT_TEXT_DETECTION"]
  confidence_threshold: 0.8
```

### Batch Processing

For large document sets, the system automatically:
- Batches requests to stay within rate limits
- Caches results to avoid reprocessing
- Falls back to rule-based classification if API fails

---

**🎉 With Google integration, your startup ops system becomes incredibly intelligent at understanding your business documents and generating actionable insights!**
