"use client";
import TableWithPagination from "@/components/common/TableWithPagination/page";
import { Icon } from "@iconify/react";
import React, { useEffect, useState } from "react";
import AddNewAICheck from "./AddNewCheckModal";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import DeleteAiCheckModal from "../common/DeleteModal";
import Toaster from "../common/Toaster";
import { ROLE } from "@/utlis/constant";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const AICheck = () => {
  const [show, setShow] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [checks, setChecks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [searchText, setSearchText] = useState("");
  const [checkData, setCheckData] = useState(null);
  const [isView, setIsView] = useState(false);
  const [role, setRole] = useState(null);

  const { contextHolder, showToast } = Toaster();

  const columns = [
    {
      headerName: "CHECK NUMBER",
      valueGetter: (p) => p.data.check_display_number,
      flex: 2,
    },
    {
      headerName: "CATEGORY",
      valueGetter: (p) => p.data.category_name,
      flex: 2,
    },
    {
      headerName: "CHECK NAME",
      valueGetter: (p) => p.data.check_name,
      flex: 2,
    },
    {
      headerName: "DESCRIPTION",
      valueGetter: (p) => p.data.description,
      flex: 2,
    },

    {
      headerName: "STATUS",
      valueGetter: (p) => (p.data.is_active ? "Active" : "Inactive"),
      flex: 2,
      cellRenderer: (params) => {
        const status = params.value;
        return (
          <span
            className={`badge badge-${
              status === "Active" ? "active" : "inactive"
            }`}
          >
            {status}
          </span>
        );
      },
    },
    {
      headerName: "ACTIONS",
      field: "actions",
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="mdi-eye"
            width="24"
            height="24"
            cursor={"pointer"}
            onClick={() => {
              setCheckData(params.data);
              setShow(true);
              setIsView(true);
            }}
          />
          {role === ROLE.ADMIN && (
            <>
              {" "}
              <Icon
                icon="basil:edit-outline"
                width="24"
                height="24"
                cursor={"pointer"}
                onClick={() => {
                  setCheckData(params.data);
                  setShow(true);
                  setIsView(false);
                }}
              />
              <Icon
                icon="mdi:trash-outline"
                width="24"
                height="24"
                color="red"
                cursor={"pointer"}
                onClick={() => {
                  setShowDeleteModal(true);
                  setDeleteId(params.data.check_id);
                }}
              />
            </>
          )}
        </div>
      ),
    },
  ];

  const fetchChecks = async (
    page_no = 1,
    page_size = 10,
    category = "",
    search = searchText || ""
  ) => {
    setLoading(true);
    try {
      const requestBody = {
        // id: 1,
        page_no,
        page_size,
        is_pagination: true,
        search_text: search,
        filters: category
          ? [{ filter_column: "category_id", filter_text: category }]
          : [],
      };
      const url =
        localStorage?.getItem("role") === ROLE.USER
          ? "/v1/llm_check_request/get-ai-checks"
          : "/v1/cms/ai_checks/ai-checks";
      const response = await axiosInstance.post(url, requestBody);
      setChecks(response.data.data.checks);
      setCategories(response.data.data.categories);
    } catch (error) {
      console.error("Failed to fetch AI checks:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const roleValue = localStorage?.getItem("role");
    setRole(roleValue);
    fetchChecks();
  }, []);

  const handleCategoryChange = (e) => {
    const category = e.target.value;
    setSelectedCategory(category);
    fetchChecks(1, 10, category, searchText);
  };

  const handlePaginationChange = (page_no, page_size) => {
    fetchChecks(page_no, page_size, selectedCategory, searchText);
  };

  const handleSearchChange = (value) => {
    setSearchText(value);
    fetchChecks(1, 10, selectedCategory, value);
  };

  const onCheckSubmit = (successMsg) => {
    setCheckData({});
    showToast({
      type: "success",
      message: successMsg,
    });
  };

  const onAiCheckDeleted = (deleteMsg) => {
    showToast({
      type: "success",
      message: deleteMsg,
    });
  };

  const onAiCheckDeletedError = (deleteMsg) => {
    showToast({
      type: "error",
      message: deleteMsg,
    });
  };

  return (
    <>
      {contextHolder}
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="d-flex align-items-center flex-wrap justify-content-between w-100">
              <h5 className="page-title text-start mb-0 flex-grow-1">
                AI Checks
              </h5>
              <label className="me-2">Category :</label>
              <select
                className="form-select me-2"
                style={{ width: "auto", minWidth: "200px" }}
                onChange={handleCategoryChange}
                value={selectedCategory}
              >
                <option value="">All</option>
                {categories.map((c) => (
                  <option value={c.category_id} key={c.category_id}>
                    {c.category_name}
                  </option>
                ))}
              </select>

              {role === ROLE.ADMIN && (
                <button
                  className="btn btn-mod btn-color btn-small btn-circle btn-hover-anim mb-xs-10"
                  id="upload-button"
                  onClick={() => {
                    setShow(true);
                    setIsView(false);
                  }}
                >
                  <span>Add New Check</span>
                </button>
              )}
            </div>

            <TableWithPagination
              data={checks}
              columns={columns}
              onPaginationChange={handlePaginationChange}
              loading={loading}
              onSearchChange={handleSearchChange}
            />
          </ParallaxContainer>
        </section>
      </main>
      {show && (
        <AddNewAICheck
          checkData={checkData}
          show={show}
          setShow={setShow}
          categories={categories}
          fetchChecks={fetchChecks}
          onCheckSubmit={onCheckSubmit}
          setCheckData={setCheckData}
          isView={isView}
        />
      )}
      {showDeleteModal && (
        <DeleteAiCheckModal
          show={showDeleteModal}
          setShow={setShowDeleteModal}
          refetch={fetchChecks}
          id={deleteId}
          apiUrl="/v1/cms/ai_checks/delete-ai-check"
          name="AI Check"
          onDeleted={onAiCheckDeleted}
          onDeletedError={onAiCheckDeletedError}
        />
      )}
    </>
  );
};

export default AICheck;
