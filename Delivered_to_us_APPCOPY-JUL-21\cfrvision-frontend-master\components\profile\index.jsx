"use client";
import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import { Controller, useForm } from "react-hook-form";
import { valibotResolver } from "@hookform/resolvers/valibot";
import Invoice from "./Invoice";
import Security from "./Security";
import Billing from "./Biling";
import { Col, Row, Button, Card } from "react-bootstrap";
import * as v from "valibot";
import Toaster from "../common/Toaster";
import { Icon } from "@iconify/react";
import { Upload } from "antd";
import Loading from "../common/Loading";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const schema = v.object({
  email: v.pipe(
    v.string(),
    v.nonEmpty("Email is required"),
    v.email("Email is invalid")
  ),
  organization: v.string(),
  organization_contact: v.string(),
  organization_address_city: v.pipe(v.string(), v.nonEmpty("City is required")),
  organization_address_state: v.pipe(
    v.string(),
    v.nonEmpty("State is required")
  ),
  organization_address_street1: v.pipe(
    v.string(),
    v.nonEmpty("Street is required")
  ),
  organization_address_street2: v.string(),
  organization_address_zip_code: v.pipe(
    v.string(),
    v.nonEmpty("Zip Code is required")
  ),
});

const Profile = () => {
  const [isFetched, setIsFetched] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileImage, setProfileImage] = useState();
  const [preview, setPreview] = useState();
  const [userDetails, setUserDetails] = useState(null);
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      email: userDetails?.email || "",
      organization: userDetails?.organization || "",
      organization_contact: userDetails?.organization_contact || "",
      organization_address_city:
        userDetails?.organization_address?.organization_address_city || "",
      organization_address_state:
        userDetails?.organization_address?.organization_address_state || "",
      organization_address_street1:
        userDetails?.organization_address?.organization_address_street1 || "",
      organization_address_street2:
        userDetails?.organization_address?.organization_address_street2 || "",
      organization_address_zip_code:
        userDetails?.organization_address?.organization_address_zip_code || "",
    },
  });
  const { contextHolder, showToast } = Toaster();

  const fetchProfileData = async () => {
    try {
      setIsFetched(true);
      const response = await axiosInstance.post("/v1/profile/basic/get");
      const data = response?.data?.data;
      setUserDetails(data);
      // setValue("first_name", data?.first_name);
      // setValue("last_name", data?.last_name);
      setValue("email", data?.email);
      setValue("organization", data?.organization);
      setValue("organization_contact", data?.organization_contact);
      setValue(
        "organization_address_city",
        data?.organization_address?.organization_address_city || ""
      );
      setValue(
        "organization_address_state",
        data?.organization_address?.organization_address_state || ""
      );
      setValue(
        "organization_address_street1",
        data?.organization_address?.organization_address_street1 || ""
      );
      setValue(
        "organization_address_street2",
        data?.organization_address?.organization_address_street2 || ""
      );
      setValue(
        "organization_address_zip_code",
        data?.organization_address?.organization_address_zip_code || ""
      );
      setPreview(data?.profile_picture);
      setIsFetched(false);
    } catch (error) {
      console.error("Failed to fetch AI checks:", error);
    } finally {
      setLoading(false);
      setIsFetched(false);
    }
  };

  useEffect(() => {
    fetchProfileData();
  }, []);

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      const formData = new FormData();
      profileImage && formData.append("profile_img", profileImage);
      formData.append("first_name", userDetails?.first_name);
      formData.append("last_name", userDetails?.last_name);
      formData.append("organization", data?.organization);
      formData.append(
        "organization_address_street1",
        data?.organization_address_street1
      );
      formData.append(
        "organization_address_street2",
        data?.organization_address_street2
      );
      formData.append(
        "organization_address_city",
        data?.organization_address_city
      );
      formData.append(
        "organization_address_state",
        data?.organization_address_state
      );
      formData.append(
        "organization_address_zip_code",
        data?.organization_address_zip_code
      );
      formData.append("organization_contact", data?.organization_contact);

      const response = await axiosInstance.post(
        "/v1/profile/basic/update",
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      showToast({
        type: "success",
        message: response?.data.message,
      });
      setLoading(false);
    } catch (error) {
      console.log("Error saving category:", error);
      showToast({
        type: "error",
        message: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (file) => {
    const isImage = file?.type?.startsWith("image/");
    if (isImage) {
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target.result);
      reader.readAsDataURL(file);
      setProfileImage(file);
    } else {
      showToast({
        type: "error",
        message: "Only image files are allowed!",
      });
    }
    return false;
  };

  return (
    <>
      {contextHolder}
      {isFetched && <Loading />}
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="container relative">
              <h4 className="page-title mb-2">Profile Details</h4>
              <form
                noValidate
                autoComplete="off"
                onSubmit={handleSubmit(onSubmit)}
                className="form contact-form profile-form"
              >
                <Row>
                  <Col md={3}>
                    <div
                      style={{
                        width: "150px",
                        height: "150px",
                        position: "relative",
                        marginBottom: "10px",
                      }}
                    >
                      {preview ? (
                        <img
                          src={preview}
                          alt="Uploaded Preview"
                          style={{
                            width: "100%",
                            height: "100%",
                            // objectFit: "cover",
                            borderRadius: "50%",
                          }}
                        />
                      ) : (
                        <Icon
                          icon="mdi:account"
                          style={{ width: "100%", height: "100%" }}
                        />
                      )}
                      <Upload
                        beforeUpload={handleFileUpload}
                        showUploadList={false}
                        accept="image/*"
                      >
                        <Icon
                          icon="mdi:plus"
                          style={{
                            position: "absolute",
                            top: "0",
                            right: "10%",
                            border: "1px solid black",
                            borderRadius: "50%",
                            background: "#fff",
                          }}
                          width={30}
                          height={30}
                        />
                      </Upload>
                    </div>

                    <h3 className="mb-2" style={{ fontSize: "20px" }}>
                      {userDetails?.first_name + " " + userDetails?.last_name}
                    </h3>
                    <p style={{ fontSize: "16px" }}>{userDetails?.email}</p>
                    <Button
                      variant="success"
                      onClick={() => {
                        window.location.href =
                          "https://madiasolutions.chargebeeportal.com/portal/v2/login";
                      }}
                    >
                      Manage Subscription
                    </Button>
                  </Col>
                  <Col md={9}>
                    <Row>
                      <Col md={6} className="mb-10">
                        <div className="form-group">
                          <label htmlFor="organization">Organization</label>
                          <Controller
                            name="organization"
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <input
                                {...field}
                                type="text"
                                name="organization"
                                id="organization"
                                className="input-md round form-control"
                                placeholder="Enter organization name"
                                required
                              />
                            )}
                          />
                          {errors?.organization && (
                            <span className="text-red">
                              {errors?.organization.message}
                            </span>
                          )}
                        </div>
                      </Col>
                      <Col md={6} className="mb-10">
                        <div className="form-group">
                          <label htmlFor="organization_contact">
                            Organization Contact
                          </label>
                          <Controller
                            name="organization_contact"
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <input
                                {...field}
                                type="text"
                                name="organization_contact"
                                id="organization_contact"
                                className="input-md round form-control"
                                placeholder="Enter organization contact name"
                                required
                              />
                            )}
                          />
                          {errors?.organization_contact && (
                            <span className="text-red">
                              {errors?.organization_contact.message}
                            </span>
                          )}
                        </div>
                      </Col>
                      {/* <Col md={12}>
                        <div className="form-group mb-20">
                          <label htmlFor="email">Email</label>
                          <Controller
                            name="email"
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <input
                                {...field}
                                type="text"
                                name="email"
                                id="email"
                                className="input-md round form-control"
                                placeholder="Enter email"
                                required
                                disabled
                              />
                            )}
                          />
                          {errors?.email && (
                            <span className="text-red">
                              {errors?.email.message}
                            </span>
                          )}
                        </div>
                      </Col> */}
                      <div
                        style={{
                          padding: "10px 10px 0",
                          margin: "0 10px 20px",
                        }}
                      >
                        <h4>Organization Address</h4>
                        <Row>
                          <Col md={6}>
                            <div className="form-group mb-20">
                              <label htmlFor="organization_address_street1">
                                Street1
                              </label>
                              <Controller
                                name="organization_address_street1"
                                control={control}
                                rules={{ required: true }}
                                render={({ field }) => (
                                  <input
                                    {...field}
                                    type="text"
                                    name="organization_address_street1"
                                    id="organization_address_street1"
                                    className="input-md round form-control"
                                    placeholder="Enter organization street1"
                                    required
                                  />
                                )}
                              />
                              {errors?.organization_address_street1 && (
                                <span className="text-red">
                                  {errors?.organization_address_street1.message}
                                </span>
                              )}
                            </div>
                          </Col>
                          <Col md={6}>
                            <div className="form-group mb-20">
                              <label htmlFor="organization_address_street2">
                                Street2
                              </label>
                              <Controller
                                name="organization_address_street2"
                                control={control}
                                rules={{ required: true }}
                                render={({ field }) => (
                                  <input
                                    {...field}
                                    type="text"
                                    name="organization_address_street2"
                                    id="organization_address_street2"
                                    className="input-md round form-control"
                                    placeholder="Enter organization street2"
                                    required
                                  />
                                )}
                              />
                              {errors?.organization_address_street2 && (
                                <span className="text-red">
                                  {errors?.organization_address_street2.message}
                                </span>
                              )}
                            </div>
                          </Col>
                          <Col md={6}>
                            <div className="form-group mb-20">
                              <label htmlFor="organization_address_city">
                                City
                              </label>
                              <Controller
                                name="organization_address_city"
                                control={control}
                                rules={{ required: true }}
                                render={({ field }) => (
                                  <input
                                    {...field}
                                    type="text"
                                    name="organization_address_city"
                                    id="organization_address_city"
                                    className="input-md round form-control"
                                    placeholder="Enter organization address"
                                    required
                                  />
                                )}
                              />
                              {errors?.organization_address_city && (
                                <span className="text-red">
                                  {errors?.organization_address_city.message}
                                </span>
                              )}
                            </div>
                          </Col>
                          <Col md={6}>
                            <div className="form-group mb-20">
                              <label htmlFor="organization_address_state">
                                State
                              </label>
                              <Controller
                                name="organization_address_state"
                                control={control}
                                rules={{ required: true }}
                                render={({ field }) => (
                                  <input
                                    {...field}
                                    type="text"
                                    name="organization_address_state"
                                    id="organization_address_state"
                                    className="input-md round form-control"
                                    placeholder="Enter organization state"
                                    required
                                  />
                                )}
                              />
                              {errors?.organization_address_state && (
                                <span className="text-red">
                                  {errors?.organization_address_state.message}
                                </span>
                              )}
                            </div>
                          </Col>
                          <Col md={12}>
                            <div className="form-group mb-20">
                              <label htmlFor="organization_address_zip_code">
                                Zip Code
                              </label>
                              <Controller
                                name="organization_address_zip_code"
                                control={control}
                                rules={{ required: true }}
                                render={({ field }) => (
                                  <input
                                    {...field}
                                    type="text"
                                    name="organization_address_zip_code"
                                    id="organization_address_zip_code"
                                    className="input-md round form-control"
                                    placeholder="Enter organization zip code"
                                    required
                                  />
                                )}
                              />
                              {errors?.organization_address_zip_code && (
                                <span className="text-red">
                                  {
                                    errors?.organization_address_zip_code
                                      .message
                                  }
                                </span>
                              )}
                            </div>
                          </Col>
                        </Row>
                      </div>

                      <Col md={12}>
                        <div className="d-flex gap-2">
                          <Button
                            type="submit"
                            variant="primary"
                            disabled={loading}
                          >
                            Update
                          </Button>
                        </div>
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </form>
            </div>
          </ParallaxContainer>
        </section>
      </main>
    </>
  );
};

export default Profile;
