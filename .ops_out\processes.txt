# Recurring Processes
# Format: <process_name> | <source_hint> | <cadence> | <next_due> | <evidence_paths_csv>

Enter receipts in QuickBooks | expenses/* | DAILY | 2025-08-10 | classifier.py,config.yml,run_example.py,Chargebee\Account_1.jpg,Chargebee\Account_2.jpg
Test deliverable and send feedback | deliverables/* | manual | 2025-08-14 | Delivered_to_us_APPCOPY-JUL-21\cfrvision-app-master\.env,Delivered_to_us_APPCOPY-JUL-21\cfrvision-app-master\Dockerfile,Delivered_to_us_APPCOPY-JUL-21\cfrvision-app-master\logo-img.png,Delivered_to_us_APPCOPY-JUL-21\cfrvision-app-master\main.py,Delivered_to_us_APPCOPY-JUL-21\cfrvision-app-master\README.md
Review development progress and invoices | openxcell/* | WEEKLY | 2025-08-16 | Openxcell\2024-12-07 10-58_page_1.pdf,Openxcell\2024-12-07 10-58_page_2 (1).pdf,Openxcell\AWS_Users.jpg,Openxcell\Madia Web App - Design Questionnaire.docx,Openxcell\OpenXcell - AWS User 1.pdf
Grant follow-up | grants/* | BIWEEKLY | 2025-08-23 | Possible_Grants_For _Us\Eligible Grant Opportunities.docx,Possible_Grants_For _Us\Grants_Starter_List.docx
Review/update pricing deck | pricing/* | MONTHLY | 2025-09-08 | Pricing_tiers_to_Customers\Pricing_tiers.xlsx,Pricing_tiers_to_Customers\Tiers.jpg
Check compliance actions | compliance/* | MONTHLY | 2025-09-08 | Required_Compliance and Security\Master List Specifications Long Version.docx,Required_Compliance and Security\archive\ADA Compliance.docx,Required_Compliance and Security\archive\Collection 1.docx,Required_Compliance and Security\archive\Locals-Resource-Packet-2023v1.1.pdf,Required_Compliance and Security\archive\Master List.docx
Review AWS access and security | aws/* | MONTHLY | 2025-09-08 | classifier.py,config.yml,run_example.py,setup.py,AWS\Access key.txt
