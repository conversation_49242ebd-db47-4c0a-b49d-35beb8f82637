import axiosInstance from "@/utlis/axios";
import { valibotResolver } from "@hookform/resolvers/valibot";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Modal, Row } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import Toaster from "../common/Toaster";

const schema = v.object({
  check_display_number: v.pipe(
    v.string(), // First, treat it as a string to allow empty input
    v.nonEmpty("Please enter check number"),
    v.regex(/^\d*\.?\d+$/, "Only numeric values are allowed"), // Ensure it's numeric
    v.transform((val) => (val === "" ? null : Number(val))) // Convert to number or null if empty
  ),
  check_name: v.pipe(v.string(), v.nonEmpty("Check name is required")),
  check_prompt: v.pipe(v.string(), v.nonEmpty("Check prompt is required")),
  category_id: v.pipe(v.string(), v.nonEmpty("Please select a category.")),
  description: v.pipe(
    v.string(),
    v.minLength(1, "Check description cannot be empty"),
    v.nonEmpty("Check description are required")
  ),
  check_extended_description: v.pipe(
    v.string(),
    v.minLength(1, "Check extended description cannot be empty"),
    v.nonEmpty("Check extended description are required")
  ),
  check_relevant_sections: v.pipe(
    v.string(),
    v.minLength(1, "Check relevant section cannot be empty"),
    v.nonEmpty("Check relevant section are required")
  ),
  check_expected_result_format: v.pipe(
    v.string(),
    v.minLength(1, "Check expected result format cannot be empty"),
    v.nonEmpty("Check expected result format are required")
  ),
  is_active: v.boolean(),
});

const AddNewAICheck = ({
  show,
  setShow,
  categories,
  fetchChecks,
  onCheckSubmit,
  checkData,
  setCheckData,
  isView,
}) => {
  const [uploading, setUploading] = useState(false);
  const { contextHolder, showToast } = Toaster();

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      check_display_number: "",
      check_name: "",
      check_prompt: "",
      category_id: "",
      description: "",
      check_extended_description: "",
      check_relevant_sections: "",
      check_expected_result_format: "",
      is_active: true,
    },
  });

  // Effect to set the form data when in edit mode
  useEffect(() => {
    if (checkData?.check_id && show) {
      setValue(
        "check_display_number",
        checkData.check_display_number.toString()
      );
      setValue("check_name", checkData.check_name);
      setValue("check_prompt", checkData.check_prompt);
      setValue("category_id", checkData.category_id.toString());
      setValue("description", checkData.description);
      setValue(
        "check_extended_description",
        checkData.check_extended_description
      );
      setValue("check_relevant_sections", checkData.check_relevant_sections);
      setValue(
        "check_expected_result_format",
        checkData.check_expected_result_format
      );
      setValue("is_active", checkData.is_active);
    }
  }, [checkData, show, setValue]);

  const onSubmit = async (data) => {
    try {
      setUploading(true);
      let payload = { ...data, category_id: Number(data?.category_id) };

      if (checkData?.check_id) {
        payload = { ...payload, id: checkData?.check_id };
      }
      const url = checkData?.check_id
        ? "/v1/cms/ai_checks/update-ai-check"
        : "/v1/cms/ai_checks/create-ai-check";

      const response = await axiosInstance.post(url, payload);
      onCheckSubmit(
        response.data.message || checkData?.check_id
          ? "AI Check updated successfully."
          : "AI Check added successfully."
      );
      fetchChecks();
      setUploading(false);
      setShow(false);
      return;
    } catch (error) {
      setUploading(false);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      <Modal
        show={show}
        onHide={() => {
          setShow(false);
          setCheckData({});
        }}
        centered
        size="lg"
      >
        <form
          noValidate
          autoComplete="off"
          onSubmit={handleSubmit(onSubmit)}
          className="form contact-form"
        >
          <Modal.Header closeButton>
            <Modal.Title className="text-center">
              {checkData?.check_id
                ? isView
                  ? "AI Check Detail"
                  : "Edit AI Check"
                : "Add New AI Check"}
            </Modal.Title>
          </Modal.Header>

          <Modal.Body>
            <Row>
              <Col md={6}>
                <div className="form-group">
                  <label htmlFor="username">Check Number</label>
                  <Controller
                    name="check_display_number"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        name="check_display_number"
                        id="check_display_number"
                        className="input-md round form-control"
                        placeholder="Enter check number"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.check_display_number && (
                    <span className="text-red">
                      {errors?.check_display_number.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={6}>
                <div className="form-group">
                  <label htmlFor="username">Check Name</label>
                  <Controller
                    name="check_name"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        name="check_name"
                        id="check_name"
                        className="input-md round form-control"
                        placeholder="Enter check name"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.check_name && (
                    <span className="text-red">
                      {errors?.check_name.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="username">Check Prompt</label>
                  <Controller
                    name="check_prompt"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        name="check_prompt"
                        id="check_prompt"
                        className="input-md round form-control"
                        placeholder="Enter check prompt"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.check_prompt && (
                    <span className="text-red">
                      {errors?.check_prompt.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="username">Category</label>
                  <Controller
                    name="category_id"
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="input-md round form-control"
                        placeholder="Select a category"
                        disabled={isView}
                      >
                        <option value="" disabled>
                          Select Category
                        </option>
                        {categories?.map((item) => (
                          <option value={item?.category_id}>
                            {item?.category_name}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                  {errors?.category_id && (
                    <span className="text-red">
                      {errors?.category_id.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="username">Description</label>
                  <Controller
                    name="description"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        name="description"
                        id="description"
                        className="input-md round form-control"
                        placeholder="Enter check description"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.description && (
                    <span className="text-red">
                      {errors?.description.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="username">Extended Description</label>
                  <Controller
                    name="check_extended_description"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        name="check_extended_description"
                        id="check_extended_description"
                        className="input-md round form-control"
                        placeholder="Enter check extended description"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.check_extended_description && (
                    <span className="text-red">
                      {errors?.check_extended_description.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="username">Relevant Section</label>
                  <Controller
                    name="check_relevant_sections"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        name="check_relevant_sections"
                        id="check_relevant_sections"
                        className="input-md round form-control"
                        placeholder="Enter check relevant section"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.check_relevant_sections && (
                    <span className="text-red">
                      {errors?.check_relevant_sections.message}
                    </span>
                  )}
                </div>
              </Col>
              <Col md={12}>
                <div className="form-group">
                  <label htmlFor="username">Expected Result Format</label>
                  <Controller
                    name="check_expected_result_format"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        name="check_expected_result_format"
                        id="check_expected_result_format"
                        className="input-md round form-control"
                        placeholder="Enter expected resulr format"
                        required
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                        disabled={isView}
                      />
                    )}
                  />
                  {errors?.check_expected_result_format && (
                    <span className="text-red">
                      {errors?.check_expected_result_format.message}
                    </span>
                  )}
                </div>
              </Col>
              {/* Active checkbox that allows user to select active or inactive */}
              <Col md={12}>
                <div className="form-group">
                  <Controller
                    name="is_active"
                    control={control}
                    render={({ field }) => (
                      <label>
                        <input
                          type="checkbox"
                          {...field}
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                          disabled={isView}
                        />
                        <span style={{ marginLeft: "5px" }}>Active</span>
                      </label>
                    )}
                  />
                  {errors.is_active && (
                    <div className="text-red mt-2">
                      {errors.is_active.message}
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShow(false)}>
              Cancel
            </Button>
            {!isView && (
              <Button type="submit" variant="primary" disabled={isView}>
                {uploading
                  ? "Saving..."
                  : checkData?.check_id
                  ? "Update"
                  : "Save"}
              </Button>
            )}
          </Modal.Footer>
        </form>
      </Modal>
    </>
  );
};

export default AddNewAICheck;
