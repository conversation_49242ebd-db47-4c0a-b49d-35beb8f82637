"use client";
import TableWithPagination from "@/components/common/TableWithPagination/page";
import { Icon } from "@iconify/react";
import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";
import ContactUsModal from "./ContactUsModal";

const ContactUsList = () => {
  const [contactUsList, setContactUsList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [show, setShow] = useState(false);
  const [conatctData, setConatctData] = useState(null);

  const { contextHolder, showToast } = Toaster();

  const columns = [
    {
      headerName: "NUMBER",
      valueGetter: (p) => p.data.id,
      flex: 2,
    },
    {
      headerName: "EMAIL",
      valueGetter: (p) => p.data.email,
      flex: 2,
    },
    {
      headerName: "NAME",
      valueGetter: (p) => p.data.name,
      flex: 2,
    },
    {
      headerName: "ORGANIZATION",
      valueGetter: (p) => p.data.organization,
      flex: 2,
    },

    {
      headerName: "STATUS",
      valueGetter: (p) => p.data.status,
      flex: 2,
    },
    {
      headerName: "ACTIONS",
      field: "actions",
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="basil:edit-outline"
            width="24"
            height="24"
            cursor={"pointer"}
            onClick={() => {
              setConatctData(params.data);
              setShow(true);
            }}
          />
        </div>
      ),
    },
  ];

  const fetchContactUs = async (
    page_no = 1,
    page_size = 10,
    search = searchText || ""
  ) => {
    setLoading(true);
    try {
      const requestBody = {
        filters: [],
        page_no: page_no,
        page_size: page_size,
        is_pagination: true,
        search_text: search,
      };

      const response = await axiosInstance.post(
        "/v1/cms/contact-us",
        requestBody
      );
      setContactUsList(response.data.data);
    } catch (error) {
      console.error("Failed to fetch AI checks:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContactUs();
  }, []);

  const handlePaginationChange = (page_no, page_size) => {
    fetchContactUs(page_no, page_size, searchText);
  };

  const handleSearchChange = (value) => {
    setSearchText(value);
    fetchContactUs(1, 10, value);
  };

  const handleUpdateContactStatus = async (data) => {
    console.log("data: ", data);
    try {
      const response = await axiosInstance.post(
        "/v1/cms/contact-us/update-status",
        data
      );
      showToast({
        type: "success",
        message: response.data.message,
      });
      fetchContactUs(); // Refetch categories after update
    } catch (error) {
      console.error("Failed to update contact us:", error);
    }
  };

  return (
    <>
      {contextHolder}
      <div className="mb-20">
        <div className="d-flex align-items-center flex-wrap justify-content-between w-100">
          <h5 className="text-start mb-0 flex-grow-1">Contact us</h5>
          {/* <label className="me-2">Category :</label>
              <select
                className="form-select me-2"
                style={{ width: "auto", minWidth: "200px" }}
                onChange={handleCategoryChange}
                value={selectedCategory}
              >
                <option value="">All</option>
                {categories.map((c) => (
                  <option value={c.category_id} key={c.category_id}>
                    {c.category_name}
                  </option>
                ))}
              </select>

              <button
                className="btn btn-mod btn-color btn-small btn-circle btn-hover-anim mb-xs-10"
                id="upload-button"
                onClick={() => {
                  setShow(true);
                  setIsView(false);
                }}
              >
                <span>Add New Check</span>
              </button> */}
        </div>

        <TableWithPagination
          data={contactUsList}
          columns={columns}
          onPaginationChange={handlePaginationChange}
          loading={loading}
          onSearchChange={handleSearchChange}
        />
      </div>
      {show && (
        <ContactUsModal
          show={show}
          setShow={setShow}
          contactData={conatctData}
          onSave={handleUpdateContactStatus}
        />
      )}
    </>
  );
};

export default ContactUsList;
