"""
Unit tests for file classification and rules engine.
"""
import unittest
from datetime import date

from models import FileInfo, CadenceRule, Recurrence
from classifier import FileClassifier, RulesEngine
from config_loader import get_default_config


class TestFileClassifier(unittest.TestCase):
    """Test file classification logic."""
    
    def setUp(self):
        self.config = get_default_config()
        self.classifier = FileClassifier(self.config)
    
    def test_folder_classification(self):
        """Test classification based on folder names."""
        file_info = FileInfo(
            path="receipts/uber_receipt.jpg",
            size=1000,
            created=date.today(),
            modified=date.today(),
            extension=".jpg",
            folder="receipts",
            extracted_text=""
        )
        
        classified = self.classifier.classify_file(file_info)
        self.assertIn("expenses", classified.topics)
    
    def test_filename_classification(self):
        """Test classification based on filename."""
        file_info = FileInfo(
            path="documents/invoice_123.pdf",
            size=1000,
            created=date.today(),
            modified=date.today(),
            extension=".pdf",
            folder="documents",
            extracted_text=""
        )
        
        classified = self.classifier.classify_file(file_info)
        self.assertIn("expenses", classified.topics)
    
    def test_content_classification(self):
        """Test classification based on content."""
        file_info = FileInfo(
            path="misc/document.txt",
            size=1000,
            created=date.today(),
            modified=date.today(),
            extension=".txt",
            folder="misc",
            extracted_text="This is a grant application for SBIR funding. We need to submit the proposal by next month."
        )
        
        classified = self.classifier.classify_file(file_info)
        self.assertIn("grants", classified.topics)
    
    def test_accomplished_folder_classification(self):
        """Test that accomplished folders are properly classified."""
        file_info = FileInfo(
            path="Week 1 Accomplished/task_completed.txt",
            size=1000,
            created=date.today(),
            modified=date.today(),
            extension=".txt",
            folder="Week 1 Accomplished",
            extracted_text=""
        )
        
        classified = self.classifier.classify_file(file_info)
        self.assertIn("accomplished", classified.topics)


class TestRulesEngine(unittest.TestCase):
    """Test rules engine logic."""
    
    def setUp(self):
        self.config = get_default_config()
        self.rules_engine = RulesEngine(self.config)
    
    def test_folder_pattern_matching(self):
        """Test folder pattern matching with wildcards."""
        # Test exact match
        self.assertTrue(self.rules_engine._folder_matches_pattern("receipts", "receipts"))
        
        # Test wildcard match
        self.assertTrue(self.rules_engine._folder_matches_pattern("Delivered_to_us_JAN", "Delivered_to_us_*"))
        self.assertTrue(self.rules_engine._folder_matches_pattern("Delivered_to_us_FEB", "Delivered_to_us_*"))
        
        # Test non-match
        self.assertFalse(self.rules_engine._folder_matches_pattern("other_folder", "Delivered_to_us_*"))
    
    def test_process_candidate_generation(self):
        """Test generation of process candidates."""
        # Create test files that should match expense rules
        files = [
            FileInfo(
                path="receipts/receipt1.jpg",
                size=1000,
                created=date.today(),
                modified=date.today(),
                extension=".jpg",
                folder="receipts",
                extracted_text="Uber receipt $25.00"
            ),
            FileInfo(
                path="receipts/receipt2.pdf",
                size=1000,
                created=date.today(),
                modified=date.today(),
                extension=".pdf",
                folder="receipts",
                extracted_text="Restaurant bill $45.00"
            )
        ]
        
        candidates = self.rules_engine.generate_process_candidates(files)
        
        # Should generate expense-related process
        expense_candidates = [c for c in candidates if "receipt" in c.name.lower() or "quickbooks" in c.name.lower()]
        self.assertTrue(len(expense_candidates) > 0)
        
        if expense_candidates:
            candidate = expense_candidates[0]
            self.assertEqual(candidate.cadence, Recurrence.DAILY)
            self.assertEqual(len(candidate.evidence_paths), 2)
    
    def test_opportunity_candidate_generation(self):
        """Test generation of opportunity candidates."""
        files = [
            FileInfo(
                path="branding/logo_design.psd",
                size=1000,
                created=date.today(),
                modified=date.today(),
                extension=".psd",
                folder="branding",
                extracted_text="Company logo design files"
            )
        ]
        
        candidates = self.rules_engine.generate_opportunity_candidates(files)
        
        # Should generate branding opportunity
        branding_candidates = [c for c in candidates if "branding" in c.title.lower()]
        self.assertTrue(len(branding_candidates) > 0)
        
        if branding_candidates:
            candidate = branding_candidates[0]
            self.assertIn("branding/logo_design.psd", candidate.evidence_paths)
            self.assertEqual(candidate.confidence, 0.7)


class TestConfigRules(unittest.TestCase):
    """Test configuration-based rules."""
    
    def test_default_config_rules(self):
        """Test that default configuration has expected rules."""
        config = get_default_config()
        
        # Should have expense rules
        self.assertIn("expenses", config.cadences)
        expense_rule = config.cadences["expenses"]
        self.assertEqual(expense_rule.recurrence, Recurrence.DAILY)
        self.assertIn("receipt", expense_rule.match)
        
        # Should have grant rules
        self.assertIn("grants", config.cadences)
        grant_rule = config.cadences["grants"]
        self.assertEqual(grant_rule.recurrence, Recurrence.BIWEEKLY)
        self.assertEqual(grant_rule.followup_after_days, 14)
        
        # Should have deliverable rules
        self.assertIn("deliverables", config.cadences)
        deliverable_rule = config.cadences["deliverables"]
        self.assertIsNone(deliverable_rule.recurrence)
        self.assertEqual(deliverable_rule.due_after_days, 5)


if __name__ == "__main__":
    unittest.main()
