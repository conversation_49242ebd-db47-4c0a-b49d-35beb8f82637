<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - Madia Application Solutions</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="images/Favicon.png" type="image/png">
    <img src="images/logo.png" alt="Madia Application Solutions Logo" style="display: block; margin: 0 auto; width: 200px;">
    <script>
        function sendEmail(event) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                message: formData.get('message')
            };

            // Simulate sending email (replace with actual email sending logic)
            console.log('Sending <NAME_EMAIL>', data);

            // Show thank you message
            document.getElementById('thankYouMessage').style.display = 'block';
            form.reset();
        }
    </script>
</head>
<body style="display: flex; flex-direction: column; min-height: 100vh;">
<header style="background-color: #003366; color: white; padding: 20px; position: sticky; top: 0; z-index: 1000;">
        <nav>
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="products.html">Products</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="blog.html">Blog</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>
<main style="display: grid; grid-template-columns: 1fr; gap: 20px;">
<section style="position: relative; background-image: url('images/i.png'); background-size: cover; background-position: center; padding: 20px;">
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); z-index: 1;"></div> <!-- Overlay -->
        <h1>Contact Us</h1>
        <form onsubmit="sendEmail(event)">
            <label for="name">Name:</label><br>
            <input type="text" id="name" name="name" required style="width: 100%;"><br><br>

            <label for="email">Email:</label><br>
            <input type="email" id="email" name="email" required style="width: 100%;"><br><br>

            <label for="message">Message:</label><br>
            <textarea id="message" name="message" required style="width: 100%; height: 150px;"></textarea>

            <button type="submit">Send Message</button>
        </form>
        <div id="thankYouModal" style="display:none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.7); z-index: 1000; justify-content: center; align-items: center;">
            <div style="background: white; padding: 20px; border-radius: 5px; text-align: center;">
                <h2>Thank You!</h2>
                <p>Your message has been sent. We will get back to you shortly.</p>
                <button onclick="closeModal()">Close</button>
            </div>
        </div>
    </main>
    <footer>
        <p>&copy; 2023 Madia Application Solutions. All rights reserved.</p>
    </footer>
</body>
