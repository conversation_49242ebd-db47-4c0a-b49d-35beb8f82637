import json

from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.aes_encrypt_decrypt import AESEncryptDecrypt


class EncryptionMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path in ["/docs", "/openapi.json", "/redoc"]:
            return await call_next(request)

        # Decrypt request body
        try:
            if request.method.upper() in ["POST", "PUT", "PATCH"] and "application/json" in request.headers.get(
                    "Content-Type").lower():
                body = await request.body()
                if body:
                    data = json.loads(body.decode()).get('data')
                    decrypted_data = AESEncryptDecrypt().decrypt(data)
                    request._body = json.dumps(json.loads(decrypted_data)).encode('utf-8')  # Set as bytes

        except Exception as e:
            return JSONResponse(content="Invalid encrypted data", status_code=400)
        # Proceed with the request
        response = await call_next(request)

        # Encrypt response body
        try:
            if response.headers.get("content-type") == "application/json":
                if response.status_code == 200:
                    response_content = b"".join([chunk async for chunk in response.body_iterator])
                    response_data = response_content.decode('utf-8')
                    encrypted_data = AESEncryptDecrypt().encrypt(json.dumps(json.loads(response_data)))
                    return JSONResponse(content={"data": encrypted_data}, media_type="application/json")

            return response
        except Exception as e:
            return JSONResponse(content="Error in encrypting response", status_code=500)
