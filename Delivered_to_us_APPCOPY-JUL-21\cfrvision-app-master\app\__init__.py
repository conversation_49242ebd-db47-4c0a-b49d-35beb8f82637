import inspect
import logging
import os
from logging.handlers import RotatingFileHandler


class LogFormatter(logging.Formatter):
    def format(self, record):
        # Get the current stack frame and function name
        frame = inspect.currentframe()
        while frame:
            if frame.f_code.co_name == record.funcName:
                break
            frame = frame.f_back
        # Get class name if it exists
        class_name = None
        if 'self' in frame.f_locals:
            class_name = frame.f_locals['self'].__class__.__name__

        # Set the class name in the record
        record.className = class_name
        return super(LogFormatter, self).format(record)


# Define the logging format
log_format = '%(asctime)s - %(levelname)s - %(className)s - %(funcName)s - line %(lineno)d - \n%(message)s'

# Set up the RotatingFileHandler
log_file = 'app.log'
max_log_size = 10 * 1024 * 1024  # 10 MB
backup_count = 10

file_handler = RotatingFileHandler(log_file, maxBytes=max_log_size, backupCount=backup_count)
file_formatter = LogFormatter(log_format)
file_handler.setFormatter(file_formatter)

# Set up the StreamHandler for console logging
console_handler = logging.StreamHandler()
console_formatter = LogFormatter(log_format)
console_handler.setFormatter(console_formatter)

# Get the root logger and set the handlers
logger = logging.getLogger()
logger.setLevel(logging.INFO)
logger.handlers = []  # Remove any default handlers
logger.addHandler(file_handler)

if os.getenv('HOST') in ['127.0.0.1', 'localhost', '0.0.0.0']:
    logger.addHandler(console_handler)
