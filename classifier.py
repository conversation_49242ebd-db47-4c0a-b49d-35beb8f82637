"""
File classification and topic extraction for the Startup Ops Indexer.
"""
import re
from typing import List, Set, Dict, Tuple
from datetime import datetime, date, timedelta
from pathlib import Path
import logging

from models import FileInfo, Config, ProcessCandidate, OpportunityCandidate, Recurrence

logger = logging.getLogger(__name__)


class FileClassifier:
    """Classifies files into topics and extracts task candidates."""
    
    def __init__(self, config: Config):
        self.config = config
        self.topic_keywords = self._build_topic_keywords()
    
    def _build_topic_keywords(self) -> Dict[str, List[str]]:
        """Build keyword mappings for topic classification."""
        return {
            'expenses': ['receipt', 'invoice', 'payment', 'charge', 'bill', 'expense', 'cost'],
            'grants': ['grant', 'funding', 'sbir', 'sttr', 'proposal', 'application'],
            'deliverables': ['delivered', 'delivery', 'milestone', 'completed', 'finished'],
            'branding': ['brand', 'logo', 'design', 'marketing', 'visual', 'identity'],
            'compliance': ['compliance', 'security', 'audit', 'requirement', 'regulation'],
            'pricing': ['pricing', 'tier', 'cost', 'price', 'subscription', 'plan'],
            'aws': ['aws', 'amazon', 'cloud', 'ec2', 's3', 'lambda'],
            'development': ['code', 'app', 'software', 'development', 'programming'],
            'business': ['business', 'plan', 'strategy', 'model', 'revenue'],
            'legal': ['legal', 'contract', 'agreement', 'terms', 'llc', 'incorporation'],
            'finance': ['finance', 'accounting', 'quickbooks', 'tax', 'bank', 'financial']
        }
    
    def classify_file(self, file_info: FileInfo) -> FileInfo:
        """Classify file into topics based on path, name, and content."""
        topics = set()
        
        # Classify by folder name
        folder_topics = self._classify_by_folder(file_info.folder)
        topics.update(folder_topics)
        
        # Classify by filename
        filename_topics = self._classify_by_filename(file_info.path)
        topics.update(filename_topics)
        
        # Classify by content
        content_topics = self._classify_by_content(file_info.extracted_text)
        topics.update(content_topics)
        
        file_info.topics = list(topics)
        return file_info
    
    def _classify_by_folder(self, folder: str) -> Set[str]:
        """Classify based on folder name."""
        topics = set()
        folder_lower = folder.lower()
        
        for topic, keywords in self.topic_keywords.items():
            if any(keyword in folder_lower for keyword in keywords):
                topics.add(topic)
        
        # Special folder patterns
        if 'accomplished' in folder_lower or 'week' in folder_lower:
            topics.add('accomplished')
        if 'delivered' in folder_lower:
            topics.add('deliverables')
        if 'grant' in folder_lower:
            topics.add('grants')
        if 'pricing' in folder_lower:
            topics.add('pricing')
        if 'compliance' in folder_lower or 'security' in folder_lower:
            topics.add('compliance')
        
        return topics
    
    def _classify_by_filename(self, path: str) -> Set[str]:
        """Classify based on filename."""
        topics = set()
        filename_lower = Path(path).name.lower()
        
        for topic, keywords in self.topic_keywords.items():
            if any(keyword in filename_lower for keyword in keywords):
                topics.add(topic)
        
        return topics
    
    def _classify_by_content(self, content: str) -> Set[str]:
        """Classify based on file content."""
        topics = set()
        content_lower = content.lower()
        
        for topic, keywords in self.topic_keywords.items():
            keyword_count = sum(1 for keyword in keywords if keyword in content_lower)
            if keyword_count >= 2:  # Require multiple keyword matches for content classification
                topics.add(topic)
        
        return topics


class RulesEngine:
    """Applies business rules to generate process and opportunity candidates."""
    
    def __init__(self, config: Config):
        self.config = config
    
    def generate_process_candidates(self, files: List[FileInfo]) -> List[ProcessCandidate]:
        """Generate process candidates based on configuration rules."""
        candidates = []
        
        for rule_name, rule in self.config.cadences.items():
            matching_files = self._find_matching_files(files, rule)
            
            if matching_files:
                candidate = self._create_process_candidate(rule_name, rule, matching_files)
                candidates.append(candidate)
        
        return candidates
    
    def generate_opportunity_candidates(self, files: List[FileInfo]) -> List[OpportunityCandidate]:
        """Generate opportunity candidates based on hints and content analysis."""
        candidates = []
        
        for hint in self.config.opportunity_hints:
            matching_files = [f for f in files if self._matches_opportunity_hint(f, hint)]
            
            if matching_files:
                candidate = self._create_opportunity_candidate(hint, matching_files)
                candidates.append(candidate)
        
        return candidates
    
    def _find_matching_files(self, files: List[FileInfo], rule) -> List[FileInfo]:
        """Find files that match a cadence rule."""
        matching = []
        
        for file_info in files:
            # Check folder matches
            if rule.match_folders:
                folder_match = any(
                    self._folder_matches_pattern(file_info.folder, pattern)
                    for pattern in rule.match_folders
                )
                if folder_match:
                    matching.append(file_info)
                    continue
            
            # Check keyword matches
            if rule.match:
                content_to_check = f"{file_info.path} {file_info.extracted_text}".lower()
                keyword_match = any(keyword.lower() in content_to_check for keyword in rule.match)
                if keyword_match:
                    matching.append(file_info)
        
        return matching
    
    def _folder_matches_pattern(self, folder: str, pattern: str) -> bool:
        """Check if folder matches a pattern (supports wildcards)."""
        if '*' in pattern:
            # Convert glob pattern to regex
            regex_pattern = pattern.replace('*', '.*')
            return bool(re.match(regex_pattern, folder, re.IGNORECASE))
        else:
            return folder.lower() == pattern.lower()
    
    def _create_process_candidate(self, rule_name: str, rule, files: List[FileInfo]) -> ProcessCandidate:
        """Create a process candidate from matching files."""
        # Calculate next due date
        next_due = self._calculate_next_due_date(rule)
        
        # Create evidence paths
        evidence_paths = [f.path for f in files[:5]]  # Limit to 5 files
        
        # Calculate confidence based on file count and recency
        confidence = min(1.0, len(files) / 3.0)  # Higher confidence with more files
        
        return ProcessCandidate(
            name=rule.rule,
            source_hint=f"{rule_name}/*",
            cadence=rule.recurrence,
            next_due=next_due,
            evidence_paths=evidence_paths,
            confidence=confidence
        )
    
    def _calculate_next_due_date(self, rule) -> date:
        """Calculate the next due date based on rule configuration."""
        today = date.today()
        
        if rule.due_after_days:
            return today + timedelta(days=rule.due_after_days)
        
        if rule.recurrence == Recurrence.DAILY:
            return today + timedelta(days=1)
        elif rule.recurrence == Recurrence.WEEKLY:
            return today + timedelta(days=7)
        elif rule.recurrence == Recurrence.BIWEEKLY:
            return today + timedelta(days=14)
        elif rule.recurrence == Recurrence.MONTHLY:
            return today + timedelta(days=30)
        else:
            return today + timedelta(days=7)  # Default to weekly
    
    def _matches_opportunity_hint(self, file_info: FileInfo, hint: str) -> bool:
        """Check if a file matches an opportunity hint."""
        hint_lower = hint.lower()
        
        # Check in path, folder, and content
        search_text = f"{file_info.path} {file_info.folder} {file_info.extracted_text}".lower()
        
        return hint_lower in search_text
    
    def _create_opportunity_candidate(self, hint: str, files: List[FileInfo]) -> OpportunityCandidate:
        """Create an opportunity candidate from matching files."""
        # Generate suggestions based on hint type
        suggestions = {
            'branding': 'Review and update brand materials',
            'linkedin': 'Update LinkedIn profile and post content',
            'aws': 'Review AWS configuration and costs',
            'architecture': 'Review system architecture documentation',
            'github': 'Review code repositories and documentation'
        }
        
        suggestion = suggestions.get(hint.lower(), f'Review {hint} related materials')
        
        evidence_paths = [f.path for f in files[:3]]  # Limit to 3 files
        
        return OpportunityCandidate(
            title=f"{hint.title()} Review",
            why_it_matters=f"Keep {hint} materials current and optimized",
            suggested_next_step=suggestion,
            evidence_paths=evidence_paths,
            confidence=0.7  # Medium confidence for opportunities
        )
