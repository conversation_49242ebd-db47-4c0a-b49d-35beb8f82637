import Home from "@/components/homes/home-9";
import Hero from "@/components/homes/home-9/heros/Hero1";
import { features4 } from "@/data/features";
import Image from "next/image";
import Link from "next/link";
import CFRVision from "./(guest-pages)/(cfrvision)/main-pages-cfrvision/page";
import MainServicesPage3 from "./(guest-pages)/(services)/main-pages-services-3/page";
import "bootstrap/dist/css/bootstrap.min.css";

export const metadata = {
  title: "CFRVision",
  description: "CFRVision Home Page",
};
export default function Home9MainDemoMultiPage({
  onePage = false,
  dark = false,
}) {
  return (
    <main id="main">
      <section className="home-section scrollSpysection" id="home">
        <div className="bg-shape-2 wow fadeIn">
          <Image
            src="/assets/images/demo-slick/bg-shape-2.svg"
            width={858}
            height={804}
            alt=""
          />
        </div>
        <Hero />
      </section>
      <section
        className={`page-section scrollSpysection  ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="about"
      >
        <div className="container position-relative">
          <div className="row">
            {/* Section Text */}
            <div className="col-lg-6 d-flex align-items-center order-first order-lg-last mb-md-60 mb-sm-40">
              <div className="w-100 wow fadeInUp">
                <h2 className="section-caption-slick mb-30 mb-sm-20">
                  About Us
                </h2>
                <h3 className="section-title mb-30">
                  Empowering Public Sector &nbsp;Entities Through Innovation
                </h3>
                <p className="text-gray mb-40">
                  CFRVision is the flagship product of Madia Application
                  Solutions, a SaaS company revolutionizing the way public
                  sector organizations approach financial reporting. Founded by
                  Melissa Madia, a visionary leader with over 20 years of
                  experience in public education and local government, our
                  mission is to make efficiency, transparency, and compliance
                  accessible to all public entities. Madia Application
                  Solutions, LLC is a woman owned business registered in the
                  State of Florida
                </p>
                {/* Features List */}
                <div className="row features-list mt-n20 mb-50 mb-sm-30">
                  {/* Features List Item */}
                  {features4.map((feature, index) => (
                    <div
                      key={index}
                      className="col-sm-6 col-lg-12 col-xl-6 d-flex mt-20"
                    >
                      <div className="features-list-icon">
                        <i className="mi-check" />
                      </div>
                      <div className="features-list-text">{feature.text}</div>
                    </div>
                  ))}
                  {/* End Features List Item */}
                </div>
                {/* End Features List */}
                <div className="local-scroll wch-unset">
                  {onePage ? (
                    <>
                      <a
                        href="#contact"
                        className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10"
                      >
                        <span>Learn more</span>
                      </a>
                    </>
                  ) : (
                    <>
                      <Link
                        href="#contact"
                        className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10"
                      >
                        <span>Learn more</span>
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>
            {/* End Section Text */}
            {/* Image */}
            <div className="col-lg-6 d-flex align-items-center">
              <div className="w-100 pe-lg-5">
                <div className="composition-5">
                  <div className="composition-5-decoration opacity-065">
                    <Image
                      src="/assets/images/demo-slick/decoration-1.svg"
                      alt=""
                      width={228}
                      height={228}
                    />
                  </div>
                  <div className="composition-5-image-1">
                    <div className="composition-5-image-1-inner">
                      <Image
                        src="/assets/j.png"
                        alt="Image Description"
                        width={850}
                        height={914}
                        className="wow scaleOutIn"
                        data-wow-offset={200}
                      />
                    </div>
                  </div>
                  <div className="composition-5-image-2">
                    <div className="composition-5-image-2-inner">
                      <Image
                        src="/assets/images/demo-slick/about-inner-img.png"
                        alt="Image Description"
                        width={850}
                        height={914}
                        className="wow scaleOutIn"
                        data-wow-offset={200}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* End Images */}
          </div>
        </div>
      </section>
      <CFRVision />
      <MainServicesPage3 />
      <Home />
    </main>
  );
}
