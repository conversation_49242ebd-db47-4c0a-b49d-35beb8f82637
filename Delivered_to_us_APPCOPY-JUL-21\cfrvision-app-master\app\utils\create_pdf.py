import os

from fpdf import FPDF
from datetime import datetime
from bs4 import BeautifulSoup

from app import logger


class PDF(FPDF):
    def header(self):
        try:
            # Add header image
            self.image('logo-img.png', 10, 8, 33)
        except Exception as e:
            print(f"Error exporting LLM check request results to PDF in logo:'{str(e)}'")
            logger.error(
                f"Error exporting LLM check request results to PDF in logo:'{str(e)}'")
        finally:
            try:
                self.set_font('Arial', 'B', 12)
                self.cell(0, 10, 'Validation Request Detail', border=False, ln=True, align='C')
                self.ln(10)
            except Exception as e:
                print(f"Error exporting LLM check request results to PDF in headers:'{str(e)}'")
                logger.error(
                    f"Error exporting LLM check request results to PDF in headers:'{str(e)}'")

    def footer(self):
        try:
            # Add footer text
            self.set_y(-15)
            self.set_font('Arial', 'I', 8)
            self.set_text_color(128)
            page = f"Page {self.page_no()}"
            self.cell(0, 10, f"{page}", 0, 0, "C")
        except Exception as e:
            print(f"Error exporting LLM check request results to PDF in footer:'{str(e)}'")
            logger.error(
                f"Error exporting LLM check request results to PDF in footer:'{str(e)}'")

def ensure_directory_exists(folder_name="llm_check_result_files"):
    """
    Creates the directory for the given file path if it doesn't already exist.
    """
    try:
        cwd = os.getcwd()
        print(f"Current working directory: {cwd}")
        logger.info(f"Target directory to create: {cwd}")
        two_levels_up = os.path.abspath(os.path.join(cwd, "..", ".."))
        print(f"Two levels up: {two_levels_up}")
        logger.info(f"Target directory to create: {two_levels_up}")
        target_dir = os.path.join(cwd, folder_name)
        logger.info(f"Target directory to create: {target_dir}")
        print(f"Target directory to create: {target_dir}")
        if not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)
            print(f"Created directory: {target_dir}")
        else:
            print(f"Directory already exists: {target_dir}")
    except Exception as e:
        print(f"Error in checking directory:'{str(e)}'")
        logger.error(
            f"Error in checking directory:'{str(e)}'")

async def html_to_pdf_with_fpdf(data, output_pdf_path):
    try:
        ensure_directory_exists()
        pdf = PDF()
        logger.info(f"Stage 1")
        pdf.add_page()
        pdf.set_auto_page_break(auto=True, margin=15)

        # Main font for text
        pdf.set_font("Arial", size=12)

        # Institution Info
        pdf.set_font("Arial", 'B', 14)
        pdf.cell(0, 10, data['institution_name'], ln=True)

        pdf.set_font("Arial", size=12)
        pdf.cell(0, 10, f"File Name: {data['file_name']}", ln=True)
        pdf.cell(0, 10, f"Draft/Final: {data['draft_final']}", ln=True)
        pdf.cell(0, 10, f"FY: {data['fy_end']}", ln=True)
        pdf.cell(0, 10, f"Version: {data['version']}", ln=True)
        pdf.cell(0, 10, f"Validation Category: {data['category_name']}", ln=True)
        pdf.multi_cell(0, 10, f"Validation Check Text: {data['check_name']}")
        logger.info(f"Stage 2")

        # Results Section
        pdf.ln(5)
        pdf.set_font("Arial", 'B', 12)
        pdf.cell(0, 10, "Results:", ln=True)
        pdf.set_font("Arial", size=11)

        for item in data['result']:
            created_at = datetime.fromtimestamp(item["created_at"]).strftime("%m/%d/%Y %H:%M")
            pdf.cell(0, 10, f"Created At: {created_at}", ln=True)
            pdf.set_font("Arial", size=10)

            # Manually format parsed_response if it's table-like
            soup = BeautifulSoup(item["parsed_response"], "html.parser")
            headers = [th.get_text(strip=True) for th in soup.find_all("th")]
            rows = [
                [td.get_text(strip=True) for td in row.find_all("td")]
                for row in soup.find_all("tr")[1:]
            ]
            # Combine headers and rows to measure column widths
            table_data = [headers] + rows

            # Calculate max width needed for each column
            def get_col_widths(pdf, data, padding=2):
                col_count = len(data[0])
                col_widths = [0] * col_count
                MIN_COL_WIDTH = 30
                for row in data:
                    for i, cell in enumerate(row):
                        cell_width = pdf.get_string_width(cell) + padding * 2
                        col_widths[i] = max(col_widths[i], cell_width, MIN_COL_WIDTH)
                return col_widths

            col_widths = get_col_widths(pdf, table_data)
            # Fix: scale widths if too wide
            max_width = pdf.w - 2 * pdf.l_margin
            total_width = sum(col_widths)
            if total_width > max_width:
                scale = max_width / total_width
                col_widths = [w * scale for w in col_widths]


            # Helper to get height of a row
            def get_row_height(row, col_widths, pdf):
                line_heights = []
                for i, cell in enumerate(row):
                    num_lines = len(pdf.multi_cell(col_widths[i], 5, cell, border=0, align='L', split_only=True))
                    line_heights.append(num_lines * 5)
                return max(line_heights) if line_heights else 5

            # === Draw Header Row ===
            pdf.set_font("Arial", 'B', 10)
            x_start = pdf.get_x()
            y_start = pdf.get_y()

            header_height = get_row_height(headers, col_widths, pdf)
            for i, header in enumerate(headers):
                col_width = col_widths[i]
                pdf.rect(x_start, y_start, col_width, header_height)
                pdf.set_xy(x_start, y_start)
                pdf.multi_cell(col_width, 5, header, border=0, align='L')
                x_start += col_width

            pdf.set_y(y_start + header_height)

            # === Draw Data Rows ===
            pdf.set_font("Arial", size=10)
            for row in rows:
                x_start = pdf.get_x()
                y_start = pdf.get_y()
                row_height = get_row_height(row, col_widths, pdf)

                for i, cell in enumerate(row):
                    col_width = col_widths[i]
                    # Draw cell border
                    pdf.rect(x_start, y_start, col_width, row_height)
                    # Write cell text (multi_cell to wrap, but manually reset X/Y)
                    pdf.set_xy(x_start, y_start)
                    pdf.multi_cell(col_width, 5, cell, border=0, align='L')
                    x_start += col_width  # move x to next column

                pdf.set_y(y_start + row_height)
        else:
            pdf.cell(0, 10, "No Result Found!", ln=True)

        # Save the PDF to the specified path
        logger.info(f"Stage 3")

        pdf.output(output_pdf_path)
        logger.info(
            f"Pdf creation success:'{output_pdf_path}'")
        print(f"PDF saved as {output_pdf_path}")
        return  output_pdf_path
    except Exception as e:
        print(f"Error exporting LLM check request results to PDF:'{str(e)}'")
        logger.error(
            f"Error exporting LLM check request results to PDF:'{str(e)}'")
        return None
