import os

from chargebee import Chargebee, Role
from dotenv import load_dotenv

from app import logger
from app.utils.exception_handling import ExceptionHandling

load_dotenv()

class ChargebeeOperations:

    def __init__(self):
        self.chargebee_api_key = os.getenv('CHARGEBEE_API_KEY')
        self.chargebee_site = os.getenv('CHARGEBEE_SITE')
        self.cb_client = Chargebee(api_key=self.chargebee_api_key, site=self.chargebee_site)

    async def update_chargebee_billing_info(self, data):
        try:
            response = self.cb_client.Customer.update_billing_info(data.user_id,
                                                              self.cb_client.Customer.UpdateBillingInfoParams(
                                                                  billing_address=self.cb_client.Customer.UpdateBillingInfoBillingAddressParams(
                                                                      first_name=data.first_name,
                                                                      last_name=data.last_name,
                                                                      email=data.email,
                                                                      line1=data.line1,
                                                                      line2=data.line2,
                                                                      line3=data.line3,
                                                                      city=data.city,
                                                                      state_code=data.state_code,
                                                                      state=data.state,
                                                                      zip=data.zip,
                                                                      country=data.country,
                                                                      validation_status=data.validation_status
                                                                  )
                                                              )
                                                              )
            if response.http_status_code == 200:
                return True
            else:
                logger.warning("Chargebee update billing info failed.", exc_info=True)
                return False

        except Exception as e:
            logger.exception(f"Error updating billing address details: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="ChargebeeOperations.update_chargebee_billing_info").exception_handling()
            raise
        return False


    async def cancel_chargebee_subscription(self, subscription_id):
        try:
            response = self.cb_client.Subscription.cancel_for_items(subscription_id,
                                                    self.cb_client.Subscription.CancelForItemsParams(
                                                        end_of_term=True
                                                    ))
            if response.http_status_code == 200:
                return True
            else:
                logger.warning("Chargebee cancel subscription failed.", exc_info=True)
                return False
        except Exception as e:
            logger.exception(f"Error cancelling subscription details: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="ChargebeeOperations.cancel_chargebee_subscription").exception_handling()
            raise


    async def add_chargebee_payment_method_card(self,customer_id, card_data):
        try:
            response = self.cb_client.PaymentSource.create_card(
                self.cb_client.PaymentSource.CreateCardParams(
                    customer_id=customer_id,
                    card=self.cb_client.PaymentSource.CreateCardCardParams(
                        number=card_data.masked_number,
                        cvv=card_data.cvv,
                        expiry_year=card_data.expiry_year,
                        expiry_month=card_data.expiry_month
                    )
                )
            )
            if response.http_status_code == 200:
                return response, True
            else:
                logger.warning("Chargebee cancel subscription failed.", exc_info=True)
                return response, False
        except Exception as e:
            logger.exception(f"Error cancelling subscription details: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="ChargebeeOperations.cancel_chargebee_subscription").exception_handling()
            raise


    async def remove_payment_source_card(self, payment_source_id):
        try:
            response = self.cb_client.PaymentSource.delete(payment_source_id)
            if response.http_status_code == 200:
                return response, True
            else:
                logger.warning("Chargebee card deletion failed.", exc_info=True)
                return response, False
        except Exception as e:
            logger.exception(f"Error deleting card details: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="ChargebeeOperations.remove_payment_source_card").exception_handling()
            raise

    async def get_invoice_pdf(self, invoice_id):
        try:
            response = self.cb_client.Invoice.pdf(invoice_id)
            if response.http_status_code == 200:
                final_response = response.download.raw_data
                return final_response, True
            else:
                logger.warning("Chargebee retrieve invoice pdf failed.", exc_info=True)
                return response, False
        except Exception as e:
            logger.exception(f"Error deleting card details: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="ChargebeeOperations.get_invoice_pdf").exception_handling()
            raise

    async def chargebee_update_payment_method(self, user_id, payment_source_id):
        try:
            response = self.cb_client.Customer.assign_payment_role(user_id,
    self.cb_client.Customer.AssignPaymentRoleParams(
        payment_source_id=payment_source_id,
        role=Role.PRIMARY
    )
)
            if response.http_status_code == 200:
                return response, True
            else:
                logger.warning("Chargebee update payment method failed.", exc_info=True)
                return response, False
        except Exception as e:
            logger.exception(f"Error deleting card details: {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e), function_name="ChargebeeOperations.chargebee_remove_payment_source_card").exception_handling()
            raise
