"""
Configuration loading and management for the Startup Ops Indexer.
"""
import yaml
from pathlib import Path
from typing import Optional
from models import Config, CadenceRule, Recurrence


def load_config(config_path: Optional[str] = None) -> Config:
    """Load configuration from YAML file."""
    if config_path is None:
        config_path = "config.yml"
    
    config_file = Path(config_path)
    if not config_file.exists():
        # Return default config if file doesn't exist
        return Config()
    
    with open(config_file, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    if not data:
        return Config()
    
    # Convert cadences to CadenceRule objects
    cadences = {}
    if 'cadences' in data:
        for name, rule_data in data['cadences'].items():
            # Convert recurrence string to enum if present
            if 'recurrence' in rule_data and rule_data['recurrence']:
                try:
                    rule_data['recurrence'] = Recurrence(rule_data['recurrence'])
                except ValueError:
                    rule_data['recurrence'] = None
            
            cadences[name] = CadenceRule(**rule_data)
    
    data['cadences'] = cadences
    
    return Config(**data)


def save_config(config: Config, config_path: Optional[str] = None) -> None:
    """Save configuration to YAML file."""
    if config_path is None:
        config_path = "config.yml"
    
    # Convert to dict and handle enums
    data = config.model_dump()
    
    # Convert recurrence enums back to strings
    for name, rule in data['cadences'].items():
        if rule.get('recurrence'):
            rule['recurrence'] = rule['recurrence'].value if hasattr(rule['recurrence'], 'value') else str(rule['recurrence'])
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(data, f, default_flow_style=False, sort_keys=False)


def get_default_config() -> Config:
    """Get a default configuration with common business rules."""
    return Config(
        timezone="America/New_York",
        cadences={
            "expenses": CadenceRule(
                match=["receipt", "receipts", "invoice", "chargebee", "stripe"],
                recurrence=Recurrence.DAILY,
                rule="Enter receipts in QuickBooks"
            ),
            "grants": CadenceRule(
                match_folders=["Possible_Grants_For_Us"],
                recurrence=Recurrence.BIWEEKLY,
                rule="Grant follow-up",
                followup_after_days=14
            ),
            "deliverables": CadenceRule(
                match_folders=["Delivered_to_us_*"],
                recurrence=None,
                due_after_days=5,
                rule="Test deliverable and send feedback"
            )
        },
        opportunity_hints=["branding", "linkedin", "aws", "architecture"],
        auto_complete_patterns=["submitted", "paid", "approved", "sent", "accomplished"],
        ignore_patterns=["node_modules", ".git", "__pycache__", "*.pyc"]
    )
