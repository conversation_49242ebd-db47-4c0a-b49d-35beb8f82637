import enum
import time

from sqlalchemy import <PERSON>umn, Integer, Boolean, BigInteger, ForeignKey, BLOB, String, Text, JSON
from sqlalchemy.dialects.postgresql import VARCHAR, TEXT, ENUM
from sqlalchemy.orm import relationship, declarative_base

Base = declarative_base()


def current_epoch_time():
    return int(time.time())

class RoleEnum(enum.Enum):
    user = "user"
    admin = "admin"

class DefaultColumns:
    is_active = Column(Boolean, default=True)
    created_at = Column(BigInteger, default=current_epoch_time, nullable=False)
    updated_at = Column(BigInteger, default=current_epoch_time, nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)


class User(Base, DefaultColumns):
    __tablename__ = 'users'
    id = Column(String, primary_key=True)
    email = Column(String, nullable=False, unique=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    password_hash = Column(String, nullable=False)
    access_token = Column(String, nullable=True)
    refresh_token = Column(String, nullable=True)
    profile_picture = Column(Text, nullable=True)
    organization = Column(String, nullable=True)
    organization_address = Column(JSON, nullable=True)
    organization_contact = Column(String, nullable=True)
    reset_token = Column(String, nullable=True)
    reset_token_exp = Column(Integer, nullable=True)
    role = Column(ENUM(RoleEnum), default=RoleEnum.user, nullable=True)
    subscription_end = Column(Integer, nullable=True)
    subscription_start = Column(Integer, nullable=True)

    auth = relationship("UserAuth", uselist=False, lazy="selectin",back_populates="user")
    files = relationship("File", lazy="selectin",back_populates="user")
    llm_check_requests = relationship("LlmCheckRequest", lazy="selectin",back_populates="user")


class UserAuth(Base, DefaultColumns):
    __tablename__ = 'user_auth'
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    reset_token = Column(String, nullable=True)
    reset_token_expiry = Column(Integer, nullable=True)

    user = relationship("User", lazy="selectin",back_populates="auth")


class File(Base, DefaultColumns):
    __tablename__ = 'files'
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    file_name = Column(String, nullable=False)
    fy_end = Column(String, nullable=True)
    draft_final = Column(String, nullable=True)
    version = Column(String, nullable=True)
    upload_date = Column(String, nullable=True)
    pages = Column(Integer, nullable=True)
    size = Column(Integer, nullable=True)
    status = Column(String, nullable=True)
    comment = Column(Text, nullable=True)
    file_path = Column(String, nullable=False)

    chunks = relationship("PdfChunk", lazy="selectin",back_populates="file")
    extracted_full_text = relationship("ExtractedFullText", uselist=False, lazy="selectin",back_populates="file")
    llm_check_requests = relationship('LlmCheckRequest', lazy="selectin",back_populates='file')
    user = relationship("User", lazy="selectin",back_populates="files")


class PdfChunk(Base, DefaultColumns):
    __tablename__ = 'pdf_chunks'
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('files.id'), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    chunk_text = Column(Text, nullable=False)
    extraction_date = Column(Integer, default=current_epoch_time, nullable=False)
    page_number = Column(Integer, nullable=True)
    is_table = Column(Boolean, default=False)

    file = relationship("File", lazy="selectin",back_populates="chunks")
    chunk_embeddings = relationship("ChunkEmbedding", lazy="selectin",back_populates="pdf_chunk")


class ChunkEmbedding(Base, DefaultColumns):
    __tablename__ = 'chunk_embeddings'
    id = Column(Integer, primary_key=True, autoincrement=True)
    chunk_id = Column(Integer, ForeignKey('pdf_chunks.id'), nullable=False)
    embedding = Column(BLOB, nullable=False)

    pdf_chunk = relationship("PdfChunk", lazy="selectin",back_populates="chunk_embeddings")


class ExtractedFullText(Base, DefaultColumns):
    __tablename__ = 'extracted_full_text'
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('files.id'), nullable=False)
    full_text = Column(Text, nullable=False)
    extraction_date = Column(Integer, default=current_epoch_time, nullable=False)

    file = relationship("File", lazy="selectin",back_populates="extracted_full_text")


class AiCheckCategory(Base, DefaultColumns):
    __tablename__ = 'ai_check_categories'
    category_id = Column(Integer, primary_key=True, autoincrement=True)
    category_name = Column(String, nullable=False)
    category_description = Column(Text, nullable=True)
    category_order = Column(Integer, nullable=True)

    ai_checks = relationship("AiCheck", lazy="selectin",back_populates="category")


class AiCheck(Base, DefaultColumns):
    __tablename__ = 'ai_checks'
    check_id = Column(Integer, primary_key=True, autoincrement=True)
    check_display_number = Column(Integer, nullable=True)
    check_name = Column(Text, nullable=False)
    check_prompt = Column(Text, nullable=True)
    description = Column(Text, nullable=True, default="")
    check_extended_description = Column(Text, nullable=True, default="")
    check_relevant_sections = Column(Text, nullable=True, default="")
    check_expected_result_format = Column(Text, nullable=True, default="")
    category_id = Column(Integer, ForeignKey('ai_check_categories.category_id'))

    category = relationship("AiCheckCategory", lazy="selectin",back_populates="ai_checks")
    llm_check_requests = relationship(
        "LlmCheckRequest",
        lazy="selectin",back_populates="ai_check",
        primaryjoin="and_(AiCheck.check_id == LlmCheckRequest.check_id, LlmCheckRequest.is_deleted == False)"
    )


class LlmCheckRequest(Base, DefaultColumns):
    __tablename__ = 'llm_check_requests'
    request_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    document_id = Column(Integer, ForeignKey('files.id'), nullable=False)
    check_id = Column(Integer, ForeignKey('ai_checks.check_id'), nullable=False)
    prompt = Column(Text, nullable=True)
    retrieved_chunks = Column(TEXT, nullable=True)
    llm_provider = Column(VARCHAR, nullable=False)
    model_used = Column(VARCHAR, nullable=False)
    status = Column(VARCHAR, nullable=True)
    chunk_selection_method = Column(VARCHAR, nullable=True)
    result = Column(Text, nullable=True)
    prompt_embedding = Column(BLOB)

    ai_check = relationship("AiCheck", lazy="selectin",back_populates="llm_check_requests")
    file = relationship('File', lazy="selectin",back_populates='llm_check_requests')
    llm_check_response = relationship('LlmCheckResponse', lazy="selectin",back_populates='llm_check_requests')
    user = relationship("User", lazy="selectin", back_populates="llm_check_requests")

class ApiKey(Base, DefaultColumns):
    __tablename__ = 'api_keys'

    id = Column(Integer, primary_key=True, autoincrement=True)
    provider = Column(String, nullable=False)
    api_key = Column(String, nullable=False)
    available_models = Column(String, nullable=False)


class LlmCheckResponse(Base, DefaultColumns):
    __tablename__ = 'llm_check_response'

    id = Column(Integer, primary_key=True, autoincrement=True)
    request_id = Column(Integer,ForeignKey('llm_check_requests.request_id'), nullable=False)
    raw_response = Column(JSON)
    parsed_response = Column(JSON)
    status_code = Column(Integer)
    error_message = Column(Text)
    processing_time = Column(Text)
    prompt_tokens = Column(Integer)
    completion_tokens = Column(Integer)
    total_tokens = Column(Integer)

    llm_check_requests = relationship("LlmCheckRequest", lazy="selectin", back_populates="llm_check_response")


class ContactUs(Base, DefaultColumns):
    __tablename__ = 'contact_us'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    email = Column(String, nullable=False)
    organization = Column(String, nullable=False)
    message = Column(String, nullable=False)
    status = Column(String, default="pending")
    notes = Column(Text, nullable=True)


class StaticPages(Base, DefaultColumns):
    __tablename__ = 'static_pages'
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String, nullable=False)
    name = Column(String, nullable=False)
    content = Column(Text, nullable=False)