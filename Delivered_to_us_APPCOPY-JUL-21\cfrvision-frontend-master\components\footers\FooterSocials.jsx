import React from "react";

export default function FooterSocials() {
  const socials = [
    {
      name: "LinkedIn",
      icon: "fa-linkedin",
      url: "https://www.linkedin.com/company/madia-application-solutions/",
    },
    // { name: "Twitter", icon: "fa-twitter", url: "#" },
    {
      name: "Facebook",
      icon: "fa-facebook",
      url: "https://www.facebook.com/share/1BMVA7ZUSY/",
    },
    // { name: "YouTube", icon: "fa-youtube", url: "#" },
    // { name: "Pinterest", icon: "fa-pinterest", url: "#" },
  ];

  return (
    <>
      {socials.map((social, index) => (
        <li key={index}>
          <a href={social.url} rel="noopener nofollow" target="_blank">
            <i className={social.icon} />{" "}
            <span className="text-secondary fw-normal">{social.name}</span>
          </a>
        </li>
      ))}
    </>
  );
}
