"use client";
import axiosInstance from "@/utlis/axios";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { Icon } from "@iconify/react";
import dynamic from "next/dynamic";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Col, Row } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import Toaster from "../common/Toaster";
import ContactUsList from "./ContactUsList";
import StaticPageList from "./StaticPageList";
import Loading from "../common/Loading";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);
const schema = v.object({
  api_key: v.pipe(
    v.string("A"),
    v.minLength(1, "Api key cannot be empty"),
    v.nonEmpty("Api key is required")
  ),
});

const Administration = () => {
  const [loading, setLoading] = useState(false);
  const [apiKeyDetails, setApiKeyDetails] = useState(null);
  const [isView, setIsView] = useState(false);
  const [modelName, setModelName] = useState("");
  const [isDataFetched, setIsDataFetched] = useState(false);

  const { contextHolder, showToast } = Toaster();

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      api_key: "",
    },
  });

  const fetchApiKeys = async () => {
    try {
      setIsDataFetched(true);
      const response = await axiosInstance.post("/v1/cms/get-api_keys");
      setApiKeyDetails(response?.data?.data);
      setValue("api_key", response?.data?.data?.[0]?.api_key);
    } catch (error) {
      console.error("Failed to fetch AI checks:", error);
    } finally {
      setIsDataFetched(false);
    }
  };

  const fetchModelList = async () => {
    setIsDataFetched(true);
    try {
      const response = await axiosInstance.post(
        "/v1/llm_check_request/available-models"
      );
      setModelList(response?.data?.data);
      setIsDataFetched(false);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setIsDataFetched(false);
    }
  };

  useEffect(() => {
    fetchApiKeys();
    fetchModelList();
  }, []);

  const handleModelName = () => {
    const data = apiKeyDetails;
    if (data[0]?.available_models?.length)
      data[0].available_models += "," + modelName;
    else data[0].available_models += modelName;
    setApiKeyDetails(data);
    setModelName("");
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && modelName.trim()) {
      e.preventDefault();
      const data = apiKeyDetails;
      if (data[0]?.available_models?.length)
        data[0].available_models += "," + modelName;
      else data[0].available_models += modelName;
      setApiKeyDetails(data);
      setModelName("");
    }
  };

  const onSubmit = async (data) => {
    try {
      if (!apiKeyDetails?.[0]?.available_models?.length) {
        showToast({
          type: "error",
          message: "At least one model name is required",
        });
        return;
      }
      setLoading(true);
      const payload = {
        id: apiKeyDetails?.[0]?.id,
        is_active: true,
        ...data,
        available_models: apiKeyDetails?.[0]?.available_models,
      };
      const response = await axiosInstance.post(
        "/v1/cms/edit-api_keys",
        payload
      );
      console.log("response: ", response);
      setLoading(false);
      showToast({
        type: "success",
        message: response.data.message || "API Keys updated successfully.",
      });
      fetchApiKeys();
      return;
    } catch (error) {
      setLoading(false);
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
    }
  };

  const handleRemoveModelName = (index) => {
    let data = apiKeyDetails;
    const modelName = data?.[0]?.available_models
      ?.replaceAll("'", "")
      ?.split(",");
    modelName.splice(index, 1);
    data = [{ ...data[0], available_models: modelName?.join(",") }];
    setApiKeyDetails(data);
  };

  return (
    <>
      {contextHolder}
      {isDataFetched && <Loading />}
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="text-start">
              <h5 className="page-title text-start mb-0">LLM Configuration</h5>
              <form
                noValidate
                autoComplete="off"
                onSubmit={handleSubmit(onSubmit)}
                className="mb-30"
              >
                <Row className="mt-5">
                  <div className="primary-card-view">
                    <div>
                      <h4>OpenAI configuiration</h4>
                    </div>
                    <div className="mb-4">
                      <span>Available Model: </span>
                      <div className="mb-3 d-flex gap-3">
                        <input
                          placeholder="Enter model name"
                          value={modelName}
                          id="modelName"
                          onChange={(e) => setModelName(e.target.value)}
                          onKeyDown={handleKeyDown}
                          type="text"
                          className="form-group input-md round form-control"
                          style={{ width: "400px", background: "#fff" }}
                          name="off-modelName"
                          readOnly // <-- block autofill on load
                          onFocus={(e) => e.target.removeAttribute("readOnly")}
                        />
                      </div>
                      <div className="d-flex gap-2">
                        {apiKeyDetails?.[0]?.available_models &&
                          apiKeyDetails?.[0]?.available_models
                            ?.replaceAll("'", "")
                            ?.split(",")
                            ?.map((item, index) => (
                              <>
                                <div className="badge badge-complete d-flex gap-2">
                                  <p className="mb-0">{item}</p>
                                  <Icon
                                    icon="mdi:close"
                                    cursor="pointer"
                                    onClick={() => handleRemoveModelName(index)}
                                  />
                                </div>
                              </>
                            ))}
                      </div>
                    </div>
                    <Col md={12}>
                      <span>OpenAI API Key</span>
                      <Row>
                        <Col md={6}>
                          <div className="input-wrapper">
                            <Controller
                              name="api_key"
                              control={control}
                              rules={{ required: true }}
                              render={({ field }) => (
                                <input
                                  {...field}
                                  name="api_key"
                                  id="api_key"
                                  placeholder="Enter Api Key"
                                  required
                                  value={field.value}
                                  onChange={field.onChange}
                                  onBlur={field.onBlur}
                                  ref={field.ref}
                                  type={isView ? "text" : "password"}
                                  className="input-field"
                                />
                              )}
                            />
                            <div className="icon-wrapper">
                              <Icon
                                icon={!isView ? "mdi-eye" : "mdi-eye-off"}
                                width={24}
                                height={24}
                                className="cursor-pointer"
                                onClick={() => setIsView(!isView)}
                              />
                              <Icon
                                icon="mdi:trash-outline"
                                width="24"
                                height="24"
                                color="red"
                                cursor={"pointer"}
                                onClick={() => setValue("api_key", "")}
                              />
                            </div>
                          </div>
                        </Col>
                        <Col
                          md={6}
                          className="d-flex align-items-center justify-content-end m-auto gap-2"
                        >
                          <Button type="submit" variant="primary">
                            {loading ? "Saving..." : "Save"}
                          </Button>
                          {/* <Button variant="success">Next</Button> */}
                        </Col>
                      </Row>
                    </Col>
                    {errors?.api_key && (
                      <span className="text-red">
                        {errors?.api_key.message}
                      </span>
                    )}
                    <Col md={12} className="mt-2">
                      <span
                        style={{
                          color: "#22c443",
                        }}
                        className="text-green"
                      >
                        ✓ Configured
                      </span>
                    </Col>
                    {/* <Col md={12} className="mt-2">
                    <span>Google Gemini API Key</span>
                    <Row>
                      <Col md={6}>
                        <div className="input-wrapper">
                          <input
                            placeholder="Enter Gemini Api key"
                            className="input-field"
                          />
                        </div>
                      </Col>
                      <Col
                        md={6}
                        className="d-flex align-items-center justify-content-end m-auto gap-2"
                      >
                        <Button type="submit" variant="primary">
                          Save
                        </Button>
                      </Col>
                    </Row>
                  </Col>
                  <Col md={12} className="mt-2">
                    <span className="text-red">x Not Configured</span>
                  </Col> */}
                    {/* <Col md={12} className="mt-3">
                      <h4>Important Notes</h4>
                      <ul className="order-list">
                        <li className="content">
                          API keys are stored securely in the database
                        </li>
                        <li className="content">
                          Keys are encrypted before storage
                        </li>
                        <li className="content">
                          You can update key at any time by entering a new value
                          and clicking dave
                        </li>
                        <li className="content">
                          To disable a key, clear the field and click dave
                        </li>
                      </ul>
                    </Col>
                    <Col className="mt-4">
                      <div>Usage Statistics</div>
                      <Button variant="primary" className="mt-3">
                        Referesh Data
                      </Button>
                    </Col> */}
                  </div>
                </Row>
              </form>

              {/* Close Button */}
              <ContactUsList />
              <StaticPageList />
            </div>
          </ParallaxContainer>
        </section>
      </main>
    </>
  );
};

export default Administration;
