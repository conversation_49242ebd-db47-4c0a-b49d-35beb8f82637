import json
from enum import Enum
from typing import Optional

from pydantic import BaseModel, <PERSON>, model_validator, Json

from app.utils.aes_encrypt_decrypt import AESEncryptDecrypt

from app.utils.s3_operations import AWSFileOperations


class RoleEnum(str, Enum):
    admin = 'admin'
    user = 'user'


class EmailSchema:
    email: str = Field(...,
                       json_schema_extra={
                           'description': 'Email id',
                           'examples': ['<EMAIL>'],
                       })


class LoginRequest(EmailSchema, BaseModel):
    password: str = Field(...,
                          json_schema_extra={
                              'description': 'Password of the user (minimum 8 characters).',
                              'examples': ['password123'],
                          })

    class Config:
        use_enum_values = True
        orm_mode = True

class UserResponseSchema(EmailSchema, BaseModel):
    id: str
    first_name: str
    last_name: str
    organization: Optional[str]
    organization_address: Optional[Json]
    organization_contact: Optional[str]
    profile_picture: Optional[str]
    role: RoleEnum
    refresh_token: Optional[str]
    access_token: Optional[str]

    class Config:
        use_enum_values = True
        orm_mode = True

    @model_validator(mode='before')
    @classmethod
    def update_profile_picture(cls, values):
        if profile_pic:=values.get('profile_picture'):
            profile_img = AWSFileOperations().get_presigned_url(user_id=values.get('id'), file_name=profile_pic)
            if profile_img:
                values['profile_picture'] = profile_img
        if not values.get('organization_address'):
            values['organization_address'] = json.dumps({
                "organization_address_street1":"",
                "organization_address_street2":"",
                "organization_address_city":"",
                "organization_address_state":"",
                "organization_address_zip_code":""
            })
        return values


class PasswordResetRequest(EmailSchema, BaseModel):
    pass


class VerifyResetTokenRequest(BaseModel):
    token: str = Field(..., json_schema_extra={
        'description': 'Enter reset token',
        'examples': ['nfkjdshfkjdhsfjdhs456'],
    })


class SetPasswordRequest(BaseModel):
    user_id: str = Field(..., json_schema_extra={
        'description': 'User ID',
        'examples': ['ghgj45a'],
    })
    password: str = Field(..., json_schema_extra={
        'description': 'Enter password',
        'examples': ['123456'],
    })
