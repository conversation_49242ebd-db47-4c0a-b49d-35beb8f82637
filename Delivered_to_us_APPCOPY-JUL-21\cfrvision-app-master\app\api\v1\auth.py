from fastapi import Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import auth_router
from app.database import get_db
from app.operations.auth_operations.auth_operations import AuthOperations
from app.schemas.auth import LoginRequest, PasswordResetRequest, VerifyResetTokenRequest, SetPasswordRequest
from app.utils.jwt_authentication.jwt_handler import J<PERSON><PERSON><PERSON>earer


@auth_router.post("/login")
async def login(data: LoginRequest, db: AsyncSession = Depends(get_db)):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await AuthOperations(db).login_operation(data=data)

@auth_router.post("/token/refresh")
async def refresh_token(user: dict = Depends(JWTBearer()),db: AsyncSession = Depends(get_db)):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await AuthOperations(db).refresh_token_operation(user_id=user.get('user_id'),token=user.get('token',''))


@auth_router.post("/forgot-password")
async def forgot_password(data: PasswordResetRequest,db: AsyncSession = Depends(get_db),
                          background_tasks: BackgroundTasks = BackgroundTasks()):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await AuthOperations(db).forgot_password_operation(data=data,background_tasks=background_tasks)


@auth_router.post("/verify-token")
async def verify_token(data: VerifyResetTokenRequest, db: AsyncSession = Depends(get_db),
                     background_tasks: BackgroundTasks = BackgroundTasks()):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await AuthOperations(db).token_verification_operation(data=data,background_tasks=background_tasks)

@auth_router.post("/set-password")
async def set_password(data: SetPasswordRequest,db: AsyncSession = Depends(get_db)):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await AuthOperations(db).set_password_operation(data=data)

@auth_router.post("/logout")
async def logout(user: dict = Depends(JWTBearer()),db: AsyncSession = Depends(get_db)):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await AuthOperations(db).logout_operations(user_id=user.get('user_id',''))



