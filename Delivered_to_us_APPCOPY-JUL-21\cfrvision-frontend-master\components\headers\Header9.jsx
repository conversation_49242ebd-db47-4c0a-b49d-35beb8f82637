"use client";
import { closeMobileMenu, toggleMobileMenu } from "@/utlis/toggleMobileMenu";
import Nav from "./components/Nav";

import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { usePathname } from "next/navigation";

export default function Header9({ links }) {
  const [auth, setAuth] = useState(null); // Set initial state to null
  const path = usePathname();
  const ref = useRef(null);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleClickOutside = (event) => {
    if (ref.current && !ref.current.contains(event.target)) {
      closeMobileMenu();
    }
  };

  useEffect(() => {
    // Access localStorage on mount, which runs after the first render
    const authValue = localStorage?.getItem("auth");
    if (authValue) {
      setAuth(authValue);
    }
  }, [path]);

  return (
    <div className="main-nav-sub full-wrapper">
      <div className="nav-logo-wrap position-static local-scroll">
        <a href="#top" className="logo">
          <Image
            src="/assets/images/demo-slick/logo.svg"
            alt="Logo"
            width={106}
            height={36}
            className="light-mode-logo"
          />
        </a>
      </div>
      {/* Mobile Menu Button */}
      <div
        onClick={toggleMobileMenu}
        className="mobile-nav"
        role="button"
        tabIndex={0}
      >
        <i className="mobile-nav-icon" />
        <span className="visually-hidden">Menu</span>
      </div>
      {/* Main Menu */}
      <div className="inner-nav desktop-nav">
        <ul
          key={links?.length}
          ref={ref}
          className="clearlist scroll-nav local-scroll justify-content-end scrollspyLinks"
        >
          <Nav links={links} />
          {/* <li className="desktop-nav-display">
            <div className="vr mt-2" />
          </li> */}
          {/* Languages */}
          {/* <LanguageSelect /> */}
          {/* End Languages */}
          <li>
            {auth ? (
              <a
                href="/"
                className="opacity-1 no-hover text-black"
                onClick={() => localStorage?.clear()}
              >
                <span
                  className="btn btn-mod btn-color-light btn-border-white-light btn-small btn-circle"
                  data-btn-animate="y"
                >
                  Sign Out
                </span>
              </a>
            ) : (
              <a href="/login" className="opacity-1 no-hover">
                <span
                  className="btn btn-mod btn-color-light btn-border-white-light btn-small btn-circle"
                  data-btn-animate="y"
                >
                  Sign In
                </span>
              </a>
            )}
          </li>
        </ul>
      </div>
      {/* End Main Menu */}
    </div>
  );
}
