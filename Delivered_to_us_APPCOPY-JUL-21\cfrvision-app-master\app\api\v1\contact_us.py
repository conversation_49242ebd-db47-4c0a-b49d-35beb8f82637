from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import contact_us_router
from app.database import get_db
from app.operations.contact_us_operations.contact_us_operations import ContactUsOperations
from app.schemas.contact_us import AddContactUsSchema


@contact_us_router.post("/add")
async def create_contact_us_api(data: AddContactUsSchema, db: AsyncSession = Depends(get_db)):
    return await ContactUsOperations(db).create_contact_us(data)
