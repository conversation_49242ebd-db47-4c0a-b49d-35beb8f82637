import json
import os
import time

from fastapi import Depends
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app import logger
from app.database import get_db
from app.database.models import User
from app.schemas.profile import BasicUserResponse
from app.utils.constants import (profile_details_fetched, profile_details_fetch_failed, profile_updated_success,
                                 profile_updated_failed,
                                 user_not_found, password_updated,
                                 exception_occurred, password_incorrect, password_invalid)
from app.utils.exception_handling import ExceptionHandling
from app.utils.jwt_authentication.jwt_handler import verify_password, get_password_hash
from app.utils.s3_operations import AWSFileOperations


class ProfileOperations:

    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    # async def get_subscription_details(self, user_id):
    #     try:
    #         # Retrieve all AI checks with their categories
    #         stmt_data = select(Subscription).where(Subscription.customer_id == user_id,
    #                                                Subscription.is_deleted == False).desc('created_at')
    #         result_data = await self.db.execute(stmt_data)
    #         data = result_data.scalars().first()
    #         formatted_data = json.loads(SubscriptionData.model_validate(data).model_dump_json())
    #         return formatted_data
    #
    #     except Exception as e:
    #         logger.exception(f"Error retrieving subscription data: {str(e)}", exc_info=True)
    #         return []

    async def get_basic_profile_details(self, user_id):
        try:
            # Retrieve all AI checks with their categories
            stmt_user = select(User).where(User.id == user_id, User.is_deleted == False)
            result_user = await self.db.execute(stmt_user)
            user = result_user.scalars().first()
            # user.profile_picture = f"https://{AESEncryptDecrypt().decrypt(os.getenv("S3_BUCKET_NAME"))}.s3.{AESEncryptDecrypt().decrypt(os.getenv("AWS_REGION"))}.amazonaws.com/files/{user.id}/{user.profile_picture}" if user.profile_picture else ''
            # For getting the latest active subscripiton
            # subscription_data = await self.get_subscription_details(user_id)
            user_data = json.loads(BasicUserResponse.model_validate(user.__dict__).model_dump_json())
            # user_data.update({'subscription': subscription_data})
            api_response = {
                "data": user_data,
                "error": "",
                "message": profile_details_fetched,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error retrieving basic profile details: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": profile_details_fetch_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    # async def get_card_details(self, data, user_id):
    #     try:
    #         # Retrieve all AI checks with their categories
    #         stmt_data = select(Card).where(Card.user_id == user_id, Card.is_deleted == False)
    #         if data.is_pagination:
    #             skip = (data.page_no - 1) * data.page_size
    #             stmt_data = stmt_data.offset(
    #                 skip).limit(data.page_size)
    #         result_data = await self.db.execute(stmt_data)
    #         data = result_data.scalars().all()
    #         formatted_data = [json.loads(CardData.model_validate(data).model_dump_json()) for data in
    #                           data]
    #
    #         api_response = {
    #             "data": formatted_data,
    #             "error": "",
    #             "message": card_details_fetched,
    #             "status": True,
    #         }
    #         return JSONResponse(content=api_response, status_code=200)
    #
    #     except Exception as e:
    #         logger.exception(f"Error retrieving Card details: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {"card_data": []},
    #             "error": str(e),
    #             "message": card_details_fetch_failed,
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)
    #
    # async def get_invoice_details(self, data, user_id):
    #     try:
    #         if data.id:
    #             stmt_data = select(Invoice).where(Invoice.id == str(data.id), Invoice.customer_id == user_id,
    #                                               Invoice.is_deleted == False)
    #             result_data = await self.db.execute(stmt_data)
    #             invoice_data = result_data.scalars().first()
    #             if not invoice_data:
    #                 api_response = {
    #                     "data": {},
    #                     "error": invoice_not_found,
    #                     "message": invoice_not_found,
    #                     "status": False,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=404)
    #             formatted_data = json.loads(InvoiceData.model_validate(invoice_data).model_dump_json())
    #         else:
    #             # Retrieve all AI checks with their categories
    #             stmt_data = select(Invoice).where(Invoice.customer_id == user_id, Invoice.is_deleted == False)
    #             result_data = await self.db.execute(stmt_data)
    #             invoice_data = result_data.scalars().all()
    #             formatted_data = [json.loads(InvoiceData.model_validate(invoice).model_dump_json()) for invoice in
    #                               invoice_data]
    #
    #         api_response = {
    #             "data": formatted_data,
    #             "error": "",
    #             "message": invoice_details_fetched,
    #             "status": True,
    #         }
    #         return JSONResponse(content=api_response, status_code=200)
    #
    #     except Exception as e:
    #         logger.exception(f"Error retrieving Invoices: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {"invoice_data": []},
    #             "error": str(e),
    #             "message": invoice_details_fetch_failed,
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)
    #
    # async def get_invoice_pdf_operation(self, data, user_id):
    #     try:
    #         stmt_data = select(Invoice).where(Invoice.id == str(data.id), Invoice.customer_id == user_id,
    #                                           Invoice.is_deleted == False)
    #         result_data = await self.db.execute(stmt_data)
    #         invoice_data = result_data.scalars().first()
    #         if not invoice_data:
    #             api_response = {
    #                 "data": {},
    #                 "error": invoice_not_found,
    #                 "message": invoice_not_found,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=404)
    #         invoice_pdf, success_flag = await ChargebeeOperations().get_invoice_pdf(str(data.id))
    #         if success_flag:
    #             api_response = {
    #                 "data": invoice_pdf,
    #                 "error": "",
    #                 "message": invoice_details_fetched,
    #                 "status": True,
    #             }
    #             return JSONResponse(content=api_response, status_code=200)
    #         else:
    #             api_response = {
    #                 "data": {},
    #                 "error": invoice_details_fetch_failed,
    #                 "message": invoice_details_fetch_failed,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=400)
    #     except Exception as e:
    #         logger.exception(f"Error retrieving Invoice pdf: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {"invoice_data": []},
    #             "error": str(e),
    #             "message": invoice_details_fetch_failed,
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)

    async def update_basic_profile_details(self, data, user_id, profile_img):
        try:
            # Retrieve all AI checks with their categories
            stmt_user = select(User).where(User.id == user_id, User.is_deleted == False)
            result_user = await self.db.execute(stmt_user)
            user = result_user.scalars().first()
            # Format data for response
            if profile_img:
                filename = profile_img.filename
                extension = os.path.splitext(filename)[1]
                profile_file_name = f"profile_img_{int(time.time())}{extension}"
                await AWSFileOperations().s3_file_upload(profile_img, user_id, profile_file_name)
                user.profile_picture = profile_file_name
            user.first_name = data.first_name
            user.last_name = data.last_name
            user.organization = data.organization
            organization_address = {
                'organization_address_street1': data.organization_address_street1,
                'organization_address_street2': data.organization_address_street2,
                'organization_address_city': data.organization_address_city,
                'organization_address_state': data.organization_address_state,
                'organization_address_zip_code': data.organization_address_zip_code
            }
            user.organization_address = json.dumps(organization_address)
            user.organization_contact = data.organization_contact
            await self.db.commit()

            api_response = {
                "data": {},
                "error": "",
                "message": profile_updated_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error updating basic profile details: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": profile_updated_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    # async def update_billing_address_details(self, data, user_id):
    #     try:
    #         # Retrieve all AI checks with their categories
    #         stmt_billing = select(BillingAddress).where(BillingAddress.user_id == user_id,
    #                                                     BillingAddress.is_deleted == False)
    #         result_billing = await self.db.execute(stmt_billing)
    #         billing_address = result_billing.scalars().first()
    #         new_billing_address = data.__dict__
    #         new_billing_address.update({'user_id': user_id})
    #         await Dbcrud().update(existing_data=billing_address, update_data=new_billing_address, db=self.db)
    #         billing_address_update = await ChargebeeOperations().update_chargebee_billing_info(data)
    #         if billing_address_update:
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": billing_address_update_success,
    #                 "status": True,
    #             }
    #             return JSONResponse(content=api_response, status_code=200)
    #         else:
    #             await self.db.rollback()
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": billing_address_update_failed,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=400)
    #     except Exception as e:
    #         logger.exception(f"Error updating billing address details: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {},
    #             "error": str(e),
    #             "message": billing_address_update_failed,
    #             "status": False,
    #         }
    #         await self.db.rollback()
    #         return JSONResponse(content=api_response, status_code=500)
    #
    # async def cancel_subscription(self, user_id):
    #     try:
    #         # Retrieve all AI checks with their categories
    #         stmt_subscription = select(Subscription).where(Subscription.customer_id == user_id,
    #                                                        Subscription.is_deleted == False,
    #                                                        Subscription.status == "active")
    #         result_subscription = await self.db.execute(stmt_subscription)
    #         subscription = result_subscription.scalars().first()
    #         if subscription:
    #             # Format data for response
    #             subscription.status = "inactive"
    #             await self.db.commit()
    #             # Cancel subscription on term end
    #             cancel_subscription = await ChargebeeOperations().cancel_chargebee_subscription(subscription.id)
    #             if cancel_subscription:
    #                 api_response = {
    #                     "data": {},
    #                     "error": "",
    #                     "message": subscription_cancel_success,
    #                     "status": True,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=200)
    #             else:
    #                 await self.db.rollback()
    #                 api_response = {
    #                     "data": {},
    #                     "error": "",
    #                     "message": subscription_cancel_failed,
    #                     "status": False,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=200)
    #
    #         else:
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": subscription_not_found,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=400)
    #
    #     except Exception as e:
    #         await self.db.rollback()
    #         logger.exception(f"Error cancelling subscription details: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {},
    #             "error": str(e),
    #             "message": subscription_cancel_failed,
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)
    #
    # async def add_new_card(self, data, user_id):
    #     try:
    #         new_card_data, card_added_flag = await ChargebeeOperations().add_chargebee_payment_method_card(user_id,
    #                                                                                                        data)
    #         if card_added_flag:
    #             payment_source = new_card_data.payment_source.__dict__
    #             payment_source.update({'user_id': user_id})
    #             fields = {column.name for column in PaymentMethod.__table__.columns}
    #             filtered_data = {key: value for key, value in payment_source.items() if key in fields}
    #             db_obj = PaymentMethod(**filtered_data)
    #             self.db.add(db_obj)
    #             await self.db.commit()
    #
    #             card = new_card_data.payment_source.card.__dict__
    #             card.update({'user_id': user_id,
    #                          'payment_source_id': payment_source.get('id')})
    #             fields = {column.name for column in Card.__table__.columns}
    #             filtered_data = {key: value for key, value in card.items() if key in fields}
    #             db_obj = Card(**filtered_data)
    #             self.db.add(db_obj)
    #             await self.db.commit()
    #
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": new_card_added,
    #                 "status": True,
    #             }
    #             return JSONResponse(content=api_response, status_code=200)
    #         else:
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": new_card_addition_failed,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=400)
    #
    #     except Exception as e:
    #         await self.db.rollback()
    #         logger.exception(f"Error adding new card: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {},
    #             "error": str(e),
    #             "message": new_card_addition_failed,
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)
    #
    # async def delete_card(self, data, user_id):
    #     try:
    #         stmt_card = select(Card).where(Card.user_id == user_id,
    #                                        Card.id == data.id,
    #                                        Card.is_deleted == False)
    #         result_card = await self.db.execute(stmt_card)
    #         card_obj = result_card.scalars().first()
    #         if card_obj:
    #             _, card_delete_flag = await ChargebeeOperations().remove_payment_source_card(card_obj.payment_source_id)
    #             if card_delete_flag:
    #                 card_obj.is_deleted = True
    #                 await self.db.commit()
    #                 api_response = {
    #                     "data": {},
    #                     "error": "",
    #                     "message": new_card_removed,
    #                     "status": True,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=200)
    #             else:
    #                 api_response = {
    #                     "data": {},
    #                     "error": "",
    #                     "message": new_card_remove_failed,
    #                     "status": False,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=400)
    #         else:
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": card_not_found,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=404)
    #     except Exception as e:
    #         await self.db.rollback()
    #         logger.exception(f"Error removing card: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {},
    #             "error": str(e),
    #             "message": new_card_remove_failed,
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)
    #
    # async def change_primary_card(self, data, user_id):
    #     try:
    #         stmt_card = select(Card).where(Card.user_id == user_id,
    #                                        Card.id == int(data.id),
    #                                        Card.is_deleted == False)
    #         result_card = await self.db.execute(stmt_card)
    #         card_obj = result_card.scalars().first()
    #         if card_obj:
    #             _, card_delete_flag = await ChargebeeOperations().chargebee_update_payment_method(user_id,
    #                                                                                               card_obj.payment_source_id)
    #             if card_delete_flag:
    #                 api_response = {
    #                     "data": {},
    #                     "error": "",
    #                     "message": "Primary card changed",
    #                     "status": True,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=200)
    #             else:
    #                 api_response = {
    #                     "data": {},
    #                     "error": "",
    #                     "message": "Failed to change Primary card",
    #                     "status": False,
    #                 }
    #                 return JSONResponse(content=api_response, status_code=400)
    #         else:
    #             api_response = {
    #                 "data": {},
    #                 "error": "",
    #                 "message": card_not_found,
    #                 "status": False,
    #             }
    #             return JSONResponse(content=api_response, status_code=404)
    #     except Exception as e:
    #         await self.db.rollback()
    #         logger.exception(f"Error changing primary payment method: {str(e)}", exc_info=True)
    #         api_response = {
    #             "data": {},
    #             "error": str(e),
    #             "message": "Failed to change Primary card",
    #             "status": False,
    #         }
    #         return JSONResponse(content=api_response, status_code=500)

    async def reset_password_operation(self, data, user_id: str):
        try:
            stmt_user = select(User).where(User.id == user_id, User.is_deleted == False)
            result_data = await self.db.execute(stmt_user)
            user_obj = result_data.scalars().first()
            if not user_obj:
                api_response = {
                    "data": {},
                    "error": user_not_found,
                    "message": user_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            else:
                if verify_password(data.old_password, user_obj.hashed_password):
                    if data.old_password != data.new_password:
                        hashed_password = get_password_hash(data.new_password)
                        user_obj.hashed_password = hashed_password
                        user_obj.access_token = ""
                        user_obj.refresh_token = ""
                        await self.db.commit()
                        api_response = {
                            "data": {},
                            "error": password_updated,
                            "message": password_updated,
                            "status": True,
                        }
                        return JSONResponse(content=api_response, status_code=200)
                    else:
                        api_response = {
                            "data": {},
                            "error": password_invalid,
                            "message": password_invalid,
                            "status": False,
                        }
                        return JSONResponse(content=api_response, status_code=400)
                else:
                    api_response = {
                        "data": {},
                        "error": password_incorrect,
                        "message": password_incorrect,
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=400)
        except Exception as e:
            logger.exception(msg=f"Input: {str(locals())} \nException : {str(e)}", exc_info=True)
            error = ExceptionHandling(e=str(e),
                                      function_name='AuthOperations.reset_password_operation').exception_handling()
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)
