import json

from fastapi import Depends
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app import logger
from app.database import get_db
from app.database.models import Api<PERSON><PERSON>, StaticPages
from app.schemas.cms import APIKeysResponseSchema, ViewEditStaticPagesSchema
from app.utils.constants import api_key_not_configure, api_keys_fetch_success, api_keys_fetch_fail, api_keys_updated, \
    exception_occurred, static_page_not_found, static_page_fetched_successfully, static_page_updated_successfully


class CMSOperations:
    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def api_keys(self):
        """ Retrieves all API keys from the database.

        This asynchronous method fetches all API key records from the database and processes them into a structured JSON response. Each API key is validated and converted into JSON format using `APIKeysResponseSchema`. The function then returns a response indicating whether API keys were found or not. If an exception occurs, it logs the error and returns an error response.

        **Args**:
            None

        **Returns**:
            JSONResponse: A JSON response containing API key data, an error message (if applicable), a status flag, and a success/failure message.

        **Raises**:
            Exception: If an error occurs during the API key retrieval process, the exception is logged, and a failure response is returned.
        """

        try:
            stmt = select(ApiKey)
            result = await self.db.execute(stmt)
            api_keys = result.scalars().all()
            api_keys_data = [json.loads(APIKeysResponseSchema.model_validate(api_key).model_dump_json()) for api_key in
                             api_keys]
            api_response = {
                "data": api_keys_data,
                "error": "",
                "message": api_key_not_configure if not api_keys_data else api_keys_fetch_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error deleting AI Check: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": api_keys_fetch_fail,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def edit_keys(self, data):
        """ Updates an existing API key record in the database.

        This asynchronous method updates an API key based on the provided data. It first retrieves the API key record using the `id` from the input data, ensuring that it has not been marked as deleted. If the API key exists, its `api_key` value and `is_active` status are updated, and the changes are committed to the database. The function then returns a JSON response indicating whether the update was successful. If no matching record is found, an error response is returned. If an exception occurs, it logs the error and returns a failure response.

        **Args**:
            data: An object containing the API key details to be updated. Must include the `id`, `api_key`, and `is_active` status.

        **Returns**:
            JSONResponse: A JSON response containing the update status, an error message (if applicable), and a success/failure message.

        **Raises**:
            Exception: If an error occurs during the API key update process, the exception is logged, and a failure response is returned.
        """

        try:
            stmt = select(ApiKey).where(ApiKey.id == data.id, ApiKey.is_deleted == False)
            result = await self.db.execute(stmt)
            api_key = result.scalar_one_or_none()
            if api_key:
                api_key.api_key = data.api_key
                api_key.is_active = data.is_active
                api_key.available_models=data.available_models
                await self.db.commit()
                api_response = {
                    "data": {},
                    "error": "",
                    "message": api_keys_updated,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:
                api_response = {
                    "data": {},
                    "error": api_key_not_configure,
                    "message": api_key_not_configure,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)
        except Exception as e:
            logger.exception(f"Error deleting AI Check: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": api_keys_fetch_fail,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)


class StaticPagesOperations:
    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def edit_static_page(self, data):
        try:
            stmt_static_pages = select(StaticPages).where(StaticPages.title==data.title, StaticPages.is_deleted==False)
            static_page_result = await self.db.execute(stmt_static_pages)
            static_page = static_page_result.scalar_one_or_none()
            if static_page:
                static_page.content = data.content
                await self.db.commit()
                api_response = {
                    "data": {},
                    "error": static_page_updated_successfully,
                    "message": static_page_updated_successfully,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:
                api_response = {
                    "data": {},
                    "error": static_page_not_found,
                    "message": static_page_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)

        except Exception as e:
            logger.exception(f"Error updating static page details: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def get_static_page(self, data):
        try:
            if data and data.title:
                stmt_static_pages = select(StaticPages).where(StaticPages.title == data.title,
                                                              StaticPages.is_deleted == False)
                static_page_result = await self.db.execute(stmt_static_pages)
                static_pages = static_page_result.scalar_one_or_none()
                if static_pages:
                    static_pages_data = json.loads(ViewEditStaticPagesSchema.model_validate(static_pages).model_dump_json())
                else:
                    api_response = {
                        "data": {},
                        "error": static_page_not_found,
                        "message": static_page_not_found,
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=404)
            else:
                stmt_static_pages = select(StaticPages).where(StaticPages.is_deleted == False)
                static_page_result = await self.db.execute(stmt_static_pages)
                static_pages = static_page_result.scalars().all()
                static_pages_data = [json.loads(ViewEditStaticPagesSchema.model_validate(static_page).model_dump_json()) for static_page in
                                     static_pages]
            api_response = {
                "data": static_pages_data,
                "error": static_page_fetched_successfully,
                "message": static_page_fetched_successfully,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error getting static page data: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)


