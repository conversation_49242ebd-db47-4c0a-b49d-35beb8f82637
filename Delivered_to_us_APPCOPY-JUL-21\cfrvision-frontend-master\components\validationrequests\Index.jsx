"use client";
import TableWithPagination from "@/components/common/TableWithPagination/page";
import React, { useEffect, useState } from "react";
import AddNewValidationRequestModal from "./AddNewValidationRequestModal";
import { Icon } from "@iconify/react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";
import DeleteFileModal from "../common/DeleteModal";
import ViewValidationRequestModal from "./ViewValidationRequestModal";
import moment from "moment";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const getCurrentDate = () => {
  const currentDate = new Date();
  const day = currentDate.getDate();
  const month = currentDate.getMonth() + 1;
  const year = currentDate.getFullYear();

  return `${year}-${month}-${day}`;
};

const ValidationRequests = () => {
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const [validationRequests, setValidationRequests] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [aiCheckList, setAiCheckList] = useState({});
  const [searchText, setSearchText] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState();
  const [viewModal, setViewModal] = useState(false);
  const [viewData, setViewData] = useState({});
  const [modelList, setModelList] = useState({});
  const [dataFetched, setDataFetched] = useState(false);
  const [callApi, setCallApi] = useState(false);
  const router = useRouter();

  const { contextHolder, showToast } = Toaster();

  const updateRequestList = (data) => {
    const newRequest = {
      id: Date.now().toString().slice(-3),
      fileName: data.selectedImage,
      status: "Complete",
      createdDate: getCurrentDate(),
    };
    setValidationRequests([...validationRequests, newRequest]);
  };

  const handleViewClick = async (id) => {
    try {
      const requestBody = {
        filters: [],
        id,
        page_no: 1,
        page_size: 10,
        is_pagination: true,
        search_text: "",
      };
      const response = await axiosInstance.post(
        "/v1/llm_check_request/get-llm-check-request",
        requestBody
      );
      setViewData(response.data.data);
      setViewModal(true);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    }
  };

  const closeViewModal = () => {
    setViewModal(false);
    setViewData({});
  };

  const runLlmCheck = async (id) => {
    try {
      const response = await axiosInstance.post(
        "/v1/llm_check_request/run-llm-check-request",
        { id }
      );
      console.log("response: ", response);
      showToast({
        type: "success",
        message: response?.data?.message,
      });
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      headerName: "FILE NAME",
      valueGetter: (p) => p.data.file_name,
      flex: 2,
    },
    {
      headerName: "STATUS",
      valueGetter: (p) => p.data.status,
      flex: 2,
      cellRenderer: (params) => {
        const status = params.value;
        return <div className="badge badge-complete">{status}</div>;
      },
    },
    {
      field: "CREATED DATE",
      valueFormatter: (p) => p.data.created_at,
      flex: 1,
      cellRenderer: (params) => {
        return (
          <p>
            {moment.unix(params.data.created_at).format("MM/DD/YYYY HH:MM")}
          </p>
        );
      },
    },
    {
      headerName: "ACTIONS",
      field: "actions",
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="mdi-eye"
            width={24}
            height={24}
            cursor={"pointer"}
            onClick={() => handleViewClick(params.data.request_id)}
          />
          <Icon
            icon="mdi:trash-outline"
            width={"24"}
            height="24"
            color="red"
            cursor={"pointer"}
            onClick={() => {
              setShowDeleteModal(true);
              setDeleteId(params.data.request_id);
            }}
          />
          <Icon
            icon="mdi:play"
            width={24}
            height={24}
            color="blue"
            cursor={"pointer"}
            onClick={() => runLlmCheck(params.data.request_id)}
          />
        </div>
      ),
    },
  ];

  const fetchValidationRequests = async (
    page_no = 1,
    page_size = 10,
    search = searchText || ""
  ) => {
    setDataFetched(true);
    try {
      const requestBody = {
        filters: [],
        // id: 1,
        page_no,
        page_size,
        is_pagination: true,
        search_text: search,
      };
      const response = await axiosInstance.post(
        "/v1/llm_check_request/get-llm-check-request",
        requestBody
      );
      setValidationRequests(response.data.data);
      setDataFetched(false);
      if (!callApi) {
        setCallApi(true);
        fetchFileList();
        fetchCheckCategoryList();
        fetchAiCheckList();
        fetchModelList();
      }
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setDataFetched(false);
    }
  };

  const fetchFileList = async () => {
    setLoading(true);
    try {
      const requestBody = {
        // id: 1,
        page_no: 1,
        page_size: 10,
        is_pagination: false,
        search_text: "",
      };
      const response = await axiosInstance.post(
        "/v1/file/my-files",
        requestBody
      );
      setFileList(response.data.data);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCheckCategoryList = async () => {
    setLoading(true);
    try {
      const requestBody = {
        id: 1,
        page_no: 1,
        page_size: 10,
        is_pagination: false,
        search_text: "",
      };
      const response = await axiosInstance.post(
        "/v1/llm_check_request/get-ai-check-category",
        requestBody
      );
      setCategoryList(response.data.data);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAiCheckList = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post(
        "/v1/llm_check_request/get-ai-checks"
      );
      const aiCheckList = response.data.data?.checks;
      let checkListObj = {};

      aiCheckList?.forEach((item) => {
        checkListObj[item?.category_id] = checkListObj[item?.category_id]
          ?.length
          ? [...checkListObj[item?.category_id], item]
          : [item];
      });

      setAiCheckList(checkListObj);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchModelList = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post(
        "/v1/llm_check_request/available-models"
      );
      setModelList(response?.data?.data);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchValidationRequests();

    // Set interval for every 30 sec
    const interval = setInterval(fetchValidationRequests, 30000);

    // Cleanup on unmount
    return () => clearInterval(interval);
  }, [callApi]);

  const handlePaginationChange = (page_no, page_size) => {
    fetchValidationRequests(page_no, page_size);
  };

  const handleSearchChange = (value) => {
    setSearchText(value);
    fetchValidationRequests(1, 10, value);
  };

  const onValidationSubmit = (successMsg) => {
    showToast({
      type: "success",
      message: successMsg,
    });
  };

  const onValidationRequestDeleted = (deleteMsg) => {
    showToast({
      type: "success",
      message: deleteMsg,
    });
  };

  const onValidationRequestDeletedError = (deleteMsg) => {
    showToast({
      type: "error",
      message: deleteMsg,
    });
  };

  return (
    <>
      {contextHolder}
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="d-flex align-items-center justify-content-between ">
              <h5 className="page-title text-center mb-0">
                My Validation Request
              </h5>
              <button
                className="btn btn-mod btn-color btn-small btn-circle btn-hover-anim mb-xs-10"
                id="upload-button"
                onClick={() => setShow(true)}
                disabled={loading}
              >
                <span>Add New Validation Request</span>
              </button>
              <input type="file" style={{ display: "none" }} multiple />
            </div>
            <TableWithPagination
              data={validationRequests}
              columns={columns}
              onPaginationChange={handlePaginationChange}
              loading={validationRequests?.length ? false : dataFetched}
              onSearchChange={handleSearchChange}
            />
          </ParallaxContainer>
        </section>
      </main>
      {show && (
        <AddNewValidationRequestModal
          show={show}
          setShow={setShow}
          updateRequestList={updateRequestList}
          fileList={fileList}
          categoryList={categoryList}
          aiCheckList={aiCheckList}
          fetchValidationRequests={fetchValidationRequests}
          onValidationSubmit={onValidationSubmit}
          modelList={modelList}
        />
      )}
      {showDeleteModal && (
        <DeleteFileModal
          show={showDeleteModal}
          setShow={setShowDeleteModal}
          refetch={fetchValidationRequests}
          id={deleteId}
          apiUrl="/v1/llm_check_request/delete-llm-check-request"
          name="Validation Request"
          onDeleted={onValidationRequestDeleted}
          onDeletedError={onValidationRequestDeletedError}
        />
      )}
      {viewModal && (
        <ViewValidationRequestModal
          show={viewModal}
          setShow={closeViewModal}
          data={viewData}
        />
      )}
    </>
  );
};

export default ValidationRequests;
