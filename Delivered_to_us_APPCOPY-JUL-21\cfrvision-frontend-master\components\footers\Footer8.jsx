"use client";
import React from "react";
import Image from "next/image";
import FooterSocials from "./FooterSocials";
import { footerLinks, navigationLinks } from "@/data/footer";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";

export default function Footer8() {
  const { contextHolder, showToast } = Toaster();
  const scrollToTop = (event) => {
    event.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth", // Linear easing replacement
    });
  };

  const handleLegalClick = async (title = "") => {
    try {
      const response = await axiosInstance.post("/v1/cms/static-pages", {
        title,
      });
      const newWindow = window.open();
      if (newWindow) {
        newWindow.document.write(
          response?.data?.data?.content.replaceAll('"""', "")
        );
        newWindow.document.close();
      }
    } catch (error) {
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      {/* End Background Shape */}
      <div className="container position-relative">
        <div className="row pb-120 pb-sm-80 pb-xs-50">
          <div className="col-md-4 col-lg-3 text-gray mb-sm-50">
            <div className="mb-30">
              <Image
                src="/assets/images/demo-slick/logo.svg"
                alt="Your Company Logo"
                width={106}
                height={36}
                className="light-mode-logo"
              />
            </div>
            <p className="text-secondary fw-normal">
              Revolutionizing public sector financial reporting with AI-powered
              solutions.
            </p>
            <div className="clearlinks text-color-primary fw-medium">
              <strong>T.</strong>
              <a
                href="tel:+17864873608"
                className="text-color-primary fw-medium"
              >
                +1**************
              </a>
            </div>
            <div className="clearlinks text-color-primary fw-medium">
              <strong>E.</strong>
              <a
                href="mailto:<EMAIL>"
                className="text-color-primary fw-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
          <div className="col-md-7 offset-md-1 offset-lg-2">
            <div className="row mt-n30">
              {/* Footer Widget */}
              <div className="col-sm-4 mt-30">
                <h3 className="fw-title">Company</h3>
                <ul className="fw-menu clearlist local-scroll">
                  {navigationLinks.map((elm, i) => (
                    <li key={i}>
                      <a href={elm.href}
                        className="text-secondary fw-normal mb-0"
                        style={{ cursor: "pointer" }}
                      >
                        {elm.text}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
              {/* End Footer Widget */}
              {/* Footer Widget */}
              <div className="col-sm-4 mt-30">
                <h3 className="fw-title">Social Media</h3>
                <ul className="fw-menu clearlist">
                  <FooterSocials />
                </ul>
              </div>
              {/* End Footer Widget */}
              {/* Footer Widget */}
              <div className="col-sm-4 mt-30">
                <h3 className="fw-title">Legal &amp; Press</h3>
                <ul className="fw-menu clearlist">
                  {footerLinks.map((elm, i) => (
                    <li key={i}>
                      <a
                        href={elm.path}
                        className="text-secondary fw-normal"
                        target="_blank"
                      >
                        {elm.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
              {/* End Footer Widget */}
            </div>
          </div>
        </div>
        {/* Footer Text */}
        <div className="row text-gray">
          <div className="col-md-4 col-lg-6 ">
            <b className="text-color-primary fw-medium">
              © Madia Solutions {new Date().getFullYear()}. All Rights Reserved
            </b>
          </div>
          <div className="col-md-4 offset-md-1 offset-lg-2 clearfix">
            {/* Back to Top Link */}
            <div
              className="local-scroll float-end mt-n20 mt-sm-10"
              onClick={scrollToTop}
            >
              <a href="#top" className="link-to-top color-light">
                <i className="mi-arrow-up size-24" />
                <span className="visually-hidden">Scroll to top</span>
              </a>
            </div>
            {/* End Back to Top Link */}
          </div>
        </div>
        {/* End Footer Text */}
      </div>
    </>
  );
}
