from typing import Optional

from pydantic import BaseModel, Field

from app.schemas.common import ObjectID


# AICheckSchemaCreate
class AICheckSchemaCreate(BaseModel):
    check_display_number: Optional[int] = Field(
        ...,
        description="Display order number for the check.",
        examples=[1, 2, 3]
    )
    check_name: str = Field(
        ...,
        description="Short name of the AI check.",
        examples=["Name Validation", "Data Integrity Check"]
    )
    check_prompt: Optional[str] = Field(
        "",
        description="AI prompt to be used for the check.",
        examples=["Please validate the entered name format."]
    )
    category_id: Optional[int] = Field(
        ...,
        description="Category ID associated with this check.",
        examples=[101, 202]
    )
    description: Optional[str] = Field(
        ...,
        description="Detailed description or extended name of the AI check.",
        examples=["Validate Name and Format Consistency"]
    )

    check_extended_description: Optional[str] = Field(
        ...,
        description="Comprehensive explanation for administrators about the purpose and usage of the AI check.",
        examples=[
            "This check ensures that the user input name adheres to the predefined format and is consistent with the system requirements."]
    )

    check_relevant_sections: Optional[str] = Field(
        ...,
        description="List of sections or areas in the system where this AI check is applicable.",
        examples=["User Profile, Account Settings"]
    )
    check_expected_result_format: Optional[str] = Field(
        ...,
        description="Expected result format for the AI check's output.",
        examples=["Fund Name Reported Operating Income (Statement) Reported Operating Income (Cash Flows) Result",
                   "Proprietary Fund A <value> <value> Match / Discrepancy Flag",
                   "Proprietary Fund B <value> <value> Match / Discrepancy Flag"]
    )
    is_active: Optional[bool] = Field(
        True,
        description="Indicates whether the check is active.",
        examples=[True, False]
    )


# CheckID
class CheckID(BaseModel, ObjectID):
    pass


# AICheckSchemaUpdate
class AICheckSchemaUpdate(ObjectID,BaseModel):
    check_display_number: Optional[int] = Field(
        None,
        description="Display order number for the check.",
        examples=[1, 2, 3]
    )
    check_name: str = Field(
        ...,
        description="Short name of the AI check.",
        examples=["Name Validation", "Data Integrity Check"]
    )
    check_prompt: Optional[str] = Field(
        "",
        description="AI prompt to be used for the check.",
        examples=["Please validate the entered name format."]
    )
    is_active: Optional[bool] = Field(
        True,
        description="Indicates whether the check is active.",
        examples=[True, False]
    )
    category_id: Optional[int] = Field(
        None,
        description="Category ID associated with this check.",
        examples=[101, 202]
    )
    description: Optional[str] = Field(
        "",
        description="Detailed description or extended name of the AI check.",
        examples=["Validate Name and Format Consistency"]
    )

    check_extended_description: Optional[str] = Field(
        "",
        description="Comprehensive explanation for administrators about the purpose and usage of the AI check.",
        examples=[
            "This check ensures that the user input name adheres to the predefined format and is consistent with the system requirements."]
    )

    check_relevant_sections: Optional[str] = Field(
        "",
        description="List of sections or areas in the system where this AI check is applicable.",
        examples=["User Profile, Account Settings"]
    )

    check_expected_result_format: Optional[str] = Field(
        ...,
        description="Expected result format for the AI check's output.",
        examples=["Fund Name Reported Operating Income (Statement) Reported Operating Income (Cash Flows) Result, "
                  "Proprietary Fund A <value> <value> Match / Discrepancy Flag, "
                  "Proprietary Fund B <value> <value> Match / Discrepancy Flag"]
    )