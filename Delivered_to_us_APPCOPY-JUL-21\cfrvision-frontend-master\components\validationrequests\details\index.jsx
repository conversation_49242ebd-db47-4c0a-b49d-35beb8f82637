"use client";
import React, { useState } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { Card, Table, Button, Collapse } from "antd";
import { Row, Col } from "react-bootstrap";

const sampleData = {
  id: 2,
  fileName: "File 2",
  status: "Complete",
  createdDate: "2025-01-02",
  aiChecks: [
    { category: "reconciliation", checks: ["rec_check_1"] },
    { category: "capital_assets", checks: ["cap_check_1", "cap_check_2"] },
    { category: "debt_liabilities", checks: ["debt_check_1"] },
  ],
};

const columns = [
  {
    title: "Category",
    dataIndex: "category",
    key: "category",
  },
  {
    title: "Checks",
    dataIndex: "checks",
    key: "checks",
    render: (checks) => checks.join(", "),
  },
];

const dummyTable = () => (
  <Table
    columns={columns}
    dataSource={sampleData.aiChecks.map((item, index) => ({
      key: index,
      ...item,
    }))}
    pagination={false}
    bordered
  />
);

const items = [
  {
    key: "1",
    label: "Statement of Net Position",
    children: (<>
      <p>
        Verify that total assets, total liabilities, and net position are
        internally consistent within the statement.
      </p>
      {dummyTable()}
    </>),
  },
  {
    key: "2",
    label: "Statement of Activities",
    children: (<>
      <p>
      Cross-check revenue and expense totals with the Statement of Net
      Position.
      </p>
      {dummyTable()}
    </>),
  },
  {
    key: "3",
    label: "Inter-statement Consistency",
    children: (<>
      <p>
        Verify alignment of net position values between the Statement of Net
        Position and the Statement of Activities.
        {dummyTable()}
      </p>
      </>),
  },
  {
    key: "4",
    label: "Consistency with Note Disclosures",
    children: (<>
      <p>
        Cross-check major figures like assets, liabilities, and net position
        with relevant notes.
      </p>
      {dummyTable()}
      </>),
  },
];

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const ValidationRequestDetail = ({ id }) => {
  const router = useRouter();

  const { fileName, status, createdDate, aiChecks } = sampleData;


  return (
    <>
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="container mt-4 primary-card-view">
              <Card
                title={`Validation Request Details - ${fileName}`}
                bordered={false}
                extra={
                  <Button
                    type="primary"
                    onClick={() => router.push("/validation-requests")}
                  >
                    Back to List
                  </Button>
                }
              >
                <Row className="mb-4">
                  <Col md={4}>
                    <strong>File Name:</strong> <span>{fileName}</span>
                  </Col>
                  <Col md={4}>
                    <strong>Status:</strong> <span>{status}</span>
                  </Col>
                  <Col md={4}>
                    <strong>Created Date:</strong> <span>{createdDate}</span>
                  </Col>
                </Row>
                <h5>AI Checks</h5>
                <Collapse items={items}  />;
              </Card>
            </div>
          </ParallaxContainer>
        </section>
      </main>
    </>
  );
};

export default ValidationRequestDetail;
