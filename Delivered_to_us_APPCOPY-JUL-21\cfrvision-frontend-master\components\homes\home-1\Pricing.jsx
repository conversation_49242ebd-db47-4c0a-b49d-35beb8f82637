"use client";
import { pricing2 } from "@/data/pricing";

import Image from "next/image";
import React, { useState } from "react";

export default function Pricing() {
  const [isYearly, setIsYearly] = useState(false);
  return (
    <>
      {/* Nav Tabs */}
      <div className="mb-60 mb-sm-40 text-center">
        <div className="small text-gray mb-10">
          Choose the plan that best fits your organization's needs.
        </div>
        <ul
          className="nav nav-tabs tpl-minimal-tabs animate border-bottom-0"
          role="tablist"
        >
          <li
            onClick={() => setIsYearly(true)}
            className="nav-item"
            role="presentation"
          >
            <a className={`nav-link ${isYearly ? "active" : ""} `}>Annual</a>
          </li>
          <li
            onClick={() => setIsYearly(false)}
            className="nav-item"
            role="presentation"
          >
            <a className={`nav-link ${!isYearly ? "active" : ""} `}>Monthly</a>
          </li>
        </ul>
      </div>
      {/* End Nav Tabs */}
      {/* Tab panes */}
      <div className="tab-content tpl-minimal-tabs-cont position-relative">
        <div className="tab-pane show active" id="tab-annual" role="tabpanel">
          <div className="row mt-n30">
            {pricing2.map((elm, i) => (
              <div
                key={i}
                className="col-md-6 col-lg-4 mt-30 d-flex align-items-stretch"
              >
                <div className="pricing-item">
                  <div className="pricing-item-inner round">
                    <div className="pricing-wrap">
                      <h4 className="pricing-title">{elm.title}</h4>
                      <div className="pricing-num">
                        <sup>$</sup>
                        {isYearly ? elm.price : Math.floor(elm.price / 10)}
                      </div>
                      <div className="pr-per">
                        {isYearly ? "per year" : "per month"}
                      </div>
                      <div className="pricing-features">
                        <ul className="pr-list">
                          {elm.features?.map((elm, i) => (
                            <li style={{ display: "flex" }} key={i}>
                              <i className="mi-check features-list-icon" />{" "}
                              {elm}
                            </li>
                          ))}
                          {elm.disabledFeatures?.map((elm, i) => (
                            <li key={i} className="opacity-055">
                              <i className="mi-close" /> {elm}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div className="mt-auto local-scroll">
                        <a
                          href={isYearly ? elm.yearlyLink : elm.monthlyLink}
                          target="_blank"
                          className="btn btn-mod btn-medium btn-color btn-circle btn-hover-anim btn-full"
                        >
                          <span>Buy {elm.title}</span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* End Tab panes */}
    </>
  );
}
