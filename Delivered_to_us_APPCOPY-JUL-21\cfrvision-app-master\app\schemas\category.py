from typing import Text, Optional

from pydantic import BaseModel, Field
from pydantic.types import constr, conint

from app.schemas.common import ObjectID


class CheckCategoryID(ObjectID, BaseModel):
    pass


# CheckCategories
class CheckCategories(BaseModel):
    category_id: int = Field(
        ...,
        description="Unique identifier of the category.",
        examples=[1, 101]
    )
    category_name: constr(min_length=1, max_length=100) = Field(
        ...,
        description="Name of the category, with a minimum length of 1 and a maximum length of 100.",
        examples=["General Checks", "Data Validation"]
    )
    category_description: Text = Field(
        ...,
        description="Detailed description of the category.",
        examples=["This category includes general checks for data validation."]
    )
    category_order: conint(ge=1) = Field(
        ...,
        description="Order number for the category, must be greater than or equal to 1.",
        examples=[1, 2, 3]
    )
    is_active: bool = Field(
        ...,
        description="Indicates whether the category is active.",
        examples=[True, False]
    )

    class Config:
        from_attributes = True
        orm_mode = True


# CheckCategories
class CheckCategoriesEdit(BaseModel):
    category_id: Optional[int] = Field(
        ...,
        description="Unique identifier of the category.",
        examples=[1, 101]
    )
    category_name: constr(min_length=1, max_length=100) = Field(
        '',
        description="Name of the category, with a minimum length of 1 and a maximum length of 100.",
        examples=["General Checks", "Data Validation"]
    )
    category_description: Text = Field(
        "",
        description="Detailed description of the category.",
        examples=["This category includes general checks for data validation."]
    )
    category_order: conint(ge=1) = Field(
        "",
        description="Order number for the category, must be greater than or equal to 1.",
        examples=[1, 2, 3]
    )
    is_active: bool = Field(
        "",
        description="Indicates whether the category is active.",
        examples=[True, False]
    )


# CheckCategories
class CheckCategoriesCreate(BaseModel):
    category_name: constr(min_length=1, max_length=100) = Field(
        ...,
        description="Name of the category, with a minimum length of 1 and a maximum length of 100.",
        examples=["General Checks", "Data Validation"]
    )
    category_description: Text = Field(
        ...,
        description="Detailed description of the category.",
        examples=["This category includes general checks for data validation."]
    )
    category_order: conint(ge=1) = Field(
        ...,
        description="Order number for the category, must be greater than or equal to 1.",
        examples=[1, 2, 3]
    )
    is_active: bool = Field(
        ...,
        description="Indicates whether the category is active.",
        examples=[True, False]
    )
