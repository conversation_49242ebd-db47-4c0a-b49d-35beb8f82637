import { features5 } from "@/data/features";
import React from "react";

export const metadata = {
  title: "CFR Vision",
  description: "CFR Vision",
};

export default function CFRVision() {
  return (
    <section className="page-section" id="crfvision">
      <div className="container position-relative">
        <div className="row mb-50 mb-sm-50">
          <div className="col-md-10 offset-md-1 col-lg-8 offset-lg-2 col-xl-6 offset-xl-3 text-center">
            <h2 className="section-caption-slick mb-30 mb-sm-20">
              Our Process
            </h2>
            <h3 className="section-title mb-50 mb-sm-30">
              <span className="wow charsAnimIn" data-splitting="chars">
                How CFRVision Works
              </span>
            </h3>
            <p className="text-gray mb-0">
              Designed with public sector entities in mind, CFRVision automates
              the most tedious aspects of ACFR preparation.
            </p>
          </div>
        </div>
        <div className="row mt-n30">
          {features5.map((elm, i) => (
            <div
              key={i}
              className="col-sm-6 col-xl-3 d-flex align-items-stretch"
            >
              <div className="alt-features-item box-shadow text-center mt-30">
                <div className="alt-features-icon mb-10 color-primary-1 mb-50">
                  <img
                    src={elm.svgPath}
                    alt="Not Found"
                    height={50}
                    width={50}
                  />
                  {/* <svg
                                        width={24}
                                        height={24}
                                        viewBox="0 0 24 24"
                                        fill="currentColor"
                                        aria-hidden="true"
                                        focusable="false"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fillRule="evenodd"
                                        clipRule="evenodd"
                                    >
                                        <path d={elm.svgPath} />
                                    </svg> */}
                </div>
                <h4 className="alt-features-title fw-bold">{elm.title}</h4>
                <div className="alt-features-descr">{elm.description}</div>
              </div>
            </div>
          ))}
        </div>
        <div className="row d-flex align-items-center justify-content-center mt-50">
          <div className="col-lg-3">
            <a
              href="#contact"
              className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10 mt-50 d-flex align-items-center justify-content-center"
            >
              <span>See it in Action</span>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
