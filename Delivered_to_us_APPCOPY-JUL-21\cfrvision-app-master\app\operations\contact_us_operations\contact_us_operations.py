import json

from fastapi import Depends
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app import logger
from app.database import get_db
from app.database.models import ContactUs
from app.schemas.contact_us import GetContactUsSchema
from app.utils.constants import (add_contact_us, exception_occurred, contact_us_list_fetched_fail,
                                 contact_us_list_fetched_success, contact_us_not_found, contact_us_updated)



class ContactUsOperations:
    def __init__(self,  db: AsyncSession = Depends(get_db)):
        self.db = db

    async def contact_us(self, data):
        try:
            stmt_contacts = select(ContactUs).where(ContactUs.is_deleted == False)
            result_contacts = await self.db.execute(stmt_contacts)
            contacts = result_contacts.scalars().all()
            contact_data = [json.loads(GetContactUsSchema.model_validate(contact).model_dump_json()) for contact in
             contacts]

            api_response = {
                "data": contact_data,
                "error": "",
                "message": contact_us_list_fetched_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error retrieving Contact Us data: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": contact_us_list_fetched_fail,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def create_contact_us(self, data):
        try:
            # Create a new AI Check
            contact_us = ContactUs(
                name=data.name,
                email=data.email,
                organization=data.organization,
                message=data.message,
                status = data.status
            )
            self.db.add(contact_us)
            await self.db.commit()

            api_response = {
                "data": {},
                "error": "",
                "message": add_contact_us,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error adding contact us: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def update_contact_us(self,data):
        try:
            stmt_contact = (await self.db.execute(select(ContactUs).filter(
                ContactUs.id == data.id,
                ContactUs.is_deleted == False))).scalars().first()
            if not stmt_contact:
                api_response = {
                    "data": {},
                    "error": "",
                    "message": contact_us_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)
            stmt_contact.status = data.status
            stmt_contact.notes = data.notes
            await self.db.commit()
            api_response = {
                "data": {},
                "error": "",
                "message": contact_us_updated,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.exception(f"Error updating contact us: {str(e)}", exc_info=True)
            api_response = {
                "data": {},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)