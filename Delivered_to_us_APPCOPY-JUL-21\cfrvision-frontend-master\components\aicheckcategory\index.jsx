"use client";
import TableWithPagination from "@/components/common/TableWithPagination/page";
import { Icon } from "@iconify/react";
import React, { useEffect, useState } from "react";
import AddEditCategoryModal from "./AddEditCategoryModal";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import DeleteCategoryModal from "../common/DeleteModal";
import Toaster from "../common/Toaster";
import { ROLE } from "@/utlis/constant";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const AICheckCategory = () => {
  const [show, setShow] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [categories, setCategories] = useState([]);
  const [categoryData, setCategoryData] = useState(null); // null by default (for add)
  const [loading, setLoading] = useState(false);
  const [isView, setIsView] = useState(false);
  const [searchText, setSearchText] = useState("");
  const { contextHolder, showToast } = Toaster();
  const [role, setRole] = useState(null);

  const columns = [
    {
      headerName: "CATEGORY ORDER",
      valueGetter: (p) => p.data.category_order,
    },
    {
      headerName: "CATEGORY NAME",
      valueGetter: (p) => p.data.category_name,
      flex: 2,
    },
    {
      headerName: "DESCRIPTION",
      valueGetter: (p) => p.data.category_description,
      flex: 8,
    },
    {
      headerName: "STATUS",
      valueGetter: (p) => (p.data.is_active ? "Active" : "Inactive"),
      flex: 2,
      cellRenderer: (params) => {
        const status = params.value;
        return (
          <div
            style={
              {
                // display: "flex", alignItems: "center", gap: "8px",
                // marginTop: "10px",
              }
            }
          >
            {/* <div class="form-check form-switch" style={{ marginTop: "5px" }}>
              <input class="form-check-input" type="checkbox" role="switch" checked={params?.data?.is_active} />
            </div> */}
            <span
              className={`badge badge-${
                status === "Active" ? "active" : "inactive"
              }`}
            >
              {status}
            </span>
          </div>
        );
      },
    },
    {
      headerName: "ACTIONS",
      field: "actions",
      valueGetter: (p) => p.data.category_id,
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="mdi-eye"
            width="24"
            height="24"
            cursor={"pointer"}
            onClick={() => {
              setCategoryData(params.data);
              setShow(true);
              setIsView(true);
            }}
          />
          {role === ROLE.ADMIN && (
            <>
              <Icon
                icon="basil:edit-outline"
                width="24"
                height="24"
                cursor={"pointer"}
                onClick={() => {
                  setCategoryData(params.data); // Set category data for edit
                  setShow(true);
                  setIsView(false);
                }}
              />
              <Icon
                icon="mdi:trash-outline"
                width="24"
                height="24"
                color="red"
                cursor={"pointer"}
                onClick={() => {
                  setShowDeleteModal(true);
                  setDeleteId(params.value);
                }}
              />
            </>
          )}
        </div>
      ),
    },
  ];

  const fetchCategories = async (
    page_no = 1,
    page_size = 10,
    search = searchText || ""
  ) => {
    setLoading(true);
    try {
      const requestBody = {
        id: 1,
        page_no,
        page_size,
        is_pagination: true,
        search_text: search,
      };
      const url =
        localStorage?.getItem("role") === ROLE.USER
          ? "v1/llm_check_request/get-ai-check-category"
          : "v1/cms/category/ai-check-categories";
      const response = await axiosInstance.post(url, requestBody);
      setLoading(false);
      setCategories(response.data.data);
    } catch (error) {
      console.error("Failed to fetch categories:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const roleValue = localStorage?.getItem("role");
    setRole(roleValue);
    fetchCategories();
  }, []);

  const handlePaginationChange = (page_no, page_size) => {
    fetchCategories(page_no, page_size);
  };

  const handleSaveCategory = async (category) => {
    if (category.category_id) {
      // Call the API to update the category
      try {
        const response = await axiosInstance.post(
          "/v1/cms/category/ai-check-categories/edit",
          category
        );
        showToast({
          type: "success",
          message: response.data.message,
        });
        fetchCategories(); // Refetch categories after update
      } catch (error) {
        console.error("Failed to update category:", error);
      }
    } else {
      // Call the API to create the category
      try {
        const response = await axiosInstance.post(
          "/v1/cms/category/ai-check-categories/create",
          category
        );
        showToast({
          type: "success",
          message: response.data.message,
        });
        fetchCategories(); // Refetch categories after creation
      } catch (error) {
        console.error("Failed to create category:", error);
      }
    }
  };

  const handleSearchChange = (value) => {
    setSearchText(value);
    fetchCategories(1, 10, value);
  };

  const onCategoryDeleted = (deleteMsg) => {
    showToast({
      type: "success",
      message: deleteMsg,
    });
  };

  const onCategoryDeletedError = (deleteMsg) => {
    showToast({
      type: "error",
      message: deleteMsg,
    });
  };

  return (
    <>
      {contextHolder}
      <main id="main">
        <section className="container page-section">
          <ParallaxContainer className="page-section parallax-5">
            <div className="d-flex align-items-center justify-content-between ">
              <h5 className="page-title text-center mb-0">
                AI Check Categories
              </h5>
              {role === ROLE.ADMIN && (
                <button
                  className="btn btn-mod btn-color btn-small btn-circle btn-hover-anim mb-xs-10"
                  id="upload-button"
                  onClick={() => {
                    setCategoryData(null); // Set categoryData to null for adding new category
                    setShow(true);
                    setIsView(false);
                  }}
                >
                  <span>Add New Category</span>
                </button>
              )}
            </div>
            <TableWithPagination
              data={categories}
              columns={columns}
              onPaginationChange={handlePaginationChange}
              loading={loading}
              onSearchChange={handleSearchChange}
            />
          </ParallaxContainer>
        </section>
        {show && (
          <AddEditCategoryModal
            show={show}
            setShow={setShow}
            categoryData={categoryData}
            onSave={handleSaveCategory}
            isView={isView}
          />
        )}
        {showDeleteModal && (
          <DeleteCategoryModal
            show={showDeleteModal}
            setShow={setShowDeleteModal}
            refetch={fetchCategories}
            id={deleteId}
            apiUrl="/v1/cms/category/ai-check-categories/delete"
            name="Category"
            onDeleted={onCategoryDeleted}
            onDeletedError={onCategoryDeletedError}
          />
        )}
      </main>
    </>
  );
};

export default AICheckCategory;
