import json
import os
import time
from datetime import datetime

from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>
from fastapi import Depends
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import noload

from app import logger
from app.database import get_db
from app.database.models import File, ExtractedFullText, PdfChunk, ChunkEmbedding, LlmCheckRequest, LlmCheckResponse
from app.operations.common_operations.common_operations import FilePreProcessing
from app.schemas.file import FileSchema
from app.utils.constants import file_upload_failed, file_upload_success, file_delete_failed, file_delete_success, \
    file_not_found, exception_occurred, db_error, llm_check_exists_for_file
from app.utils.s3_operations import AWSFileOperations


class FileOperations:
    def __init__(self, db: AsyncSession = Depends(get_db)):
        self.db = db

    async def upload_file(self, file, data,user_id, background_tasks):
        """
        Endpoint to upload a file and process its metadata, with text extraction in a background thread.
        """

        try:
            fy_end = data.fy_end
            draft_final = data.draft_final
            version = data.version
            comment = data.comment
            original_filename = file.filename
            file_name, file_extension = os.path.splitext(original_filename)  # Splitting name and extension

            # Create a new file name with timestamp
            new_file_name = f"{file_name}_{int(time.time())}{file_extension}"

            file_path = await AWSFileOperations().s3_file_upload(file, user_id, new_file_name)

            # Extract number of pages (if a PDF file)
            try:
                reader = PdfReader(file_path.get("url"))
                pages = len(reader.pages)
            except Exception as e:
                pages = 0
            # Save file metadata to the database
            new_file = File(
                file_name=file.filename,
                file_path=new_file_name,
                fy_end=fy_end,
                draft_final=draft_final,
                version=version,
                user_id=user_id,
                upload_date=datetime.fromtimestamp(int(time.time())).strftime('%Y-%m-%d %H:%M:%S'),
                pages=pages,
                size=file.size,
                status="Processing",
                comment=comment
            )
            self.db.add(new_file)
            await self.db.commit()
            await self.db.refresh(new_file)

            file_id = new_file.id
            background_tasks.add_task(FilePreProcessing(self.db).run_extraction, file_id, file_path.get('filename'), user_id)

            api_response = {
                "data": {"file_id": file_id},
                "error": "",
                "message": file_upload_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            logger.error(f"Error uploading the file: {str(e)}")
            api_response = {
                "data": {},
                "error": str(e),
                "message": file_upload_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def delete_file(self, data):
        """
        Deletes a file and all related records from the database, as well as the file from the file system.
        Input: JSON data with file_id.
        """

        try:
            file_id = data.id
            # Query the file record
            stmt = select(File).where(File.id == file_id).options(noload(File.chunks), noload(File.extracted_full_text), noload(File.llm_check_requests), noload(File.user))
            result = await self.db.execute(stmt)
            file_record = result.scalar_one_or_none()

            # check llm-check-request table for any records related to the file
            stmt_llm_check = select(LlmCheckRequest).where(LlmCheckRequest.document_id == file_id, LlmCheckRequest.is_deleted == False)
            result_llm_check = await self.db.execute(stmt_llm_check)
            llm_check_record = result_llm_check.scalars().first()
            if llm_check_record:
                logger.error(f"File with ID {file_id} not found.")
                api_response = {
                    "data": {"file_id": file_id},
                    "error": llm_check_exists_for_file,
                    "message": file_delete_failed,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=400)


            if not file_record:
                logger.error(f"File with ID {file_id} not found.")
                api_response = {
                    "data": {"file_id": file_id},
                    "error": file_not_found,
                    "message": file_delete_failed,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)

            file_name = file_record.file_name
            file_path = os.path.join('uploads', file_name)
            logger.info(f"Attempting to delete file at path: {file_path}")

            # Delete the file from the file system
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"Successfully deleted file: {file_path}")
                except Exception as e:
                    logger.error(f"Error deleting file from file system: {str(e)}")
                    api_response = {
                        "data": {"file_id": file_id},
                        "error": str(e),
                        "message": file_delete_failed,
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=500)
            else:
                logger.warning(f"File not found at path: {file_path}")

            # Delete related llm check responses
            logger.info(f"Deleting llm check response to file ID {file_id}...")
            delete_llm_check_response = (
                LlmCheckResponse.__table__.delete()
                .where(LlmCheckResponse.request_id.in_(
                    select(LlmCheckRequest.request_id).where(LlmCheckRequest.document_id == file_id)
                ))
            )
            result = await self.db.execute(delete_llm_check_response)
            logger.info(f"Deleted {result.rowcount} llm check response.")

            # Delete related llm check requests
            logger.info(f"Deleting llm check requests to file ID {file_id}...")
            delete_llm_check_requests = (
                LlmCheckRequest.__table__.delete()
                .where(LlmCheckRequest.document_id == file_id)
            )
            result = await self.db.execute(delete_llm_check_requests)
            logger.info(f"Deleted {result.rowcount} llm check requests.")

            # Delete related embeddings from chunk_embeddings
            logger.info(f"Deleting embeddings related to file ID {file_id}...")
            delete_embeddings_stmt = (
                ChunkEmbedding.__table__.delete()
                .where(ChunkEmbedding.chunk_id.in_(
                    select(PdfChunk.id).where(PdfChunk.file_id == file_id)
                ))
            )
            result = await self.db.execute(delete_embeddings_stmt)
            logger.info(f"Deleted {result.rowcount} embeddings.")

            # Delete related records from pdf_chunks
            logger.info(f"Deleting chunks related to file ID {file_id}...")
            delete_chunks_stmt = PdfChunk.__table__.delete().where(PdfChunk.file_id == file_id)
            result = await self.db.execute(delete_chunks_stmt)
            logger.info(f"Deleted {result.rowcount} chunks.")

            # Delete related records from extracted_full_text
            logger.info(f"Deleting full text entries related to file ID {file_id}...")
            delete_full_text_stmt = ExtractedFullText.__table__.delete().where(ExtractedFullText.file_id == file_id)
            result = await self.db.execute(delete_full_text_stmt)
            logger.info(f"Deleted {result.rowcount} full text entries.")

            # Delete the file record from the files table
            logger.info(f"Deleting file record with ID {file_id}...")
            delete_file_stmt = File.__table__.delete().where(File.id == file_id)
            await self.db.execute(delete_file_stmt)

            # Commit all changes
            await self.db.commit()
            logger.info(f"Successfully deleted file record and all related data for file ID {file_id}.")

            # Return success response
            api_response = {
                "data": {"file_id": file_id},
                "error": "",
                "message": file_delete_success,
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except SQLAlchemyError as e:
            logger.exception(f"Database error while deleting file ID {file_id}: {str(e)}")
            api_response = {
                "data": {"file_id": file_id},
                "error": db_error + str(e),
                "message": file_delete_failed,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

        except Exception as e:
            logger.exception(f"Unexpected error while deleting file ID {file_id}: {str(e)}")
            api_response = {
                "data": {"file_id": file_id},
                "error": str(e),
                "message": exception_occurred,
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def get_files(self, data, user_id):
        """
        POST Endpoint to search for files by name.
        """
        try:
            files_query = select(File).where(File.user_id==user_id, File.is_deleted==False)

            # Add search filter if search_text is provided
            if data.search_text:
                files_query = files_query.where(File.file_name.ilike(f"%{data.search_text}%"))

            if data.id:
                files_query = files_query.where(File.id==data.id)
                file = (await self.db.execute(files_query)).scalars().first()
                if file:
                    files_data = json.loads(FileSchema.model_validate(file).model_dump_json())
                else:
                    # Build the structured response
                    api_response = {
                        "data": {},
                        "error": file_not_found,
                        "message": file_not_found,
                        "status": False,
                    }
                    return JSONResponse(content=api_response, status_code=404)
            else:

                files = (await self.db.execute(files_query)).scalars().all()
                files_data = [json.loads(FileSchema.model_validate(file).model_dump_json()) for file in
                          files]

            # Build the structured response
            api_response = {
                "data": files_data,
                "error": "",
                "message": f"File {'list' if data.id else 'detail'} retrieved successfully.",
                "status": True,
            }
            return JSONResponse(content=api_response, status_code=200)

        except Exception as e:
            # Log the exception and return a structured error response
            logger.exception(f"Error retrieving files: {str(e)}", exc_info=True)
            api_response = {
                "data": [],
                "error": str(e),
                "message": "Failed to retrieve file list.",
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)

    async def get_file_path(self, data, user_id):
        try:
            files_query = select(File).where(File.id==data.id, File.user_id==user_id)

            files_query = files_query.where(File.id==data.id)
            file = (await self.db.execute(files_query)).scalars().first()
            if file:
                file_path = AWSFileOperations().get_presigned_url(user_id=user_id, file_name=file.file_path)
                api_response = {
                    "data": {"file_path": file_path},
                    "error": "File retrieved successfully!",
                    "message": "File retrieved successfully!"  ,
                    "status": True,
                }
                return JSONResponse(content=api_response, status_code=200)
            else:
                # Build the structured response
                api_response = {
                    "data": {},
                    "error": file_not_found,
                    "message": file_not_found,
                    "status": False,
                }
                return JSONResponse(content=api_response, status_code=404)

        except Exception as e:
            # Log the exception and return a structured error response
            logger.exception(f"Error retrieving file path: {str(e)}", exc_info=True)
            api_response = {
                "data": [],
                "error": str(e),
                "message": "Failed to retrieve file path.",
                "status": False,
            }
            return JSONResponse(content=api_response, status_code=500)