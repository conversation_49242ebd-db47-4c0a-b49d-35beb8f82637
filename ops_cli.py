#!/usr/bin/env python3
"""
Command-line interface for managing Startup Ops tasks.
"""
import argparse
import sys
from datetime import date, timedelta
from pathlib import Path
from typing import Optional

from models import TaskStatus
from task_manager import TaskStateManager
from dashboard_renderer import DashboardRenderer


class OpsCLI:
    """Command-line interface for task management."""
    
    def __init__(self, output_dir: str = ".ops_out"):
        self.state_manager = TaskStateManager(output_dir)
        self.dashboard_renderer = DashboardRenderer(output_dir=output_dir)
    
    def mark_done(self, task_id: str, notes: str = "") -> bool:
        """Mark a task as done."""
        success = self.state_manager.update_task_status(task_id, TaskStatus.DONE, notes)
        
        if success:
            print(f"✅ Task {task_id} marked as done")
            if notes:
                print(f"   Note: {notes}")
            self._refresh_dashboard()
            return True
        else:
            print(f"❌ Task {task_id} not found")
            return False
    
    def snooze_task(self, task_id: str, days: int, notes: str = "") -> bool:
        """Snooze a task for specified number of days."""
        tasks = self.state_manager.load_existing_tasks()
        
        if task_id not in tasks:
            print(f"❌ Task {task_id} not found")
            return False
        
        task = tasks[task_id]
        
        # Update due date
        if task.due:
            task.due = task.due + timedelta(days=days)
        else:
            task.due = date.today() + timedelta(days=days)
        
        # Update status and notes
        task.status = TaskStatus.SNOOZED
        if notes:
            task.notes = f"{task.notes}\n[Snoozed {days}d]: {notes}".strip()
        
        # Save updated tasks
        all_tasks = list(tasks.values())
        self.state_manager.save_tasks(all_tasks)
        
        print(f"😴 Task {task_id} snoozed for {days} days (due: {task.due})")
        if notes:
            print(f"   Note: {notes}")
        
        self._refresh_dashboard()
        return True
    
    def add_note(self, task_id: str, note_text: str) -> bool:
        """Add a note to a task."""
        tasks = self.state_manager.load_existing_tasks()
        
        if task_id not in tasks:
            print(f"❌ Task {task_id} not found")
            return False
        
        task = tasks[task_id]
        timestamp = date.today().isoformat()
        
        if task.notes:
            task.notes = f"{task.notes}\n[{timestamp}]: {note_text}"
        else:
            task.notes = f"[{timestamp}]: {note_text}"
        
        # Save updated tasks
        all_tasks = list(tasks.values())
        self.state_manager.save_tasks(all_tasks)
        
        print(f"📝 Note added to task {task_id}")
        print(f"   Note: {note_text}")
        
        self._refresh_dashboard()
        return True
    
    def list_tasks(self, status_filter: Optional[str] = None, limit: int = 20) -> None:
        """List tasks with optional status filter."""
        tasks = self.state_manager.load_existing_tasks()
        
        if not tasks:
            print("No tasks found. Run the indexer first.")
            return
        
        # Filter by status if specified
        if status_filter:
            filtered_tasks = [t for t in tasks.values() if t.status.value == status_filter]
        else:
            filtered_tasks = list(tasks.values())
        
        # Sort by due date and priority
        filtered_tasks.sort(key=lambda t: (
            t.due or date(9999, 12, 31),  # Put tasks without due date at end
            t.priority.value
        ))
        
        # Limit results
        filtered_tasks = filtered_tasks[:limit]
        
        if not filtered_tasks:
            print(f"No tasks found with status: {status_filter}")
            return
        
        print(f"\n📋 Tasks ({len(filtered_tasks)} shown):")
        print("-" * 80)
        
        for task in filtered_tasks:
            status_emoji = {
                'pending': '⏳',
                'in_progress': '🔄',
                'done': '✅',
                'snoozed': '😴'
            }.get(task.status.value, '❓')
            
            priority_emoji = {
                'high': '🔥',
                'med': '⚠️',
                'low': '🟢'
            }.get(task.priority.value, '⚪')
            
            due_str = f"Due: {task.due}" if task.due else "No due date"
            
            print(f"{status_emoji} {priority_emoji} {task.title[:50]}")
            print(f"   ID: {task.id} | {due_str} | Type: {task.type.value}")
            
            if task.notes:
                # Show last note
                notes_lines = task.notes.split('\n')
                last_note = notes_lines[-1] if notes_lines else ""
                if len(last_note) > 60:
                    last_note = last_note[:57] + "..."
                print(f"   Note: {last_note}")
            
            print()
    
    def show_summary(self) -> None:
        """Show a summary of all tasks."""
        tasks = self.state_manager.load_existing_tasks()
        
        if not tasks:
            print("No tasks found. Run the indexer first.")
            return
        
        # Calculate statistics
        total = len(tasks)
        by_status = {}
        by_priority = {}
        overdue_count = 0
        
        today = date.today()
        
        for task in tasks.values():
            # Count by status
            status = task.status.value
            by_status[status] = by_status.get(status, 0) + 1
            
            # Count by priority (only pending tasks)
            if status == 'pending':
                priority = task.priority.value
                by_priority[priority] = by_priority.get(priority, 0) + 1
                
                # Count overdue
                if task.due and task.due < today:
                    overdue_count += 1
        
        print("\n📊 Task Summary:")
        print("-" * 40)
        print(f"Total tasks: {total}")
        print(f"  ⏳ Pending: {by_status.get('pending', 0)}")
        print(f"  🔄 In Progress: {by_status.get('in_progress', 0)}")
        print(f"  ✅ Done: {by_status.get('done', 0)}")
        print(f"  😴 Snoozed: {by_status.get('snoozed', 0)}")
        
        if by_priority:
            print(f"\nPending by priority:")
            print(f"  🔥 High: {by_priority.get('high', 0)}")
            print(f"  ⚠️ Medium: {by_priority.get('med', 0)}")
            print(f"  🟢 Low: {by_priority.get('low', 0)}")
        
        if overdue_count > 0:
            print(f"\n⚠️  {overdue_count} tasks are overdue!")
        
        print(f"\n🌐 Dashboard: {Path('.ops_out/dashboard.html').absolute()}")
    
    def _refresh_dashboard(self) -> None:
        """Refresh the dashboard after task updates."""
        try:
            self.dashboard_renderer.render_from_tasks_json()
        except Exception as e:
            print(f"Warning: Could not refresh dashboard: {e}")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description="Startup Ops Task Manager")
    parser.add_argument("--output-dir", default=".ops_out", help="Output directory")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Mark done command
    done_parser = subparsers.add_parser("mark-done", help="Mark a task as done")
    done_parser.add_argument("--id", required=True, help="Task ID")
    done_parser.add_argument("--note", default="", help="Optional note")
    
    # Snooze command
    snooze_parser = subparsers.add_parser("snooze", help="Snooze a task")
    snooze_parser.add_argument("--id", required=True, help="Task ID")
    snooze_parser.add_argument("--days", type=int, required=True, help="Days to snooze")
    snooze_parser.add_argument("--note", default="", help="Optional note")
    
    # Add note command
    note_parser = subparsers.add_parser("note", help="Add a note to a task")
    note_parser.add_argument("--id", required=True, help="Task ID")
    note_parser.add_argument("--text", required=True, help="Note text")
    
    # List tasks command
    list_parser = subparsers.add_parser("list", help="List tasks")
    list_parser.add_argument("--status", choices=["pending", "in_progress", "done", "snoozed"], 
                           help="Filter by status")
    list_parser.add_argument("--limit", type=int, default=20, help="Maximum number of tasks to show")
    
    # Summary command
    subparsers.add_parser("summary", help="Show task summary")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = OpsCLI(args.output_dir)
    
    try:
        if args.command == "mark-done":
            success = cli.mark_done(args.id, args.note)
            sys.exit(0 if success else 1)
        
        elif args.command == "snooze":
            success = cli.snooze_task(args.id, args.days, args.note)
            sys.exit(0 if success else 1)
        
        elif args.command == "note":
            success = cli.add_note(args.id, args.text)
            sys.exit(0 if success else 1)
        
        elif args.command == "list":
            cli.list_tasks(args.status, args.limit)
        
        elif args.command == "summary":
            cli.show_summary()
    
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
