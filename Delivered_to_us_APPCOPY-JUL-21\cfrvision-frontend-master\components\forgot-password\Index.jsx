"use client";
import AnimatedText from "@/components/common/AnimatedText";
import React from "react";

import { valibotResolver } from "@hookform/resolvers/valibot";
import { useRouter } from "next/navigation";
import { Controller, useForm } from "react-hook-form";
import * as v from "valibot";
import dynamic from "next/dynamic";
import axiosInstance from "@/utlis/axios";
import Toaster from "../common/Toaster";

const ParallaxContainer = dynamic(
  () => import("@/components/common/ParallaxContainer"),
  { ssr: false }
);

const schema = v.object({
  email: v.pipe(
    v.string(),
    v.nonEmpty("Email is required"),
    v.email("Email is invalid")
  ),
});

const ForgotPassword = () => {
  const { contextHolder, showToast } = Toaster();
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      email: "",
    },
  });

  const router = useRouter();

  const onSubmit = async (data) => {
    try {
      const response = await axiosInstance.post(
        "/v1/auth/forgot-password",
        data
      );
      showToast({
        type: "sucess",
        message: response?.data?.message,
      });
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    } catch (error) {
      console.log("err", error);
      showToast({
        type: "error",
        message: error?.response?.data?.message || error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      <main id="main">
        <section className="page-section pt-0 pb-0">
          <ParallaxContainer
            className="page-section bg-gray-light-1 bg-light-alpha-90 parallax-5"
            style={{
              backgroundImage: "url(/assets/background.png)",
            }}
          >
            <>
              <div className="position-absolute top-0 bottom-0 start-0 end-0 bg-gradient-white background-img" />
              <div className="container position-relative pt-50">
                {/* Section Content */}
                <div className="text-center">
                  <div className="row">
                    {/* Page Title */}
                    <div className="col-md-8 offset-md-2">
                      <h2
                        className="section-caption-slick mb-30 mb-sm-20 wow fadeInUp"
                        data-wow-duration="1.2s"
                      >
                        Account
                      </h2>
                      <h1 className="mb-0">
                        <span
                          className="wow charsAnimIn"
                          data-splitting="chars"
                        >
                          <AnimatedText text="Forgot your password." />
                        </span>
                      </h1>
                      <div className="sub-text mt-5">
                        Enter your email address to receive password reset
                        instructions.
                      </div>
                    </div>
                    {/* End Page Title */}
                  </div>
                </div>
                {/* End Section Content */}
              </div>
            </>
          </ParallaxContainer>
        </section>
        <>
          <>
            {/* Section */}
            <section className="page-section container pt-0">
              <div className="row">
                <div className="col-md-6 offset-md-3">
                  <form
                    noValidate
                    autoComplete="off"
                    onSubmit={handleSubmit(onSubmit)}
                    className="form contact-form"
                  >
                    <div className="mb-30">
                      {/* Name */}
                      <div className="form-group">
                        <label htmlFor="username">Email Address</label>
                        <Controller
                          name="email"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="email"
                              name="email"
                              id="email"
                              className="input-md round-large form-control"
                              placeholder="Enter email address"
                              required
                              aria-required="true"
                              value={field.value}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              ref={field.ref}
                            />
                          )}
                        />
                        {errors?.email && touchedFields.email && (
                          <span className="text-red">
                            {errors.email.message}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="row mb-30">
                      <div className="col-6">
                        {/* Inform Tip */}
                        <div className="form-tip pt-10 back-to-login text-secondary">
                          <i
                            className="mi-arrow-left size-24"
                            style={{ marginRight: "5px" }}
                          />
                          <a
                            href="/login"
                            style={{ textDecoration: "none" }}
                            className="text-secondary"
                          >
                            Back to Login
                          </a>
                        </div>
                      </div>
                      <div className="col-6">
                        {/* Send Button */}
                        <div className="text-end">
                          <button
                            className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim mb-xs-10"
                            id="forgot-password-btn"
                            disabled={!isValid}
                          >
                            <span>Submit</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </section>
            {/* End Section */}
            {/* Divider */}
          </>

          {/* End Divider */}
        </>
      </main>
    </>
  );
};

export default ForgotPassword;
