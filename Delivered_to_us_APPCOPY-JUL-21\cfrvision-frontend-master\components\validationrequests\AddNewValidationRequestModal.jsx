"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import { Select, Upload, message } from "antd";
import { valibotResolver } from "@hookform/resolvers/valibot";
import * as v from "valibot";
import { CloseOutlined } from "@ant-design/icons"; // For the remove icon
import Toaster from "../common/Toaster";
import axiosInstance from "@/utlis/axios";

const { Option } = Select;

// Validation schema using Valibot
const schema = v.object({
  selectedImage: v.pipe(
    v.string(), // First, treat it as a string to allow empty input
    v.nonEmpty("Please select a file"),
    v.regex(/^\d*\.?\d+$/, "Only numeric values are allowed"), // Ensure it's numeric
    v.transform((val) => (val === "" ? null : Number(val))) // Convert to number or null if empty
  ),
  aiChecks: v.pipe(
    v.string(), // First, treat it as a string to allow empty input
    v.nonEmpty("Please select an ai checks"),
    v.regex(/^\d*\.?\d+$/, "Only numeric values are allowed"), // Ensure it's numeric
    v.transform((val) => (val === "" ? null : Number(val))) // Convert to number or null if empty
  ),
  model_used: v.pipe(v.string(), v.nonEmpty("Model is required")),
});

const AddNewValidationRequestModal = ({
  show,
  setShow,
  fileList,
  categoryList,
  aiCheckList,
  fetchValidationRequests,
  onValidationSubmit,
  modelList,
}) => {
  const [uploading, setUploading] = useState(false);
  const [fileOptions, setFileOptions] = useState(fileList);
  const [selectedFileOption, setSelectedFileOption] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [aiChecksOptions, setAiChecksOptions] = useState([]);
  const [categoryAiChecksMap, setCategoryAiChecksMap] = useState({});
  const [selectedChecks, setSelectedChecks] = useState([]);
  const { contextHolder, showToast } = Toaster();

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: valibotResolver(schema),
    mode: "all",
    defaultValues: {
      selectedImage: "",
      aiChecks: "",
      model_used: "",
    },
  });

  const handleFileUpload = (file) => {
    if (file && file.type === "application/pdf") {
      if (file.size <= 1024 * 1024 * 10) {
        const newFileOption = { value: file.name, label: file.name };
        setFileOptions((prev) => [...prev, newFileOption]);
        setSelectedFileOption(file.name);
        setValue("selectedImage", file.name);
        showToast({
          type: "success",
          message: `${file.name} uploaded successfully.`,
        });
      } else {
        showToast({
          type: "error",
          message: "File must be smaller than 10MB.",
        });
      }
    } else {
      showToast({
        type: "error",
        message: "Only PDF files are allowed.",
      });
    }
    return false;
  };

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setAiChecksOptions(aiCheckList[e.target.value] || []);

    const previousAiChecks = categoryAiChecksMap[e.target.value] || [];
    setValue("aiChecks", "");
  };

  const handleAiChecksChange = (value) => {
    // Update the categoryAiChecksMap
    setCategoryAiChecksMap((prev) => ({
      ...prev,
      [selectedCategory]: value,
    }));

    // Update the selectedChecks list
    const allSelectedChecks = Object.values(categoryAiChecksMap)
      .flat()
      .concat(value)
      .filter((val, index, self) => self.indexOf(val) === index); // Remove duplicates
    setSelectedChecks(allSelectedChecks);

    // Update the form value
    // setValue("aiChecks", value);
  };

  const handleRemoveCheck = (checkToRemove) => {
    // Remove the check from the selectedChecks list
    const updatedSelectedChecks = selectedChecks.filter(
      (check) => check !== checkToRemove
    );
    setSelectedChecks(updatedSelectedChecks);

    // Remove the check from the categoryAiChecksMap
    const updatedCategoryChecks = categoryAiChecksMap[selectedCategory].filter(
      (check) => check !== checkToRemove
    );
    setCategoryAiChecksMap((prev) => ({
      ...prev,
      [selectedCategory]: updatedCategoryChecks,
    }));

    // Update the form value
    setValue("aiChecks", updatedCategoryChecks);
  };

  const onSubmit = async (data) => {
    try {
      const requestData = {
        document_id: data.selectedImage,
        check_id: data?.aiChecks,
        model_used: data?.model_used,
      };
      setUploading(true);
      const response = await axiosInstance.post(
        "/v1/llm_check_request/create-llm-check-request",
        requestData
      );
      onValidationSubmit(
        response.data.message || "Validation Requested added successfully."
      );
      fetchValidationRequests();
      setUploading(false);
      setShow(false);
      return;
    } catch (error) {
      setUploading(false);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  const getAiCheckLabel = (id) => {
    const aiCheckArr = Object.keys(aiCheckList)
      ?.map((item) => aiCheckList[item])
      ?.flat();
    const index = aiCheckArr?.findIndex((data) => data?.check_id == id);
    if (index !== -1) {
      return aiCheckArr[index]?.check_name;
    }
    return 1;
  };

  return (
    <Modal
      show={show}
      onHide={() => setShow(false)}
      centered
      size="lg"
      backdrop="static"
    >
      {contextHolder}
      <form
        noValidate
        onSubmit={handleSubmit(onSubmit)}
        className="form contact-form"
      >
        <Modal.Header closeButton>
          <Modal.Title>Add New Validation Request</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row className="mb-3">
            <Col md={12}>
              <div className="form-group">
                <label>Select File</label>
                <Controller
                  name="selectedImage"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      placeholder="Select a file"
                      className="input-md round form-control"
                    >
                      <option value="" disabled>
                        Select a file
                      </option>
                      {fileOptions?.map((item) => (
                        <option value={item?.id}>{item?.file_name}</option>
                      ))}
                    </select>
                  )}
                />
                {errors.selectedImage && (
                  <div className="text-danger mt-2">
                    {errors.selectedImage.message}
                  </div>
                )}
              </div>
            </Col>
            {/* <Col md={6}>
              <div className="form-group">
                <Upload
                  beforeUpload={handleFileUpload}
                  showUploadList={false}
                  accept=".pdf"
                >
                  <Button type="button" style={{ marginTop: "28px" }}>
                    Upload New File
                  </Button>
                </Upload>
              </div>
            </Col> */}
          </Row>
          <Row className="mb-3">
            <Col md={12}>
              <div className="form-group">
                <label>Select Model</label>
                <Controller
                  name="model_used"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      placeholder="Select a model"
                      className="input-md round form-control"
                    >
                      <option value="" disabled>
                        Select a model
                      </option>
                      {modelList &&
                        modelList?.available_models
                          ?.replaceAll("'", "")
                          ?.split(",")
                          ?.map((item) => <option value={item}>{item}</option>)}
                    </select>
                  )}
                />
                {errors.model_used && (
                  <div className="text-danger mt-2">
                    {errors.model_used.message}
                  </div>
                )}
              </div>
            </Col>
          </Row>
          <Row className="mb-3">
            <Col>
              <div className="form-group">
                <label>Select Checks Category</label>
                <select
                  placeholder="Select a category"
                  className="input-md round form-control"
                  style={{ width: "100%" }}
                  onChange={handleCategoryChange}
                >
                  <option disabled selected>
                    Select a category
                  </option>
                  {categoryList?.map((item) => (
                    <option value={item?.category_id}>
                      {item?.category_name}
                    </option>
                  ))}
                </select>
              </div>
            </Col>
          </Row>
          <Row className="mb-3">
            <Col>
              <div className="form-group">
                <label className="me-2">Select AI Checks</label>
                <Controller
                  name="aiChecks"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      className="input-md round form-control"
                      allowClear
                      placeholder="Select AI Checks"
                      disabled={!selectedCategory}
                      // onChange={handleAiChecksChange}
                    >
                      <option value="" disabled selected>
                        Select ai checks
                      </option>
                      {aiChecksOptions?.map((item) => (
                        <option value={item?.check_id}>
                          {item?.check_name}
                        </option>
                      ))}
                    </select>
                  )}
                />
                {errors.aiChecks && (
                  <div className="text-danger mt-2">
                    {errors.aiChecks.message}
                  </div>
                )}
              </div>
            </Col>
          </Row>
          {/* Selected Checks List (only show if multiple categories are selected) */}
          {/* {Object.keys(categoryAiChecksMap).length > 1 && (
            <Row className="mb-3">
              <Col>
                <div className="form-group">
                  <label>Selected Checks</label>
                  <div
                    style={{
                      border: "1px solid #d9d9d9",
                      padding: "10px",
                      borderRadius: "4px",
                    }}
                  >
                    {selectedChecks.length === 0 ? (
                      <div style={{ color: "#999" }}>
                        No checks selected yet.
                      </div>
                    ) : (
                      selectedChecks.map((check) => (
                        <div
                          key={check}
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "8px",
                          }}
                        >
                          <span>{getAiCheckLabel(check)}</span>
                          <CloseOutlined
                            style={{ cursor: "pointer", color: "#ff4d4f" }}
                            onClick={() => handleRemoveCheck(check)}
                          />
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          )} */}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShow(false)}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            {uploading ? "Saving..." : "Submit"}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default AddNewValidationRequestModal;
