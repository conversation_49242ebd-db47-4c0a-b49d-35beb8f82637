from fastapi import Depends, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import profile_router
from app.database import get_db
from app.operations.profile_operations.profile_operations import ProfileOperations
from app.schemas.profile import (ChangePasswordRequest, ProfileUpdateSchema)
from app.utils.jwt_authentication.jwt_handler import JWTBearer


@profile_router.post("/basic/update")
async def update_profile_details(profile_img: UploadFile | None=None,profile_data: ProfileUpdateSchema = Depends(ProfileUpdateSchema.as_form),user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    """
    Update the basic profile details of a user.

    This endpoint updates the basic profile information of a user, such as their name and email,
    using the provided data.

    **Args**:
        data (BasicProfileUpdate): The data used to update the user's basic profile details.
        db (AsyncSession): The database session used to perform the update operation.

    **Returns**:
        JSON: The updated basic profile data.
    """
    return await ProfileOperations(db).update_basic_profile_details(profile_data, user_id=user.get('user_id',''), profile_img=profile_img)


#
# @profile_router.post("/billing_address/update")
# async def update_billing_address(data: BillingDataUpdate,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     """
#     Update the billing address details of a user.
#
#     This endpoint updates the user's billing address information based on the provided data.
#
#     **Args**:
#         data (BillingDataUpdate): The data used to update the user's billing address details.
#         db (AsyncSession): The database session used to perform the update operation.
#
#     **Returns**:
#         JSON: The updated billing address details.
#     """
#     return await ProfileOperations(db).update_billing_address_details(data=data, user_id=user.get('user_id',''))
#
#
# @profile_router.post("/subscription/cancel")
# async def update_subscription(user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     """
#     Cancel a user's subscription.
#
#     This endpoint cancels a user's subscription based on the provided cancellation data.
#
#     **Args**:
#         data (CancelSubscription): The data used to cancel the user's subscription.
#         db (AsyncSession): The database session used to perform the cancellation operation.
#
#     **Returns**:
#         JSON: Confirmation of the subscription cancellation.
#     """
#     return await ProfileOperations(db).cancel_subscription(user.get('user_id'))

#
# @profile_router.post("/cards/add")
# async def add_new_card(data: NewCardData,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     return await ProfileOperations(db).add_new_card(data, user.get('user_id',''))
#
#
# @profile_router.post("/cards/delete")
# async def delete_card(data: DeleteCardData,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     return await ProfileOperations(db).delete_card(data, user.get('user_id',''))
#
# @profile_router.post("/change-primary-card")
# async def change_primary_card(data: ChangePrimaryCard,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     return await ProfileOperations(db).change_primary_card(data, user.get('user_id',''))


@profile_router.post("/basic/get")
async def get_profile_details(user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
    """
    Retrieve the basic profile details of a user.

    This endpoint fetches the basic profile details of a user, such as their email, first name,
    and last name, using the user's profile ID.

    **Args**:
        data (UserProfileID): The user profile ID used to fetch the basic profile details.
        db (AsyncSession): The database session used to query and retrieve the user's basic profile data.

    **Returns**:
        JSON: The user's basic profile data.
    """
    # EMAIL, FIRST NAME, LAST NAME
    return await ProfileOperations(db).get_basic_profile_details(user_id=user.get('user_id', ''))


# @profile_router.post("/cards/get")
# async def get_card_details(data: UserProfileID, user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     """
#     Retrieve the card details of a user.
#
#     This endpoint fetches the card details of a user using their profile ID.
#
#     **Args**:
#         data (UserProfileID): The user profile ID used to fetch the card details.
#         db (AsyncSession): The database session used to query and retrieve the user's card data.
#
#     **Returns**:
#         JSON: The user's card details.
#     """
#     return await ProfileOperations(db).get_card_details(data, user.get('user_id',''))


# @profile_router.post("/invoices/get")
# async def get_invoices_details(data: UserProfileID,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     """
#     Retrieve the invoice details of a user.
#
#     This endpoint fetches the invoice details of a user using their profile ID.
#
#     **Args**:
#         data (UserProfileID): The user profile ID used to fetch the invoice details.
#         db (AsyncSession): The database session used to query and retrieve the user's invoice data.
#
#     **Returns**:
#         JSON: The user's invoice details.
#     """
#     return await ProfileOperations(db).get_invoice_details(data, user.get('user_id',''))
#
# @profile_router.post("/invoice-pdf/")
# async def get_invoices_pdf(data: InvoicePDFViewInput,user: dict = Depends(JWTBearer()), db: AsyncSession = Depends(get_db)):
#     """
#     Retrieve the invoice details of a user.
#
#     This endpoint fetches the invoice details of a user using their profile ID.
#
#     **Args**:
#         data (UserProfileID): The user profile ID used to fetch the invoice details.
#         db (AsyncSession): The database session used to query and retrieve the user's invoice data.
#
#     **Returns**:
#         JSON: The user's invoice details.
#     """
#     return await ProfileOperations(db).get_invoice_pdf_operation(data, user.get('user_id',''))
#

@profile_router.post("/change-password")
async def set_password(data: ChangePasswordRequest, user: dict = Depends(JWTBearer()),
                       db: AsyncSession = Depends(get_db)):
    """
    User login API that verifies the credentials and returns access and refresh tokens.

    **Args**:
        request (LoginRequest): The request body containing user credentials (phone number and password).
        db (AsyncSession): The database session to query user data.

    **Returns**:
        dict: A response dictionary containing the login status, tokens, and any error messages.
    """
    return await ProfileOperations(db).reset_password_operation(data=data, user_id=user.get('user_id', ''))
