import Loading from "@/components/common/Loading";
import dynamic from "next/dynamic";
import React from "react";

const ValidationRequestDetail = dynamic(() => import("@/components/validationrequests/details/index"), {
  loading: () => <Loading/>
});

const ValidationRequestDetailPage = ({ params }) => {

    const { requestId } = params;

  return (
    <div>
      <ValidationRequestDetail id={requestId} />
    </div>
  );
};

export default ValidationRequestDetailPage;
