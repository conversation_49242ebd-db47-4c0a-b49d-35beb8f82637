from typing import Optional

from fastapi import Form
from pydantic import BaseModel, Field

from app.schemas.auth import UserResponseSchema
from app.schemas.common import FetchSchemaSearchFilter

class OrganizationAddress(BaseModel):
    street1: str
    street2: Optional[str]
    city: str
    state: str
    zip_code: str
    contact_no: str


class ProfileParameterSchema(BaseModel):
    first_name: str = Field(
        ...,
        description="First Name.",
        examples=["Shivani"]
    )
    last_name: str = Field(
        ...,
        description="Last Name.",
        examples=["Dobariya"]
    )
    organization: Optional[str] = Field(
        ...,
        description="Organization.",
        examples=["OX"]
    )
    organization_address_street1: Optional[str] = Field(
        ...,
        description="Organization Address Street1.",
        examples=["OX"]
    )
    organization_address_street2: Optional[str] = Field(
        ...,
        description="Organization Address Street2.",
        examples=["OX"]
    )
    organization_address_city: Optional[str] = Field(
        ...,
        description="Organization Address City.",
        examples=["OX"]
    )
    organization_address_state: Optional[str] = Field(
        ...,
        description="Organization Address State.",
        examples=["OX"]
    )
    organization_address_zip_code: Optional[str] = Field(
        ...,
        description="Organization Address Zip Code.",
        examples=["OX"]
    )
    organization_contact: Optional[str] = Field(
        ...,
        description="Organization Contact.",
        examples=["<EMAIL>"]
    )

class ProfileUpdateSchema(ProfileParameterSchema, BaseModel):
    @classmethod
    def as_form(cls,
                first_name: str = Form(...),
                last_name: str = Form(...),
                organization: Optional[str] = Form(""),
                organization_address_street1: Optional[str] = Form(""),
                organization_address_street2: Optional[str] = Form(""),
                organization_address_city: Optional[str] = Form(""),
                organization_address_state: Optional[str] = Form(""),
                organization_address_zip_code: Optional[str] = Form(""),
                organization_contact: Optional[str] = Form("")) -> "ProfileUpdateSchema":
        return cls(first_name=first_name,
                   last_name=last_name,
                   organization=organization,
                   organization_address_street1=organization_address_street1,
                   organization_address_street2=organization_address_street2,
                   organization_address_city=organization_address_city,
                   organization_address_state=organization_address_state,
                   organization_address_zip_code=organization_address_zip_code,
                   organization_contact=organization_contact)




# class SubscriptionItem(BaseModel):
#     id: int
#     item_price_id: str
#     item_type: str
#     quantity: int
#     unit_price: int
#     amount: int
#     free_quantity: int
#     trial_end: Optional[int]
#     object: str
#
#     class Config:
#         from_attributes = True
#         orm_mode = True
#
# class ShippingAddress(BaseModel):
#     id: int
#     first_name: str
#     last_name: str
#     line1: str
#     line2: Optional[str]
#     line3: Optional[str]
#     city: str
#     state_code: str
#     state: str
#     country: str
#     zip: str
#     validation_status: str
#     object: str
#
#     class Config:
#         from_attributes = True
#         orm_mode = True
#
# class SubscriptionData(BaseModel):
#     id: str
#     billing_period: int
#     billing_period_unit: str
#     trial_end: Optional[int]
#     customer_id: str
#     status: str
#     trial_start: Optional[int]
#     next_billing_at: int
#     has_scheduled_changes: bool
#     payment_source_id: str
#     channel: str
#     resource_version: int
#     currency_code:str
#     due_invoices_count: int
#     has_scheduled_advance_invoices: bool
#     subscription_items: List[SubscriptionItem]
#     shipping_addresses: List[ShippingAddress]
#
#     class Config:
#         from_attributes = True
#         orm_mode = True
#
# class CardData(BaseModel):
#     id: int
#     payment_source_id: str
#     iin: str
#     last4: str
#     brand: str
#     funding_type: str
#     expiry_month: int
#     expiry_year: int
#     masked_number: str
#
#     class Config:
#         from_attributes = True
#         orm_mode = True
#
# class InvoiceData(BaseModel):
#     id: str
#     recurring: bool
#     status: str
#     price_type: str
#     date: int
#     due_date: int
#     net_term_days: int
#     exchange_rate: float
#     total: float
#     amount_paid: float
#     amount_adjusted: float
#     write_off_amount: float
#     credits_applied: float
#     amount_due: float
#     paid_at: int
#     resource_version: int
#     object_type: str
#     first_invoice: bool
#     amount_to_collect: float
#     round_off_amount: float
#     new_sales_amount: float
#     has_advance_charges: bool
#     currency_code: str
#     base_currency_code: str
#     generated_at: int
#     is_gifted: bool
#     term_finalized: bool
#     channel: str
#     tax: float
#     subscription: SubscriptionData
#
#     class Config:
#         from_attributes = True
#         orm_mode = True


class UserProfileID(FetchSchemaSearchFilter):
    pass


class BasicProfileUpdate(BaseModel):
    first_name: str
    last_name: str
    organization: Optional[str]
    organization_address: Optional[str]
    organization_contact: Optional[str]
    profile_picture: Optional[str]


# class BillingDataUpdate(BaseModel):
#     id: Optional[int] = None
#     first_name: str
#     last_name: str
#     email: str
#     company: str
#     line1: str
#     line2: str
#     line3: str
#     city: str
#     state: str
#     state_code: str
#     country: str
#     zip: str
#     validation_status: str
#
#     class Config:
#         from_attributes = True
#         orm_mode = True

# class BillingDataResponse(BaseModel):
#     id: Optional[int] = None
#     first_name: Optional[str] = ""
#     last_name: Optional[str] = ""
#     email: Optional[str] = ""
#     company: Optional[str] = ""
#     line1: str
#     line2: Optional[str] = ""
#     line3: Optional[str] = ""
#     city: str
#     state: str
#     state_code: str
#     country: str
#     zip: str
#     validation_status: str
#
#     class Config:
#         from_attributes = True
#         orm_mode = True
#

class BasicUserResponse(UserResponseSchema):
    # If already a full URL, return as is

    # billing_address: Optional[BillingDataUpdate] = None

    class Config:
        from_attributes = True
        orm_mode = True


# class NewCardData(BaseModel):
#     expiry_month: int
#     expiry_year: int
#     masked_number: str
#     cvv: str
#
#
# class DeleteCardData(ObjectID, BaseModel):
#     pass
#
# class ChangePrimaryCard(ObjectID, BaseModel):
#     pass

class ChangePasswordRequest(BaseModel):
    old_password: str = Field(..., json_schema_extra={
        'description': 'Enter old password',
        'examples': ['123456'],
    })
    new_password: str = Field(..., json_schema_extra={
        'description': 'Enter new password',
        'examples': ['456123'],
    })

# class InvoicePDFViewInput(ObjectID, BaseModel):
#     pass
