"use client";

import React from "react";
import dynamic from "next/dynamic";

const ParallaxContainer = dynamic(() => import('./ParallaxContainer'), {ssr : false})

const Loading = () => {
  return (
    <main id="main">
      <section className="page-section pt-0 pb-0">
        <ParallaxContainer className="page-section bg-gray-light-1 parallax-5">
          <div className="loading-container">
            <div className="loadingio-spinner-eclipse-nq4q5u6dq7r">
              <div className="ldio-x2uulkbinbj">
                <div></div>
              </div>
            </div>
            <style jsx>{`
              .loading-container {
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 600px;
              }
              @keyframes ldio-x2uulkbinbj {
                0% {
                  transform: rotate(0deg);
                }
                50% {
                  transform: rotate(180deg);
                }
                100% {
                  transform: rotate(360deg);
                }
              }
              .ldio-x2uulkbinbj div {
                position: absolute;
                animation: ldio-x2uulkbinbj 0.89s linear infinite;
                width: 113.94px;
                height: 113.94px;
                top: 48.53px;
                left: 48.53px;
                border-radius: 50%;
                box-shadow: 0 5.486px 0 0 #1330a8;
                transform-origin: 56.97px 59.713px;
              }
              .loadingio-spinner-eclipse-nq4q5u6dq7r {
                width: 211px;
                height: 211px;
                display: inline-block;
                overflow: hidden;
                background: none;
              }
              .ldio-x2uulkbinbj {
                width: 100%;
                height: 100%;
                position: relative;
                transform: translateZ(0) scale(1);
                backface-visibility: hidden;
                transform-origin: 0 0;
              }
              .ldio-x2uulkbinbj div {
                box-sizing: content-box;
              }
            `}</style>
          </div>
        </ParallaxContainer>
      </section>
    </main>
  );
};

export default Loading;
