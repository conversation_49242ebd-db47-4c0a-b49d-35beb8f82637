<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - Madia Application Solutions</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="images/Favicon.png" type="image/png">
    <img src="images/logo.png" alt="Madia Application Solutions Logo" style="display: block; margin: 0 auto; width: 200px;">
    <script>
        function sendEmail(event) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                message: formData.get('message')
            };

            // Simulate sending email (replace with actual email sending logic)
            console.log('Sending <NAME_EMAIL>', data);

            // Show thank you message
            document.getElementById('thankYouMessage').style.display = 'block';
            form.reset();
        }
    </script>
</head>
<body>
    <header>
        <nav>
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="products.html">Products</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="blog.html">Blog</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>
    <main>
        <h1>Contact Us</h1>
        <form onsubmit="sendEmail(event)">
            <label for="name">Name:</label><br>
            <input type="text" id="name" name="name" required style="width: 100%;"><br><br>

            <label for="email">Email:</label><br>
            <input type="email" id="email" name="email" required style="width: 100%;"><br><br>

            <label for="message">Message:</label><br>
            <textarea id="message" name="message" required style="width: 100%; height: 150px;"></textarea>

            <button type="submit">Send Message</button>
        </form>
        <p id="thankYouMessage" style="display:none;">Thank you for your message! We will get back to you shortly.</p>
    </main>
    <footer>
        <p>&copy; 2023 Madia Application Solutions. All rights reserved.</p>
    </footer>
</body>
</html>
