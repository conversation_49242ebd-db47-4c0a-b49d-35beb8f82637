import { valibot<PERSON>esolver } from "@hookform/resolvers/valibot";
import { Controller, useForm } from "react-hook-form";

const Security = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    // resolver: valibotResolver(),
    mode: "all",
    defaultValues: {
      new_password: "",
      confirm_password: "",
    },
  });
  return (
    <>
      <form
        noValidate
        autoComplete="off"
        // onSubmit={handleSubmit(onSubmit)}
        className="form contact-form"
      >
        <div className="mb-30">
          {/*Current Password */}
          <div className="form-group">
            <label htmlFor="newpassword">Current Password</label>
            <Controller
              name="current_password"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  {...field}
                  type="password"
                  name="current_password"
                  id="current_password"
                  className="input-md round-large form-control"
                  placeholder="Enter current password"
                  required
                  value={field.value}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  ref={field.ref}
                />
              )}
            />
            {errors?.current_password && (
              <span className="text-red">
                {errors?.current_password.message}
              </span>
            )}
          </div>
          {/*New Password */}
          <div className="form-group">
            <label htmlFor="newpassword">New Password</label>
            <Controller
              name="new_password"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  {...field}
                  type="password"
                  name="new_password"
                  id="new_password"
                  className="input-md round-large form-control"
                  placeholder="Enter new password"
                  required
                  value={field.value}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  ref={field.ref}
                />
              )}
            />
            {errors?.new_password && (
              <span className="text-red">{errors?.new_password.message}</span>
            )}
          </div>
          {/*Confirm Password */}
          <div className="form-group">
            <label htmlFor="confirmpassword">Confirm Password</label>
            <Controller
              name="confirm_password"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  {...field}
                  type="password"
                  name="confirm_password"
                  id="confirm_password"
                  className="input-md round-large form-control"
                  placeholder="Re-enter new password"
                  required
                  value={field.value}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  ref={field.ref}
                />
              )}
            />
            {errors?.confirm_password && (
              <span className="text-red">
                {errors?.confirm_password.message}
              </span>
            )}
          </div>
        </div>
        <div className="text-end">
          <button
            className="btn btn-mod btn-color btn-large btn-circle btn-hover-anim"
            id="update_password-btn"
          >
            <span>Save Changes</span>
          </button>
        </div>
      </form>
    </>
  );
};

export default Security;
