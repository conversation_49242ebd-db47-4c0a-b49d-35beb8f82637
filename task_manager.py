"""
Task generation and state management for the Startup Ops Indexer.
"""
import json
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Set
import logging

from models import (
    Task, TaskType, TaskStatus, Priority, 
    ProcessCandidate, OpportunityCandidate, 
    FileInfo, Config, ActivityLogEntry
)

logger = logging.getLogger(__name__)


class TaskGenerator:
    """Generates tasks from process and opportunity candidates."""
    
    def __init__(self, config: Config):
        self.config = config
    
    def generate_tasks_from_processes(self, candidates: List[ProcessCandidate]) -> List[Task]:
        """Generate tasks from process candidates."""
        tasks = []
        
        for candidate in candidates:
            task = Task(
                id=Task.generate_id(candidate.name, candidate.evidence_paths),
                title=candidate.name,
                type=TaskType.PROCESS,
                status=TaskStatus.PENDING,
                priority=self._determine_priority(candidate),
                due=candidate.next_due,
                source=candidate.evidence_paths,
                confidence=candidate.confidence,
                recurrence=candidate.cadence,
                tags=self._extract_tags_from_candidate(candidate)
            )
            tasks.append(task)
        
        return tasks
    
    def generate_tasks_from_opportunities(self, candidates: List[OpportunityCandidate]) -> List[Task]:
        """Generate tasks from opportunity candidates."""
        tasks = []
        
        for candidate in candidates:
            task = Task(
                id=Task.generate_id(candidate.title, candidate.evidence_paths),
                title=candidate.title,
                type=TaskType.OPPORTUNITY,
                status=TaskStatus.PENDING,
                priority=Priority.MEDIUM,
                source=candidate.evidence_paths,
                notes=f"Why: {candidate.why_it_matters}\nNext: {candidate.suggested_next_step}",
                confidence=candidate.confidence,
                tags=self._extract_tags_from_opportunity(candidate)
            )
            tasks.append(task)
        
        return tasks
    
    def generate_oneoff_tasks(self, files: List[FileInfo]) -> List[Task]:
        """Generate one-off tasks from files with specific patterns."""
        tasks = []
        
        for file_info in files:
            # Look for task indicators in content
            task_indicators = self._find_task_indicators(file_info)
            
            for indicator in task_indicators:
                task = Task(
                    id=Task.generate_id(indicator['title'], [file_info.path]),
                    title=indicator['title'],
                    type=TaskType.ONEOFF,
                    status=TaskStatus.PENDING,
                    priority=indicator.get('priority', Priority.MEDIUM),
                    due=indicator.get('due_date'),
                    source=[file_info.path],
                    confidence=indicator.get('confidence', 0.6),
                    tags=file_info.topics
                )
                tasks.append(task)
        
        return tasks
    
    def _determine_priority(self, candidate: ProcessCandidate) -> Priority:
        """Determine task priority based on candidate properties."""
        if candidate.confidence > 0.8:
            return Priority.HIGH
        elif candidate.confidence > 0.5:
            return Priority.MEDIUM
        else:
            return Priority.LOW
    
    def _extract_tags_from_candidate(self, candidate: ProcessCandidate) -> List[str]:
        """Extract tags from process candidate."""
        tags = []
        
        # Extract from source hint
        if 'receipt' in candidate.source_hint.lower():
            tags.extend(['expenses', 'finance'])
        if 'grant' in candidate.source_hint.lower():
            tags.extend(['grants', 'funding'])
        if 'delivered' in candidate.source_hint.lower():
            tags.extend(['deliverables', 'review'])
        
        return list(set(tags))
    
    def _extract_tags_from_opportunity(self, candidate: OpportunityCandidate) -> List[str]:
        """Extract tags from opportunity candidate."""
        tags = ['opportunity']
        
        title_lower = candidate.title.lower()
        if 'brand' in title_lower:
            tags.append('branding')
        if 'linkedin' in title_lower:
            tags.append('social')
        if 'aws' in title_lower:
            tags.append('infrastructure')
        
        return tags
    
    def _find_task_indicators(self, file_info: FileInfo) -> List[Dict]:
        """Find task indicators in file content."""
        indicators = []
        content = file_info.extracted_text.lower()
        
        # Look for common task patterns
        task_patterns = [
            r'todo[:\s]+(.+)',
            r'action[:\s]+(.+)',
            r'next[:\s]+(.+)',
            r'follow[:\s]*up[:\s]+(.+)',
            r'due[:\s]+(.+)',
            r'deadline[:\s]+(.+)'
        ]
        
        import re
        for pattern in task_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 5:  # Meaningful content
                    indicators.append({
                        'title': match.strip()[:100],  # Limit length
                        'confidence': 0.7,
                        'priority': Priority.MEDIUM
                    })
        
        return indicators[:3]  # Limit to 3 tasks per file


class TaskStateManager:
    """Manages task state persistence and updates."""
    
    def __init__(self, output_dir: str = ".ops_out"):
        self.output_dir = Path(output_dir)
        self.tasks_file = self.output_dir / "tasks.json"
        self.activity_log = self.output_dir / "activity.log"
        
        # Ensure output directory exists
        self.output_dir.mkdir(exist_ok=True)
    
    def load_existing_tasks(self) -> Dict[str, Task]:
        """Load existing tasks from JSON file."""
        if not self.tasks_file.exists():
            return {}
        
        try:
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            tasks = {}
            for task_data in data:
                # Convert date strings back to date objects
                if task_data.get('due'):
                    task_data['due'] = datetime.fromisoformat(task_data['due']).date()
                if task_data.get('created'):
                    task_data['created'] = datetime.fromisoformat(task_data['created']).date()
                if task_data.get('last_completed'):
                    task_data['last_completed'] = datetime.fromisoformat(task_data['last_completed']).date()
                
                task = Task(**task_data)
                tasks[task.id] = task
            
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to load existing tasks: {e}")
            return {}
    
    def merge_tasks(self, new_tasks: List[Task], existing_tasks: Dict[str, Task]) -> List[Task]:
        """Merge new tasks with existing ones, preserving state."""
        merged = []
        
        for new_task in new_tasks:
            if new_task.id in existing_tasks:
                existing = existing_tasks[new_task.id]
                
                # Preserve important state
                new_task.status = existing.status
                new_task.notes = existing.notes
                new_task.last_completed = existing.last_completed
                
                # Update title and due date only if source changed
                if set(new_task.source) != set(existing.source):
                    # Source changed, update task
                    self._log_activity("task_updated", {
                        "task_id": new_task.id,
                        "old_source": existing.source,
                        "new_source": new_task.source
                    })
                else:
                    # Keep existing title and due date
                    new_task.title = existing.title
                    new_task.due = existing.due
            
            # Auto-complete tasks based on patterns
            if self._should_auto_complete(new_task):
                new_task.status = TaskStatus.DONE
                new_task.last_completed = date.today()
                self._log_activity("task_auto_completed", {"task_id": new_task.id})
            
            merged.append(new_task)
        
        # Add existing tasks that weren't found in new tasks
        new_task_ids = {task.id for task in new_tasks}
        for task_id, existing_task in existing_tasks.items():
            if task_id not in new_task_ids:
                merged.append(existing_task)
        
        return merged
    
    def save_tasks(self, tasks: List[Task]) -> None:
        """Save tasks to JSON file."""
        try:
            # Convert to serializable format
            task_data = []
            for task in tasks:
                data = task.model_dump()
                
                # Convert date objects to ISO strings
                if data.get('due'):
                    data['due'] = data['due'].isoformat()
                if data.get('created'):
                    data['created'] = data['created'].isoformat()
                if data.get('last_completed'):
                    data['last_completed'] = data['last_completed'].isoformat()
                
                task_data.append(data)
            
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(task_data, f, indent=2, ensure_ascii=False)
            
            self._log_activity("tasks_saved", {"count": len(tasks)})
            
        except Exception as e:
            logger.error(f"Failed to save tasks: {e}")
            raise
    
    def _should_auto_complete(self, task: Task) -> bool:
        """Check if task should be auto-completed based on patterns."""
        for pattern in self.config.auto_complete_patterns if hasattr(self, 'config') else []:
            for source_path in task.source:
                if pattern.lower() in source_path.lower():
                    return True
        return False
    
    def _log_activity(self, action: str, details: Dict = None) -> None:
        """Log activity to the activity log."""
        try:
            entry = ActivityLogEntry(action=action, details=details or {})
            
            with open(self.activity_log, 'a', encoding='utf-8') as f:
                f.write(str(entry) + '\n')
                
        except Exception as e:
            logger.warning(f"Failed to log activity: {e}")
    
    def update_task_status(self, task_id: str, status: TaskStatus, notes: str = "") -> bool:
        """Update a specific task's status."""
        tasks = self.load_existing_tasks()
        
        if task_id not in tasks:
            return False
        
        task = tasks[task_id]
        old_status = task.status
        task.status = status
        
        if notes:
            task.notes = notes
        
        if status == TaskStatus.DONE:
            task.last_completed = date.today()
        
        # Save updated tasks
        all_tasks = list(tasks.values())
        self.save_tasks(all_tasks)
        
        self._log_activity("task_status_updated", {
            "task_id": task_id,
            "old_status": old_status.value,
            "new_status": status.value,
            "notes": notes
        })
        
        return True
