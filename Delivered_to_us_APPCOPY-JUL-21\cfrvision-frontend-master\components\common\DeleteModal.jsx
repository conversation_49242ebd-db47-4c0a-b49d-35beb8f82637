import React from "react";
import { But<PERSON>, Modal } from "react-bootstrap";
import { useForm } from "react-hook-form";
import axiosInstance from "@/utlis/axios";
import Toaster from "./Toaster";

const DeleteModal = ({
  show,
  setShow,
  refetch,
  id,
  apiUrl,
  name,
  onDeleted,
  onDeletedError,
}) => {
  const { handleSubmit } = useForm();

  const { contextHolder, showToast } = Toaster();

  const onSubmit = async (data) => {
    // Perform the delete action here
    // For example, calling an API to delete the file
    try {
      const response = await axiosInstance.post(apiUrl, { id });
      onDeleted(response.data.message || "File deleted successfully");
      refetch();
      setShow(false);
    } catch (error) {
      console.error("Error deleting file", error);
      onDeletedError(error.response.data.message || "Something went wrong");
      setShow(false);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  return (
    <>
      {contextHolder}
      <Modal show={show} onHide={() => setShow(false)} centered size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Delete {name}</Modal.Title>
        </Modal.Header>
        <Modal.Body>Are you sure you want to delete this {name}?</Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShow(false)}>
            Cancel
          </Button>
          {/* Wrap this with form submission */}
          <Button
            type="button"
            variant="danger"
            onClick={handleSubmit(onSubmit)}
          >
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default DeleteModal;
