import axiosInstance from "@/utlis/axios";
import { useEffect, useState } from "react";
import TableWithPagination from "../common/TableWithPagination/page";

const Invoice = ({ userDetails }) => {
  const [loading, setLoading] = useState(false);
  const [invoiceList, setInvoiceList] = useState([]);
  const fetchInvoiceData = async () => {
    setLoading(true);
    try {
      const payload = {
        filters: [
          {
            filter_column: "",
            filter_text: "",
          },
          {
            filter_column: "",
            filter_text: "",
          },
        ],
        id: 1,
        page_no: 1,
        page_size: 10,
        is_pagination: true,
        search_text: "",
      };
      const response = await axiosInstance.post(
        "/v1/profile/invoices/get",
        payload
      );
      setInvoiceList([]);
      setLoading(false);
    } catch (error) {
      console.log("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      headerName: "NAME",
      valueGetter: (p) => p.data.category_name,
      flex: 2,
    },
    {
      headerName: "DESCRIPTION",
      valueGetter: (p) => p.data.category_description,
      flex: 8,
    },

    {
      headerName: "ACTIONS",
      field: "actions",
      valueGetter: (p) => p.data.category_id,
      flex: 2,
      cellRenderer: (params) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
            alignItems: "center",
            marginTop: "15px",
          }}
        >
          <Icon
            icon="mdi-eye"
            width="24"
            height="24"
            cursor={"pointer"}
            onClick={() => {}}
          />
          <Icon
            icon="basil:edit-outline"
            width="24"
            height="24"
            cursor={"pointer"}
            onClick={() => {}}
          />
          <Icon
            icon="mdi:trash-outline"
            width="24"
            height="24"
            color="red"
            cursor={"pointer"}
            onClick={() => {}}
          />
        </div>
      ),
    },
  ];

  useEffect(() => {
    fetchInvoiceData();
  }, []);

  return (
    <>
      <TableWithPagination
        data={invoiceList}
        columns={columns}
        // onPaginationChange={handlePaginationChange}
        loading={loading}
        // onSearchChange={handleSearchChange}
      />
    </>
  );
};

export default Invoice;
