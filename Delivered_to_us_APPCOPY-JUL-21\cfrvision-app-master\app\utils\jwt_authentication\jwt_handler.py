import logging
import os
import time
from datetime import datetime, timedelta

import jwt
import pytz
from dotenv import load_dotenv
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import logger
from app.database import get_db
from app.database.models import User
from app.utils.constants import status_code_message
from app.utils.exception_handling import ExceptionHandling

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Load environment variables from the .env file
load_dotenv()


async def check_auth_tokens_db(access_token: str, user_id: str, cms_login: bool, refresh_token: bool = False,
                               db: AsyncSession = Depends(get_db)):
    if not refresh_token:
        result = await db.execute(
            select(User)
            .filter(User.access_token == access_token, User.is_deleted == False,
                    User.id == user_id)
            .order_by(User.id.asc())
        )
    else:
        result = await db.execute(
            select(User)
            .filter(User.refresh_token == access_token, User.is_deleted == False,
                    User.id == user_id)
            .order_by(User.id.asc())
        )
    user = result.scalars().first()

    if not user:
        return False, 401
    elif cms_login and user.role.value == 'admin':
        return True, 200
    elif cms_login and user.role.value != 'admin':
        return False, 403
    else:
        if user.role.value == 'user':
            current_time = int(time.time())
            if current_time > user.subscription_end or current_time < user.subscription_start:
                return False, 401
        return True, 200


def generate_access_token(user_id: str) -> str:
    """
    Generate an access token for the user.

    **Args**:
        user_id (str): The ID of the user.

    **Returns**:
        str: The generated access token as a binary string.
    """
    try:
        expiration_time = datetime.now(pytz.utc) + timedelta(
            days=int(os.getenv("AUTH_TOKEN_EXP_DAY", 0)),
            minutes=int(os.getenv("AUTH_TOKEN_EXP_MIN", 0))
        )
        access_token_payload = {
            "user_id": user_id,
            "exp": expiration_time,
        }
        access_token = jwt.encode(access_token_payload,
                                  os.getenv("JWT_SECRET_KEY"), algorithm=os.getenv("JWT_ALGORITHM"))
    except Exception as e:
        access_token = ExceptionHandling(e=str(e),
                                         function_name="generate_access_token").exception_handling()
        logger.exception(msg=f"Input: {str(locals())}\nOutput: {str(access_token)} \nException : {str(e)}",
                         exc_info=True)

    return access_token


def generate_refresh_token(user_id: str) -> str:
    """
    Generate a refresh token for the user.

    **Args**:
        user_id (str): The ID of the user.

    **Returns**:
        str: The generated refresh token as a binary string.
    """
    try:
        refresh_token_payload = {
            "user_id": user_id,
            "exp": datetime.now(pytz.utc) + timedelta(
                days=int(os.getenv("REFRESH_AUTH_TOKEN_EXP_DAY", 0)),
                minutes=int(os.getenv("REFRESH_AUTH_TOKEN_EXP_MIN", 0)),
            ),
            "refresh_token": True
        }
        refresh_token = jwt.encode(refresh_token_payload,
                                   os.getenv("JWT_SECRET_KEY"), algorithm=os.getenv("JWT_ALGORITHM"))
    except Exception as e:
        refresh_token = ExceptionHandling(e=str(e),
                                          function_name="generate_refresh_token").exception_handling()
        logger.exception(msg=f"Input: {str(locals())}\nOutput: {str(refresh_token)} \nException : {str(e)}",
                         exc_info=True)

    return refresh_token


def generate_auth_tokens(user_id: str) -> dict:
    """
    Generate both access and refresh tokens for the user.

    **Args**:
        user_id (int): The ID of the user.

    **Returns**:
        dict: A dictionary containing the access and refresh tokens.
    """
    access_token = generate_access_token(user_id=user_id)
    refresh_token = generate_refresh_token(user_id=user_id)

    return {"access_token": access_token, "refresh_token": refresh_token}


async def regenerate_access_token(user_id: str, token: str,
                                  db: AsyncSession = Depends(get_db)) -> dict | None:
    """
    Regenerate access and refresh tokens based on the provided authorization header.

    **Args**:
        authorization_header (str): The authorization header containing the current token.

    **Returns**:
        dict: A dictionary containing the new access and refresh tokens.
    """
    try:
        result = await db.execute(
            select(User)
            .filter(User.refresh_token == token,
                    User.is_deleted == False,
                    User.id == user_id)
            .order_by(User.id.asc())
        )
        auth_token = result.scalars().first()
        if auth_token:
            access_token = generate_auth_tokens(user_id=user_id)
            auth_token.refresh_token = access_token.get("refresh_token")
            auth_token.access_token = access_token.get("access_token")
            await db.commit()
        else:
            return None

    except Exception as e:
        ExceptionHandling(e=str(e),
                          function_name=' function: regenerate_access_token').exception_handling()
        access_token = None
    return access_token


def decode_token(access_token: str) -> dict:
    """
    Decode the provided access token.

    **Args**:
        access_token (str): The access token to decode.

    **Returns**:
        dict: The decoded token payload or None if an error occurs.
    """
    try:
        payload = jwt.decode(access_token, os.getenv("JWT_SECRET_KEY"),
                             algorithms=[os.getenv("JWT_ALGORITHM")], options={"verify_exp": False})
    except Exception as e:

        error = ExceptionHandling(e=str(e),
                                  function_name="decode_token").exception_handling()
        logger.exception(msg=f"Input: {str(locals())}\nOutput: {str(error)} \nException : {str(e)}", exc_info=True)

        payload = None

    return payload


class JWTBearer(HTTPBearer):
    """
    A custom HTTPBearer authentication class that verifies JWT tokens.
    """

    def __init__(self, auto_error: bool = True):
        """
        Initialize the JWTBearer instance.

        **Args**:
            auto_error (bool): Whether to automatically raise an error if authentication fails. Defaults to True.
        """
        super(JWTBearer, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request, db: AsyncSession = Depends(get_db)):
        """
        Handle the request and verify the provided JWT token.

        **Args**:
            request (Request): The incoming request.

        **Returns**:
            str: The verified JWT token.

        Raises:
            HTTPException: If the authentication scheme is invalid or the token verification fails.
        """
        token_regeneration = False
        cms_login = False
        if '/cms' in request.scope.get('path', ''):
            cms_login = True
        elif '/refresh' in request.scope.get('path', ''):
            token_regeneration = True
        credentials: HTTPAuthorizationCredentials = await super(JWTBearer, self).__call__(request)
        if credentials:
            if credentials.scheme != "Bearer":
                raise HTTPException(status_code=400, detail="Invalid authentication scheme.")
            jwt_status = self.verify_jwt(credentials.credentials)
            if not jwt_status.get("status"):
                raise HTTPException(status_code=401, detail=jwt_status.get("error"))
            user_id = jwt_status.get("user_id")
            if user_id:
                refresh_token = jwt_status.get('refresh_token', False)
                user_state, status_code = await check_auth_tokens_db(credentials.credentials, user_id, cms_login,
                                                jwt_status.get('refresh_token', False), db)
                if refresh_token and not token_regeneration:
                    raise HTTPException(status_code=401, detail=status_code_message[401])
                elif user_state and status_code == 200 and token_regeneration:
                    jwt_status.update({'token': credentials.credentials})
                    return jwt_status
                elif user_state and status_code == 200:
                    return jwt_status
                else:
                    raise HTTPException(status_code=status_code, detail=status_code_message[status_code])

            return jwt_status
        else:
            raise HTTPException(status_code=401, detail=status_code_message[401])

    def verify_jwt(self, access_token: str) -> dict:
        """
        Verify the provided JWT token.

        **Args**:
            access_token (str): The JWT token to verify.

        **Returns**:
            dict: A dictionary indicating the verification status and any error messages.
        """
        try:
            decode_info = jwt.decode(access_token, os.getenv("JWT_SECRET_KEY"),
                                     algorithms=[os.getenv("JWT_ALGORITHM")])
        except jwt.ExpiredSignatureError:
            return {"error": "AuthenticationFailed : Access Token expired", "status": False}
        except jwt.InvalidSignatureError:
            return {"error": "AuthenticationFailed : Access Token Invalid", "status": False}
        except Exception as e:
            logging.exception(msg=str(e), exc_info=True)
            return {"error": "AuthenticationFailed : Access Token Invalid", "status": False}

        return {"error": '', "status": True, "user_id": decode_info['user_id'],
                "refresh_token": decode_info.get('refresh_token', False)}


def verify_password(password: str, hashed_password: str):
    try:
        return pwd_context.verify(password, hashed_password)
    except Exception as e:
        logger.exception(msg=f"Input: {str(locals())}\nException : {str(e)}", exc_info=True)
        error = ExceptionHandling(e=str(e), function_name="verify_password").exception_handling()
        raise

def get_password_hash(password: str):
    return pwd_context.hash(password)
