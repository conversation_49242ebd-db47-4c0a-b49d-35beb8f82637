"""
Data models for the Startup Ops Indexer system.
"""
from datetime import datetime, date
from typing import List, Optional, Dict, Any, Literal
from enum import Enum
from pydantic import BaseModel, Field
import hashlib
import json


class TaskType(str, Enum):
    PROCESS = "process"
    OPPORTUNITY = "opportunity"
    ONEOFF = "oneoff"


class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    DONE = "done"
    SNOOZED = "snoozed"


class Priority(str, Enum):
    HIGH = "high"
    MEDIUM = "med"
    LOW = "low"


class Recurrence(str, Enum):
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    BIWEEKLY = "BIWEEKLY"
    MONTHLY = "MONTHLY"


class Task(BaseModel):
    id: str = Field(..., description="SHA1 hash of path or stable UUID")
    title: str
    type: TaskType
    status: TaskStatus = TaskStatus.PENDING
    priority: Priority = Priority.MEDIUM
    due: Optional[date] = None
    created: date = Field(default_factory=lambda: datetime.now().date())
    source: List[str] = Field(default_factory=list, description="Source file paths")
    notes: str = ""
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)
    recurrence: Optional[Recurrence] = None
    last_completed: Optional[date] = None
    tags: List[str] = Field(default_factory=list)
    
    @classmethod
    def generate_id(cls, title: str, source_paths: List[str]) -> str:
        """Generate a stable ID based on title and source paths."""
        content = f"{title}:{':'.join(sorted(source_paths))}"
        return hashlib.sha1(content.encode()).hexdigest()[:16]


class FileInfo(BaseModel):
    path: str
    size: int
    created: datetime
    modified: datetime
    extension: str
    folder: str
    content_hash: Optional[str] = None
    extracted_text: str = ""
    description: str = ""
    topics: List[str] = Field(default_factory=list)
    confidence: float = 1.0


class ProcessCandidate(BaseModel):
    name: str
    source_hint: str
    cadence: Optional[Recurrence]
    next_due: Optional[date]
    evidence_paths: List[str]
    confidence: float = 1.0


class OpportunityCandidate(BaseModel):
    title: str
    why_it_matters: str
    suggested_next_step: str
    evidence_paths: List[str]
    confidence: float = 1.0


class CadenceRule(BaseModel):
    match: Optional[List[str]] = None
    match_folders: Optional[List[str]] = None
    recurrence: Optional[Recurrence] = None
    rule: str
    followup_after_days: Optional[int] = None
    due_after_days: Optional[int] = None


class Config(BaseModel):
    timezone: str = "America/New_York"
    cadences: Dict[str, CadenceRule] = Field(default_factory=dict)
    opportunity_hints: List[str] = Field(default_factory=list)
    auto_complete_patterns: List[str] = Field(default_factory=list)
    ignore_patterns: List[str] = Field(default_factory=list)


class ActivityLogEntry(BaseModel):
    timestamp: datetime = Field(default_factory=datetime.now)
    action: str
    details: Dict[str, Any] = Field(default_factory=dict)
    
    def __str__(self) -> str:
        return f"{self.timestamp.isoformat()} - {self.action}: {json.dumps(self.details)}"
