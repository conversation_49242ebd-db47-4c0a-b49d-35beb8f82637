import React, { useEffect, useState } from "react";
import { Button, Col, Modal, Row } from "react-bootstrap";
import { TextEditor } from "../UI/TextEditor";

const StaticPageModal = ({ show, setShow, staticPageData, onSave }) => {
  const [data, setData] = useState(staticPageData);
  useEffect(() => {}, [staticPageData, show]);

  const onSubmit = async () => {
    try {
      await onSave(data);
      setShow(false); // Close the modal after saving
    } catch (error) {
      console.log("Error saving category:", error);
      showToast({
        type: "error",
        message: error.message,
      });
    }
  };

  const handleChange = (e) => {
    setData((prev) => ({ ...prev, content: e }));
  };

  return (
    <Modal show={show} onHide={() => setShow(false)} centered size="lg">
      <Modal.Header closeButton>
        <Modal.Title className="text-center">Update Static Page</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        <Row>
          <Col md={12}>
            <div className="form-group">
              <label htmlFor="notes">Notes</label>
              <TextEditor
                rows={5}
                name="content"
                values={data?.content}
                onChange={handleChange}
                id="content"
                className="input-md round form-control"
              />
            </div>
          </Col>
        </Row>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={() => setShow(false)}>
          Cancel
        </Button>
        <Button type="submit" variant="primary" onClick={onSubmit}>
          Update
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default StaticPageModal;
