from app.schemas import SchemaApiResponse

response_format = {
    200: {
        "model": SchemaApiResponse,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error": "",
                    "message": "Dynamic Message",
                    "status": True
                }
            }
        },
    }, 422: {
        "model": SchemaApiResponse,
        "description": "Missing data",
        "content": {
            "application/json": {
                "example": {
                    "data": "",
                    "error": [{"type": "", "loc": [], "msg": "", "input": "", "ctx": {"error": {}}}],
                    "message": "", "status": False}},
        }},
    500: {
        "model": SchemaApiResponse,
        "description": "Internal error",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error": {"class-method": "api: 'Api in which error is raised'",
                              "exception_msg": "Exception Message"},
                    "message": "We are having issue for this request, please try again.",
                    "status": False
                }
            }
        },

    }
}


checks_categories_fetched = "AI Checks and Categories retrieved successfully."
retrieve_checks_failed = "Failed to retrieve AI Checks."
check_name_exists = "The AI Check name is not allowed as it already exists."
add_check_success = "AI Check added successfully."
add_check_failed = "Failed to create AI Check."
check_not_found = "AI Check not found."
check_deactivated = "AI Check deactivated successfully."
check_update_success = "AI Check updated successfully."
check_updated_failed = "Failed to update AI Check."
check_id_required = "Check ID is required for update."
check_delete_success = "AI Check deleted successfully."
check_delete_rejected = "AI Check cannot be deleted."
check_delete_failed = "Failed to delete AI Check."
categories_fetched_success = "AI Checks Categories retrieved successfully."
categories_fetched_failed = "Failed to retrieve AI Checks Categories."
category_name_exists = "The AI Check Category name is not allowed as it already exists."
add_category_success = "AI Check Category added successfully!!"
add_category_failed = "Failed to add AI Check Category."
update_category_success = "Category updated successfully!!."
update_category_failed = "Failed to add AI Check Category."
category_not_found = "AI Check Category not found!"
dependent_llm_check_exists = "Dependent Llm Check Request exist."
dependent_ai_check_exists = "Dependent AI Check exist."
category_delete_success = "AI Check Category deleted successfully!"
category_delete_rejected = "Category cannot be deleted."
category_delete_failed = "Failed to delete Category."
api_key_not_configure = "API Keys not configured."
api_keys_fetch_success = "Here are your API Keys."
api_keys_fetch_fail = "Failed to get API Keys."
api_keys_updated = "OpenAI configuration updated successfully!"
file_upload_success = "File upload started. Processing..."
file_upload_failed = "File upload failed."
file_delete_success = "File and related records deleted successfully."
file_delete_failed = "Error deleting file from the system."
file_not_found = "File not found."
llm_check_exists_for_file = "There is LLM Check Request for the file."
exception_occurred = "We could not proceed due to some issue, Please try again later."
db_error = "Database error occurred. : "
llm_checks_fetched_success = "LLM Check Request fetched successfully!"
llm_checks_fetched_failed = "Failed to fetch LLM Check Request."
llm_checks_exists = "The LLM Check Request is not allowed as it already exists."
llm_check_not_found = "LLM Check Request not found!"
llm_check_request_add_failed = "Failed to run llm check request"
add_llm_checks_success = "LLM Check Request added successfully."
add_llm_checks_fail_try_again = "File processing is going on. Try adding LLM Check Request in a while once file processing is complete."
add_llm_checks_fail_file_issue = "File processing failed. Try adding new wile and then add LLM Check Request."
delete_llm_checks_success = "LLM Check Request deleted successfully."
delete_llm_checks_failed = "Failed to delete LLM Check Request successfully."
profile_details_fetched = "Profile details retrieved successfully."
profile_details_fetch_failed = "Failed to retrieve profile details."
card_details_fetched = "Card details retrieved successfully."
card_details_fetch_failed = "Failed to retrieve card details."
invoice_details_fetched = "Invoice details retrieved successfully."
invoice_details_fetch_failed = "Failed to retrieve invoice details."
invoice_not_found = "Invoice not found!"
profile_updated_success = "Profile updated successfully."
profile_updated_failed = "Failed to update profile."
billing_address_update_success = "Billing address updated successfully."
billing_address_update_failed = "Failed to update billing address."
subscription_not_found = "No active subscriptions found!."
subscription_cancel_success = "Subscription cancelled successfully."
subscription_cancel_failed = "Failed to cancel subscription."
new_card_added = "Card added successfully!"
new_card_addition_failed = "Failed to add the card!"
new_card_removed = "Card removed successfully!"
new_card_remove_failed = "Failed to remove the card!"
card_not_found="Card not found"
user_email_not_found = "Incorrect credentials. Try checking your email."
user_incorrect_password = "Invalid credentials. Check your email and password."
login_success = "User login successful!"
invalid_refresh_token_error="Invalid Refresh Token!"
refresh_token_success="Refresh Token creation success!"
user_not_found = "We couldn’t find your account. Please sign up first."
reset_pass_link_sent_success = "We’ve sent the link to update password to your email. Check your mail!"
token_expired = 'Invalid or expired link'
reset_pass_link_verification_success = "Link verified. You can now reset your password."
password_updated = "Password updated successfully."
password_invalid = "New Password can not be the same as old password."
password_incorrect = "Old Password is incorrect. Please try again."
user_logout_success = "User logged out successfully!"
user_logout_fail = "Failed to logout!"
add_contact_us = "Thank you for reaching out! We've received your message and will get back to you shortly."
contact_us_list_fetched_success = "Contact Us inquiries fetched successfully!"
contact_us_list_fetched_fail = "Failed to fetch Contact Us inquiries!"
contact_us_not_found = "Contact Inquiry not found."
contact_us_updated = "Successfully updated status of the inquiry."
static_page_not_found = "Static Page not found!"
static_page_fetched_successfully = "Static Page(s) fetched successfully!"
static_page_updated_successfully = "Static Page(s) updated successfully!"
fail_llm_check_response_html="""<table><tr><th>Result</th></tr><tr><td>Something went wrong. If the issue persists, please contact admin.</td></tr></table>"""

status_code_message = {
    401:"Invalid authorization code.",
    403:"Insufficient Permissions."
}

